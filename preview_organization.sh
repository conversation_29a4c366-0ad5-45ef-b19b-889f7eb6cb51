#!/bin/bash

# 项目文件整理预览脚本
# 显示将要进行的整理操作，不实际执行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_ROOT="/var/www/tempmail"

echo -e "${BLUE}📋 项目文件整理预览${NC}"
echo "=================================================="

cd "$PROJECT_ROOT"

echo ""
echo -e "${BLUE}📁 将要创建的目录结构:${NC}"
echo "  scripts/"
echo "    ├── diagnostics/     # 诊断脚本"
echo "    ├── maintenance/     # 维护脚本"
echo "    └── deployment/      # 部署脚本"
echo "  config/"
echo "    ├── systemd/         # systemd服务配置"
echo "    └── templates/       # 配置模板"
echo "  backup/"
echo "    ├── database/        # 数据库备份"
echo "    └── configs/         # 配置备份"

echo ""
echo -e "${BLUE}🔧 脚本文件整理计划:${NC}"

# 诊断脚本
echo ""
echo "📊 诊断脚本 → scripts/diagnostics/:"
for file in diagnose_permissions.sh diagnose_service_failure.sh check_permissions.sh final_verification.sh; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (不存在)"
    fi
done

# 维护脚本
echo ""
echo "🔧 维护脚本 → scripts/maintenance/:"
for file in fix_permissions.sh fix_monitoring_permissions.sh fix_service_issues.sh verify_optimizations.py optimize_sqlite.py; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (不存在)"
    fi
done

# 快速修复脚本（软链接）
echo ""
echo "⚡ 快速修复脚本 (移动到maintenance/，根目录保留软链接):"
for file in quick_fix_permissions.sh quick_fix_service.sh; do
    if [ -f "$file" ]; then
        echo "  ✅ $file → scripts/maintenance/ (软链接保留)"
    else
        echo "  ❌ $file (不存在)"
    fi
done

# 部署脚本
echo ""
echo "🚀 部署脚本 → scripts/deployment/:"
for file in start_production.sh; do
    if [ -f "$file" ]; then
        echo "  ✅ $file (软链接保留)"
    else
        echo "  ❌ $file (不存在)"
    fi
done

echo ""
echo -e "${BLUE}⚙️ 配置文件整理计划:${NC}"

# 服务配置文件
echo ""
echo "🔧 systemd服务配置 → config/systemd/:"
echo "  保留最终版本: tempmail-minimal.service → tempmail-optimized.service"
echo "  备份其他版本:"
for file in tempmail-optimized.service tempmail-optimized-simple.service; do
    if [ -f "$file" ]; then
        echo "    ✅ $file → backup/configs/"
    else
        echo "    ❌ $file (不存在)"
    fi
done

# 其他配置
echo ""
echo "📄 其他配置文件:"
if [ -f "tempmail-logrotate.conf" ]; then
    echo "  ✅ tempmail-logrotate.conf → config/"
else
    echo "  ❌ tempmail-logrotate.conf (不存在)"
fi

if [ -f "monitoring_config.env.example" ]; then
    echo "  ✅ monitoring_config.env.example → config/templates/"
else
    echo "  ❌ monitoring_config.env.example (不存在)"
fi

echo ""
echo -e "${BLUE}📄 文档文件整理计划:${NC}"

# 文档文件
doc_files=(
    "MONITORING_PERMISSIONS_FIXED.md"
    "MONITORING_PERMISSIONS_SOLUTION.md"
    "OPTIMIZATION_IMPLEMENTATION_SUMMARY.md"
    "PRODUCTION_SERVICE_DEPLOYMENT_GUIDE.md"
    "SERVICE_DEPLOYMENT_SUCCESS_REPORT.md"
)

echo ""
echo "📚 文档文件 → docs/:"
for file in "${doc_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (不存在)"
    fi
done

echo ""
echo "📖 保留在根目录:"
if [ -f "README.md" ]; then
    echo "  ✅ README.md"
else
    echo "  ❌ README.md (不存在)"
fi

echo ""
echo -e "${BLUE}🗄️ 备份文件整理计划:${NC}"

echo ""
echo "💾 数据库备份 → backup/database/:"
if [ -f "database/tempmail.db.backup_20250611_150526" ]; then
    echo "  ✅ tempmail.db.backup_20250611_150526"
else
    echo "  ❌ 无数据库备份文件"
fi

echo ""
echo "⚙️ 配置备份 → backup/configs/:"
if [ -f "git_permissions_backup.acl" ]; then
    echo "  ✅ git_permissions_backup.acl"
else
    echo "  ❌ git_permissions_backup.acl (不存在)"
fi

echo ""
echo -e "${BLUE}🧹 临时文件清理计划:${NC}"

# 验证和结果文件
temp_files=(
    "optimization_verification_results.json"
    "internal_mail_demo_results.json"
    "demo_internal_mail_monitoring.py"
    "demo_monitoring.py"
    "verify_monitoring_fix.py"
)

echo ""
echo "🗑️ 移动到backup/或scripts/:"
for file in "${temp_files[@]}"; do
    if [ -f "$file" ]; then
        if [[ "$file" == *.json ]]; then
            echo "  ✅ $file → backup/"
        else
            echo "  ✅ $file → scripts/diagnostics/"
        fi
    else
        echo "  ❌ $file (不存在)"
    fi
done

echo ""
echo -e "${BLUE}🔗 软链接创建计划:${NC}"
echo ""
echo "将在根目录创建以下软链接，保持向后兼容:"
echo "  • tempmail-optimized.service → config/systemd/tempmail-optimized.service"
echo "  • start_production.sh → scripts/deployment/start_production.sh"
echo "  • quick_fix_permissions.sh → scripts/maintenance/quick_fix_permissions.sh"
echo "  • quick_fix_service.sh → scripts/maintenance/quick_fix_service.sh"

echo ""
echo -e "${BLUE}📋 保留在根目录的重要文件:${NC}"
echo ""
echo "以下文件将保留在根目录:"
important_files=(
    "app.py"
    "gunicorn.conf.py"
    "manage_tempmail_service.sh"
    "README.md"
    "requirements.txt"
    "package.json"
)

for file in "${important_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (不存在)"
    fi
done

echo ""
echo "=================================================="
echo -e "${YELLOW}⚠️  注意事项:${NC}"
echo ""
echo "1. 整理过程不会影响正在运行的服务"
echo "2. 重要脚本会创建软链接保持向后兼容"
echo "3. 所有移动的文件都会保留在项目内"
echo "4. 备份文件会被妥善保存"
echo "5. 整理后需要验证脚本路径引用"
echo ""
echo -e "${GREEN}✅ 如果预览结果满意，请运行:${NC}"
echo "   ./organize_project_files.sh"
echo ""
echo -e "${BLUE}📊 当前根目录文件统计:${NC}"
echo "  脚本文件: $(ls -1 *.sh 2>/dev/null | wc -l)"
echo "  配置文件: $(ls -1 *.service *.conf 2>/dev/null | wc -l)"
echo "  文档文件: $(ls -1 *.md 2>/dev/null | wc -l)"
echo "  Python文件: $(ls -1 *.py 2>/dev/null | grep -v app.py | wc -l)"
echo "  JSON文件: $(ls -1 *.json 2>/dev/null | grep -v package | wc -l)"
