# Gunicorn 生产环境配置文件
# 使用方法: gunicorn -c gunicorn.conf.py app:app

import os
import multiprocessing

# 服务器套接字配置
bind = "0.0.0.0:8080"
backlog = 2048

# 工作进程配置
workers = 2  # 推荐 CPU 核心数 * 2 + 1，但对于轻量级应用2个足够
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# 最大请求数（防止内存泄漏）
max_requests = 1000
max_requests_jitter = 50

# 进程管理
preload_app = True
daemon = False
pidfile = "/var/www/tempmail/gunicorn.pid"

# 日志配置
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# 性能优化
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统（如果可用）

# 环境变量
raw_env = [
    'FLASK_ENV=production',
    'FLASK_DEBUG=0'
]

# 启动时的钩子函数
def on_starting(server):
    """服务器启动时执行"""
    server.log.info("Gunicorn 服务器正在启动...")

def on_reload(server):
    """重新加载时执行"""
    server.log.info("Gunicorn 服务器正在重新加载...")

def worker_int(worker):
    """工作进程中断时执行"""
    worker.log.info("工作进程 %s 被中断", worker.pid)

def pre_fork(server, worker):
    """工作进程分叉前执行"""
    server.log.info("工作进程 %s 即将启动", worker.pid)

def post_fork(server, worker):
    """工作进程分叉后执行"""
    server.log.info("工作进程 %s 已启动", worker.pid)

def worker_abort(worker):
    """工作进程异常终止时执行"""
    worker.log.info("工作进程 %s 异常终止", worker.pid)
