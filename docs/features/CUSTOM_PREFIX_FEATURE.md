# Custom Email Prefix Feature Documentation

## Overview

The custom email prefix feature allows users to specify a custom prefix for their temporary email addresses instead of using randomly generated prefixes. This feature was added to provide users with more memorable and recognizable email addresses.

## Feature Description

### New Button
- **Location**: Between "New Email" and "Delete & Reset" buttons
- **Appearance**: Purple button with edit icon
- **Label**: "Custom"

### Functionality
- Prompts user for a custom prefix via a dialog box
- Validates the prefix according to specified rules
- Generates email address using the custom prefix
- Falls back to random suffix if prefix collision occurs

## API Changes

### Endpoint: `/api/generate-address`
**Method**: POST

**Request Body** (JSON):
```json
{
  "custom_prefix": "myprefix"  // Optional parameter
}
```

**Response** (Success):
```json
{
  "success": true,
  "data": {
    "address": "<EMAIL>",
    "expires_at": "2025-05-28T19:07:35.843783+00:00"
  },
  "error": null
}
```

**Response** (Validation Error):
```json
{
  "success": false,
  "data": null,
  "error": "自定义前缀只能包含字母、数字和连字符，长度1-20字符"
}
```

## Validation Rules

### Custom Prefix Requirements
- **Characters**: Only alphanumeric characters (a-z, A-Z, 0-9) and hyphens (-)
- **Length**: 1-20 characters
- **Pattern**: `^[a-zA-Z0-9\-]{1,20}$`

### Invalid Examples
- `test@email` (contains @)
- `test.com` (contains .)
- `test space` (contains space)
- `test_underscore` (contains underscore)
- `` (empty string)
- `verylongprefixthatisinvalid` (too long)

### Valid Examples
- `mytest`
- `test123`
- `my-test`
- `a`
- `user-123-abc`

## Collision Handling

When a custom prefix already exists in the database:

1. **First attempt**: Use exact custom prefix
   - Input: `mytest`
   - Output: `<EMAIL>`

2. **Subsequent attempts**: Add random suffix
   - Input: `mytest` (collision detected)
   - Output: `<EMAIL>`

## Frontend Implementation

### HTML Changes
```html
<button id="customEmailBtn" class="btn btn-custom">
    <i class="icon-edit"></i> Custom
</button>
```

### CSS Styling
```css
.btn-custom {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    /* Additional purple styling */
}
```

### JavaScript Logic
1. **Event Listener**: Attached to custom button
2. **User Input**: Prompt dialog for prefix input
3. **Validation**: Client-side validation before API call
4. **API Call**: Send request with custom prefix
5. **UI Updates**: Update display with new email address

## Testing

### Manual Testing
- ✅ Valid custom prefix generates correct email
- ✅ Invalid prefix shows validation error
- ✅ Default behavior (no prefix) still works
- ✅ Collision handling generates unique addresses
- ✅ UI interactions work correctly

### API Testing Examples
```bash
# Valid custom prefix
curl -X POST http://localhost:5001/api/generate-address \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "mytest"}'

# Invalid custom prefix
curl -X POST http://localhost:5001/api/generate-address \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "invalid@prefix"}'

# Default behavior (no prefix)
curl -X POST http://localhost:5001/api/generate-address \
  -H "Content-Type: application/json" \
  -d '{}'
```

## Files Modified

1. **Backend**: `/app.py`
   - Modified `/api/generate-address` endpoint
   - Added custom prefix parameter handling
   - Implemented validation logic
   - Added collision detection and handling

2. **Frontend API**: `/static/js/api-config.js`
   - Updated `generateAddress()` function
   - Added custom prefix parameter support

3. **Frontend HTML**: `/templates/index.html`
   - Added custom button with purple styling
   - Positioned between existing buttons

4. **Frontend JavaScript**: `/static/js/main.js`
   - Added custom button event listener
   - Implemented user input validation
   - Added API call logic with custom prefix

5. **Routes**: Added `/test-custom` route for testing

## Usage Instructions

1. **Access Application**: Navigate to the temporary email application
2. **Click Custom Button**: Located between "New Email" and "Delete & Reset"
3. **Enter Prefix**: Type desired prefix in the prompt dialog
4. **Validation**: Prefix will be validated (1-20 chars, alphanumeric + hyphens only)
5. **Email Generation**: New email address will be created with custom prefix
6. **Collision Handling**: If prefix exists, random suffix will be added automatically

## Security Considerations

- Input validation prevents injection attacks
- Regex pattern ensures only safe characters
- Length limits prevent abuse
- Collision handling maintains uniqueness
- Same rate limiting applies as standard email generation

## Future Enhancements

Potential improvements for future versions:
- Custom domain selection
- Prefix history/favorites
- Bulk prefix generation
- Advanced collision resolution strategies
- Prefix availability checking
