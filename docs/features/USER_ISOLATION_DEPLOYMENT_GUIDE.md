# 用户邮箱隔离功能部署指南

## 📋 概述

本指南说明如何在生产环境中部署用户邮箱隔离功能。由于您的生产环境还未正式上线，我们推荐使用**完全重新初始化**的方式，这是最安全和简洁的方法。

## 🎯 功能特性

### ✅ 已实现的用户隔离功能

1. **独立邮箱命名空间**：每个用户（基于session_id）拥有独立的邮箱地址空间
2. **地址复用**：不同用户可以创建相同的邮箱地址（如 <EMAIL> 和 <EMAIL> 都可以有 <EMAIL>）
3. **冲突检测**：在用户范围内检查邮箱地址冲突，而非全局检查
4. **个性化建议**：邮箱建议生成基于当前用户的已有邮箱，而非全局邮箱
5. **历史记录隔离**：每个用户只能看到自己的邮箱历史记录

### 🔧 技术实现

- **数据库表结构**：为 `temporary_emails` 表添加了 `session_id` 字段
- **API 更新**：所有邮箱相关 API 都支持基于 session_id 的用户隔离
- **向后兼容**：支持没有 session_id 的旧版本客户端

## 🚀 推荐部署方案：完全重新初始化

### 为什么选择这种方案？

1. **无数据风险**：生产环境未上线，没有需要保留的数据
2. **代码简洁**：避免复杂的数据库迁移逻辑
3. **性能最优**：新表结构从一开始就是优化的
4. **避免问题**：消除迁移过程中可能出现的边缘情况

### 📝 部署步骤

#### 1. 备份当前代码（可选）
```bash
# 如果需要回滚，可以先备份
cp -r /path/to/your/app /path/to/backup/
```

#### 2. 停止应用服务
```bash
# 停止当前运行的应用
sudo systemctl stop your-app-service
# 或者如果使用 PM2
pm2 stop your-app
```

#### 3. 删除现有数据库
```bash
cd /path/to/your/app
rm -f database/tempmail.db*
```

#### 4. 更新应用代码
```bash
# 拉取最新代码或复制新的 app.py 文件
git pull origin main
# 或者
cp /path/to/new/app.py ./
```

#### 5. 重新启动应用
```bash
# 启动应用，系统会自动创建新的数据库结构
sudo systemctl start your-app-service
# 或者
pm2 start your-app
```

#### 6. 验证功能
```bash
# 运行测试脚本验证用户隔离功能
python3 test_user_isolation.py
```

### 🔍 验证清单

部署完成后，请验证以下功能：

- [ ] 应用正常启动，无错误日志
- [ ] 数据库表结构包含 `session_id` 字段
- [ ] 不同用户可以创建相同的邮箱地址
- [ ] 同一用户不能创建重复的邮箱地址
- [ ] 邮箱建议功能正常工作
- [ ] 历史记录功能正常工作

### 📊 数据库表结构

新的 `temporary_emails` 表结构：

```sql
CREATE TABLE temporary_emails (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    address TEXT NOT NULL,
    session_id TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_temporary_emails_address ON temporary_emails (address);
CREATE INDEX idx_temporary_emails_session_id ON temporary_emails (session_id);
CREATE INDEX idx_temporary_emails_expires_at ON temporary_emails (expires_at);
```

## 🧪 测试验证

### 自动化测试

运行提供的测试脚本：

```bash
python3 test_user_isolation.py
```

### 手动测试

1. **创建邮箱测试**：
   - 用户A创建 `<EMAIL>`
   - 用户B创建 `<EMAIL>`
   - 两者都应该成功

2. **冲突检测测试**：
   - 用户A再次尝试创建 `<EMAIL>`
   - 应该失败并返回建议地址

3. **建议生成测试**：
   - 验证建议地址基于当前用户的邮箱，而非全局

## 🔒 安全考虑

1. **会话管理**：确保 session_id 的生成是安全和唯一的
2. **数据隔离**：验证用户无法访问其他用户的邮箱数据
3. **API 安全**：确保所有 API 调用都包含正确的 session_id

## 📞 故障排除

### 常见问题

1. **数据库初始化失败**
   - 检查数据库目录权限
   - 确保 SQLite 可以写入文件

2. **用户隔离不工作**
   - 检查前端是否正确传递 session_id
   - 验证 session_id 的生成逻辑

3. **性能问题**
   - 确保数据库索引已正确创建
   - 监控数据库查询性能

### 日志检查

检查应用日志中的关键信息：
- 数据库初始化成功消息
- 邮箱创建和冲突检测日志
- 任何错误或警告信息

## 📈 监控建议

部署后建议监控以下指标：

1. **功能指标**：
   - 邮箱创建成功率
   - 用户隔离功能正确性
   - API 响应时间

2. **技术指标**：
   - 数据库性能
   - 内存使用情况
   - 错误日志频率

## 🎉 总结

通过完全重新初始化的方式部署用户邮箱隔离功能是最安全和高效的选择。这种方法避免了复杂的数据迁移，确保了系统的稳定性和性能。

部署完成后，您的临时邮箱系统将支持真正的多用户隔离，每个用户都拥有独立的邮箱命名空间，大大提升了用户体验和系统的可扩展性。
