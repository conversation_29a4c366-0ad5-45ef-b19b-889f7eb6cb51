# API配置系统使用指南

## 概述

我们已经将原来硬编码在JavaScript中的API端点重构为一个可配置的API管理系统。这个改进解决了以下问题：

1. **硬编码API端点** - 现在所有API端点都通过配置管理
2. **缺少错误处理** - 添加了重试机制和完善的错误处理
3. **不支持不同环境配置** - 现在支持开发、测试、生产环境的不同配置

## 新增文件

### 1. `/static/js/api-config.js`
API配置管理模块，提供以下功能：
- 统一的API端点管理
- 自动重试机制
- 超时控制
- 错误处理
- 环境配置支持

### 2. `.env.api.example`
API配置环境变量示例文件，包含完整的API配置选项。

## 配置选项

### 环境变量

在 `.env` 文件中添加以下配置：

```bash
# API基础URL（空值表示使用相对路径）
API_BASE_URL=

# API请求超时时间（毫秒）
API_TIMEOUT=10000

# API重试次数
API_RETRY_ATTEMPTS=3

# API重试延迟（毫秒）
API_RETRY_DELAY=1000
```

### 不同环境的配置示例

#### 开发环境
```bash
# 使用本地不同端口的API服务器
API_BASE_URL=http://localhost:5001
API_TIMEOUT=5000
API_RETRY_ATTEMPTS=1
```

#### 生产环境
```bash
# 使用相对路径（推荐）
API_BASE_URL=
API_TIMEOUT=15000
API_RETRY_ATTEMPTS=5
API_RETRY_DELAY=2000
```

#### 测试环境
```bash
# 指向测试服务器
API_BASE_URL=https://api-test.tempmail.com
API_TIMEOUT=8000
API_RETRY_ATTEMPTS=2
```

## API端点配置

所有API端点现在都在 `APIConfig` 类中统一管理：

```javascript
endpoints: {
    generateAddress: '/api/generate-address',
    getEmails: '/api/emails',
    getEmailContent: '/api/email',
    deleteEmail: '/api/delete-email'
}
```

## 功能特性

### 1. 自动重试机制
- 网络错误或5xx错误时自动重试
- 可配置重试次数和延迟
- 递增延迟（第一次重试1秒，第二次2秒，以此类推）

### 2. 超时控制
- 可配置的请求超时时间
- 防止请求无限期挂起

### 3. 错误处理
- 网络错误的友好提示
- 根据HTTP状态码提供相应的错误信息
- 详细的错误日志记录

### 4. 配置热加载
- 从服务器配置动态加载API设置
- 支持运行时配置更新

## 使用方法

### 在JavaScript中使用

```javascript
// 生成新邮箱地址
try {
    const result = await window.apiConfig.generateAddress();
    if (result.success) {
        console.log('新邮箱:', result.data.address);
    }
} catch (error) {
    console.error('生成邮箱失败:', error.message);
}

// 获取邮件列表
try {
    const result = await window.apiConfig.getEmails(emailAddress, lastReceived);
    if (result.success) {
        console.log('邮件列表:', result.data.emails);
    }
} catch (error) {
    console.error('获取邮件失败:', error.message);
}

// 获取邮件详情
try {
    const result = await window.apiConfig.getEmailContent(emailId, emailAddress);
    if (result.success) {
        console.log('邮件内容:', result.data);
    }
} catch (error) {
    console.error('获取邮件内容失败:', error.message);
}

// 删除邮箱
try {
    const result = await window.apiConfig.deleteEmail(emailAddress);
    if (result.success) {
        console.log('邮箱删除成功');
    }
} catch (error) {
    console.error('删除邮箱失败:', error.message);
}
```

### 动态更新配置

```javascript
// 更新API配置
window.apiConfig.updateConfig({
    baseUrl: 'https://new-api.tempmail.com',
    timeout: 20000,
    retryAttempts: 5
});

// 获取当前配置
const currentConfig = window.apiConfig.getConfig();
console.log('当前配置:', currentConfig);
```

## 迁移指南

### 旧代码（硬编码）
```javascript
const response = await fetch('/api/generate-address', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' }
});
const result = await response.json();
```

### 新代码（使用API配置）
```javascript
const result = await window.apiConfig.generateAddress();
```

## 优势

1. **更好的可维护性** - API端点集中管理，修改更容易
2. **环境适应性** - 不同环境可以使用不同的API配置
3. **更强的错误处理** - 自动重试和友好的错误提示
4. **更好的用户体验** - 网络问题时的自动重试提高成功率
5. **更简洁的代码** - 减少重复的fetch代码

## 故障排除

### 常见问题

1. **API配置不生效**
   - 检查 `.env` 文件是否正确加载
   - 确认HTML中正确引入了 `api-config.js`
   - 检查浏览器控制台是否有加载错误

2. **网络请求失败**
   - 检查 `API_BASE_URL` 配置是否正确
   - 确认目标服务器可访问
   - 查看浏览器网络面板的详细错误

3. **重试机制不工作**
   - 检查 `API_RETRY_ATTEMPTS` 和 `API_RETRY_DELAY` 配置
   - 确认错误类型是否触发重试（只有网络错误和5xx错误会重试）

### 调试技巧

1. **启用详细日志**
   ```javascript
   // 在浏览器控制台中查看API请求日志
   window.apiConfig.config.debug = true;
   ```

2. **检查配置加载**
   ```javascript
   // 查看当前API配置
   console.log('API配置:', window.apiConfig.getConfig());
   console.log('全局配置:', window.appConfig);
   ```

3. **测试API连接**
   ```javascript
   // 手动测试API连接
   window.apiConfig.generateAddress()
     .then(result => console.log('API测试成功:', result))
     .catch(error => console.error('API测试失败:', error));
   ```
