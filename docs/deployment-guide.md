# 临时邮箱系统服务器部署完整指南

## 📋 目录

1. [系统要求和环境准备](#系统要求和环境准备)
2. [安装和配置步骤](#安装和配置步骤)
3. [邮件服务器配置](#邮件服务器配置)
4. [Web服务器配置](#web服务器配置)
5. [部署流程](#部署流程)
6. [运维和维护](#运维和维护)
7. [安全配置](#安全配置)
8. [故障排除](#故障排除)

---

## 🖥️ 系统要求和环境准备

### 推荐的操作系统

#### 主要支持的Linux发行版
- **Ubuntu 20.04 LTS / 22.04 LTS** (推荐)
- **Debian 11 (Bullseye) / 12 (Bookworm)**
- **CentOS 8 / Rocky Linux 8**
- **RHEL 8/9**

#### 为什么选择这些发行版？
- 长期支持版本，稳定性好
- 软件包管理器完善
- 社区支持丰富
- 安全更新及时

### 硬件配置建议

#### 最低配置
- **CPU**: 1核心 (1 vCPU)
- **内存**: 1GB RAM
- **存储**: 10GB 可用磁盘空间
- **网络**: 100Mbps 带宽

#### 推荐配置
- **CPU**: 2核心 (2 vCPU)
- **内存**: 2GB RAM
- **存储**: 20GB SSD
- **网络**: 1Gbps 带宽

#### 高负载配置
- **CPU**: 4核心 (4 vCPU)
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 1Gbps+ 带宽

### 必需的软件依赖和版本要求

#### 系统级依赖

```bash
# Ubuntu/Debian 系统
sudo apt update
sudo apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    git \
    curl \
    wget \
    unzip \
    sqlite3 \
    nginx \
    postfix \
    mailutils \
    ufw \
    logrotate \
    cron \
    htop \
    tree

# CentOS/RHEL 系统
sudo yum update -y
sudo yum install -y \
    python3 \
    python3-pip \
    python3-devel \
    gcc \
    gcc-c++ \
    make \
    git \
    curl \
    wget \
    unzip \
    sqlite \
    nginx \
    postfix \
    mailx \
    firewalld \
    logrotate \
    cronie \
    htop \
    tree
```

#### Python版本要求
- **Python 3.8+** (推荐 Python 3.9 或 3.10)
- **pip 21.0+**
- **virtualenv** 或 **venv**

#### Node.js (用于前端构建)
```bash
# 安装 Node.js 16+ (用于 TailwindCSS 构建)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
```

### 网络和域名要求

#### 域名配置
- 拥有一个有效的域名 (例如: `yourdomain.com`)
- 配置 MX 记录指向服务器
- 配置 A 记录指向服务器IP

#### 端口要求
- **80** (HTTP) - 用于 Let's Encrypt 证书验证
- **443** (HTTPS) - Web 服务
- **25** (SMTP) - 邮件接收
- **8080** (可选) - 应用直接访问端口

---

## 🔧 安装和配置步骤

### 1. 创建系统用户和目录

#### 创建专用用户
```bash
# 创建 tempmail 用户组
sudo groupadd tempmail

# 创建 tempmail 用户
sudo useradd -r -g tempmail -d /var/www/tempmail -s /bin/bash tempmail

# 创建项目目录
sudo mkdir -p /var/www/tempmail
sudo chown tempmail:tempmail /var/www/tempmail

# 将当前用户添加到 tempmail 组 (可选，便于管理)
sudo usermod -a -G tempmail $USER
```

#### 设置目录权限
```bash
# 切换到项目目录
cd /var/www/tempmail

# 设置基本权限
sudo chmod 755 /var/www/tempmail
sudo chmod g+s /var/www/tempmail  # 设置组粘滞位
```

### 2. 下载和部署代码

#### 从Git仓库克隆
```bash
# 切换到 tempmail 用户
sudo -u tempmail -i

# 克隆代码仓库
cd /var/www/tempmail
git clone https://github.com/your-username/tempmail.git .

# 或者如果已有代码包
# wget https://github.com/your-username/tempmail/archive/main.zip
# unzip main.zip
# mv tempmail-main/* .
# rm -rf tempmail-main main.zip
```

#### 设置文件权限
```bash
# 确保所有文件属于正确的用户和组
sudo chown -R tempmail:tempmail /var/www/tempmail

# 设置执行权限
chmod +x /var/www/tempmail/*.sh
chmod +x /var/www/tempmail/scripts/**/*.sh
```

### 3. Python环境配置

#### 创建虚拟环境
```bash
# 切换到 tempmail 用户
sudo -u tempmail -i
cd /var/www/tempmail

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级 pip
pip install --upgrade pip
```

#### 安装Python依赖
```bash
# 安装项目依赖
pip install -r requirements.txt

# 验证安装
pip list
```

### 4. 前端资源构建

#### 安装Node.js依赖
```bash
# 安装前端依赖
npm install

# 构建CSS文件
npm run build-css

# 验证构建结果
ls -la static/css/
```

### 5. 数据库初始化

#### 创建数据库目录
```bash
# 创建数据库目录
mkdir -p /var/www/tempmail/database
mkdir -p /var/www/tempmail/logs

# 设置权限
chmod 755 /var/www/tempmail/database
chmod 755 /var/www/tempmail/logs
```

#### 初始化数据库
```bash
# 激活虚拟环境
source /var/www/tempmail/venv/bin/activate

# 初始化数据库表结构
cd /var/www/tempmail
python -m flask init-db

# 验证数据库创建
sqlite3 database/tempmail.db ".tables"
```

### 6. 环境变量配置

#### 创建生产环境配置
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 生产环境配置示例
```bash
# Flask App Configuration
FLASK_APP=app.py
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your_very_secure_secret_key_at_least_32_characters_long

# Database Configuration
DATABASE_PATH=/var/www/tempmail/database/tempmail.db

# Email Configuration
DOMAIN_NAME=yourdomain.com
EMAIL_EXPIRATION_HOURS=24

# Logging Configuration
LOG_FILE_APP=/var/www/tempmail/logs/app.log
LOG_FILE_MAIL_HANDLER=/var/www/tempmail/logs/mail_handler.log
LOG_FILE_CLEANUP=/var/www/tempmail/logs/cleanup.log
FLASK_LOG_LEVEL=INFO

# Performance Configuration
FLASK_CACHE_TYPE=simple
FLASK_CACHE_DEFAULT_TIMEOUT=300
FLASK_CACHE_THRESHOLD=500

# SQLite Optimization
SQLITE_TIMEOUT=60
SQLITE_CACHE_SIZE=8000
SQLITE_PAGE_SIZE=4096
SQLITE_AUTO_VACUUM=INCREMENTAL

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
MONITORING_EMAIL_ENABLED=true
MONITORING_USE_INTERNAL_MAIL=true
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# Security
AUTO_REFRESH_ENABLED=false
```

#### 保护配置文件
```bash
# 设置配置文件权限
chmod 600 /var/www/tempmail/.env
chown tempmail:tempmail /var/www/tempmail/.env
```

---

## 📧 邮件服务器配置

### 1. Postfix 安装和基础配置

#### 安装 Postfix
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y postfix mailutils

# CentOS/RHEL
sudo yum install -y postfix mailx
```

#### 配置 Postfix 主配置文件
```bash
# 备份原始配置
sudo cp /etc/postfix/main.cf /etc/postfix/main.cf.backup

# 编辑主配置文件
sudo nano /etc/postfix/main.cf
```

#### Postfix 主配置示例 (/etc/postfix/main.cf)
```bash
# 基本配置
myhostname = mail.yourdomain.com
mydomain = yourdomain.com
myorigin = $mydomain
inet_interfaces = all
inet_protocols = ipv4
mydestination = $myhostname, localhost.$mydomain, localhost, $mydomain

# 网络配置
mynetworks = *********/8 [::ffff:*********]/104 [::1]/128

# 邮箱大小限制
message_size_limit = 10240000
mailbox_size_limit = 1024000000

# 虚拟域配置
virtual_alias_domains = yourdomain.com
virtual_alias_maps = hash:/etc/postfix/virtual

# 传输配置
transport_maps = hash:/etc/postfix/transport

# 安全配置
smtpd_banner = $myhostname ESMTP
disable_vrfy_command = yes
smtpd_helo_required = yes

# 日志配置
maillog_file = /var/log/postfix.log
```

### 2. 配置虚拟别名和传输

#### 创建虚拟别名文件
```bash
# 创建虚拟别名配置
sudo nano /etc/postfix/virtual
```

#### 虚拟别名配置内容
```bash
# 将所有邮件转发到临时邮箱处理器
@yourdomain.com tempmail-handler
```

#### 创建传输配置
```bash
# 创建传输配置文件
sudo nano /etc/postfix/transport
```

#### 传输配置内容
```bash
# 临时邮箱域的传输配置
yourdomain.com local:tempmail-handler
```

#### 创建别名配置
```bash
# 编辑系统别名文件
sudo nano /etc/aliases
```

#### 别名配置内容
```bash
# 系统别名
postmaster: root
root: <EMAIL>

# 临时邮箱处理器别名
tempmail-handler: "|/var/www/tempmail/venv/bin/python /var/www/tempmail/mail_handler.py"
```

#### 更新配置文件
```bash
# 更新虚拟别名数据库
sudo postmap /etc/postfix/virtual

# 更新传输数据库
sudo postmap /etc/postfix/transport

# 更新系统别名数据库
sudo newaliases

# 重启 Postfix 服务
sudo systemctl restart postfix
sudo systemctl enable postfix
```

### 3. 邮件处理器配置

#### 设置邮件处理器权限
```bash
# 确保邮件处理器可执行
chmod +x /var/www/tempmail/mail_handler.py

# 设置正确的所有者
sudo chown tempmail:tempmail /var/www/tempmail/mail_handler.py

# 创建邮件处理日志目录
sudo mkdir -p /var/log/tempmail
sudo chown tempmail:tempmail /var/log/tempmail
```

#### 测试邮件处理器
```bash
# 切换到 tempmail 用户测试
sudo -u tempmail -i
cd /var/www/tempmail

# 激活虚拟环境
source venv/bin/activate

# 测试邮件处理器
echo "Test email content" | python mail_handler.py <EMAIL>

# 检查日志
tail -f logs/mail_handler.log
```

### 4. DNS 配置

#### 必需的 DNS 记录
```bash
# A 记录
yourdomain.com.     IN  A       YOUR_SERVER_IP
mail.yourdomain.com. IN  A       YOUR_SERVER_IP

# MX 记录
yourdomain.com.     IN  MX  10  mail.yourdomain.com.

# TXT 记录 (SPF)
yourdomain.com.     IN  TXT     "v=spf1 mx ~all"

# 可选：DMARC 记录
_dmarc.yourdomain.com. IN TXT    "v=DMARC1; p=none; rua=mailto:<EMAIL>"
```

#### 验证 DNS 配置
```bash
# 检查 MX 记录
dig MX yourdomain.com

# 检查 A 记录
dig A mail.yourdomain.com

# 检查 SPF 记录
dig TXT yourdomain.com
```

### 5. 邮件服务测试

#### 发送测试邮件
```bash
# 从外部发送测试邮件到临时邮箱
echo "Test message" | mail -s "Test Subject" <EMAIL>

# 检查 Postfix 日志
sudo tail -f /var/log/postfix.log

# 检查邮件处理器日志
tail -f /var/www/tempmail/logs/mail_handler.log
```

#### 验证邮件接收
```bash
# 通过 API 检查是否收到邮件
curl -X POST http://localhost:8080/api/create-email \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "test"}'

# 检查邮件是否被正确存储
sqlite3 /var/www/tempmail/database/tempmail.db \
  "SELECT * FROM received_mails ORDER BY received_at DESC LIMIT 5;"
```

---

## 🌐 Web服务器配置

### 1. Nginx 安装和配置

#### 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt install -y nginx

# CentOS/RHEL
sudo yum install -y nginx

# 启动并启用 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 创建 Nginx 配置文件
```bash
# 创建站点配置文件
sudo nano /etc/nginx/sites-available/tempmail
```

#### Nginx 配置示例
```nginx
# HTTP 配置 (重定向到 HTTPS)
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Let's Encrypt 验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # 重定向到 HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS 配置
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志配置
    access_log /var/log/nginx/tempmail_access.log;
    error_log /var/log/nginx/tempmail_error.log;

    # 静态文件配置
    location /static/ {
        alias /var/www/tempmail/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 主应用代理
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        access_log off;
    }
}
```

#### 启用站点配置
```bash
# 创建符号链接启用站点
sudo ln -s /etc/nginx/sites-available/tempmail /etc/nginx/sites-enabled/

# 删除默认站点配置
sudo rm -f /etc/nginx/sites-enabled/default

# 测试 Nginx 配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

### 2. SSL/TLS 证书配置

#### 安装 Certbot (Let's Encrypt)
```bash
# Ubuntu/Debian
sudo apt install -y certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install -y certbot python3-certbot-nginx

# 或使用 snap (推荐)
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot
```

#### 获取 SSL 证书
```bash
# 停止 Nginx (如果正在运行)
sudo systemctl stop nginx

# 获取证书 (standalone 模式)
sudo certbot certonly --standalone \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d yourdomain.com \
  -d www.yourdomain.com

# 或使用 webroot 模式 (如果 Nginx 正在运行)
sudo certbot certonly --webroot \
  -w /var/www/html \
  --email <EMAIL> \
  --agree-tos \
  --no-eff-email \
  -d yourdomain.com \
  -d www.yourdomain.com
```

#### 配置证书自动续期
```bash
# 测试自动续期
sudo certbot renew --dry-run

# 添加自动续期到 crontab
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -

# 或创建 systemd timer
sudo systemctl enable certbot.timer
sudo systemctl start certbot.timer
```

#### 验证 SSL 配置
```bash
# 启动 Nginx
sudo systemctl start nginx

# 测试 SSL 连接
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# 在线 SSL 测试
# 访问: https://www.ssllabs.com/ssltest/
```

---

## 🚀 部署流程

### 1. 服务配置和启动

#### 配置 Systemd 服务
```bash
# 复制服务配置文件
sudo cp /var/www/tempmail/config/systemd/tempmail-optimized.service \
       /etc/systemd/system/tempmail.service

# 编辑服务配置 (如需要)
sudo nano /etc/systemd/system/tempmail.service
```

#### Systemd 服务配置示例
```ini
[Unit]
Description=Temporary Email System - Gunicorn WSGI Server
After=network.target

[Service]
Type=simple
User=tempmail
Group=tempmail
WorkingDirectory=/var/www/tempmail
Environment=PATH=/var/www/tempmail/venv/bin:/usr/local/bin:/usr/bin:/bin

# 使用配置文件启动
ExecStart=/var/www/tempmail/venv/bin/gunicorn -c gunicorn.conf.py app:app

# 重启策略
Restart=always
RestartSec=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/tempmail/logs /var/www/tempmail/database

# 日志
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### 启动和启用服务
```bash
# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start tempmail

# 启用开机自启
sudo systemctl enable tempmail

# 检查服务状态
sudo systemctl status tempmail
```

### 2. 端口和防火墙配置

#### Ubuntu/Debian (UFW)
```bash
# 启用 UFW
sudo ufw enable

# 允许 SSH
sudo ufw allow ssh

# 允许 HTTP 和 HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 允许 SMTP
sudo ufw allow 25/tcp

# 可选：允许应用直接访问端口 (仅用于调试)
# sudo ufw allow 8080/tcp

# 检查防火墙状态
sudo ufw status verbose
```

#### CentOS/RHEL (firewalld)
```bash
# 启动 firewalld
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 允许 HTTP 和 HTTPS
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https

# 允许 SMTP
sudo firewall-cmd --permanent --add-service=smtp

# 可选：允许应用端口
# sudo firewall-cmd --permanent --add-port=8080/tcp

# 重新加载防火墙配置
sudo firewall-cmd --reload

# 检查防火墙状态
sudo firewall-cmd --list-all
```

### 3. 验证部署

#### 检查服务状态
```bash
# 检查应用服务
sudo systemctl status tempmail

# 检查 Nginx
sudo systemctl status nginx

# 检查 Postfix
sudo systemctl status postfix

# 检查端口监听
sudo netstat -tlnp | grep -E ':(80|443|25|8080)'
```

#### 功能测试
```bash
# 测试 Web 界面
curl -I https://yourdomain.com

# 测试 API
curl -X POST https://yourdomain.com/api/create-email \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "test"}'

# 测试邮件接收
echo "Test message" | mail -s "Test" <EMAIL>
```

### 4. 设置定时任务

#### 配置邮件清理任务
```bash
# 切换到 tempmail 用户
sudo -u tempmail crontab -e

# 添加清理任务 (每小时执行一次)
0 * * * * cd /var/www/tempmail && /var/www/tempmail/venv/bin/python cleanup_script.py

# 添加日志轮转任务 (每天凌晨执行)
0 0 * * * /usr/sbin/logrotate /etc/logrotate.d/tempmail
```

#### 配置系统监控任务
```bash
# 添加健康检查任务 (每5分钟执行一次)
*/5 * * * * cd /var/www/tempmail && /var/www/tempmail/scripts/diagnostics/health_check.sh

# 添加性能监控任务 (每15分钟执行一次)
*/15 * * * * cd /var/www/tempmail && /var/www/tempmail/scripts/diagnostics/performance_check.sh
```

---

## 🛠️ 运维和维护

### 1. 日志管理和轮转

#### 配置 Logrotate
```bash
# 创建 logrotate 配置文件
sudo nano /etc/logrotate.d/tempmail
```

#### Logrotate 配置内容
```bash
/var/www/tempmail/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 tempmail tempmail
    postrotate
        systemctl reload tempmail
    endscript
}

/var/log/nginx/tempmail_*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

#### 测试日志轮转
```bash
# 测试配置
sudo logrotate -d /etc/logrotate.d/tempmail

# 强制执行轮转
sudo logrotate -f /etc/logrotate.d/tempmail
```

### 2. 监控系统配置

#### 启用内置监控
```bash
# 编辑环境变量文件
nano /var/www/tempmail/.env

# 确保监控配置正确
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
MONITORING_EMAIL_ENABLED=true
MONITORING_USE_INTERNAL_MAIL=true
MONITORING_ALERT_EMAIL_TO=<EMAIL>
```

#### 监控 API 端点
```bash
# 检查监控状态
curl http://localhost:8080/api/monitoring/status

# 获取系统指标
curl http://localhost:8080/api/monitoring/metrics

# 执行健康检查
curl http://localhost:8080/api/monitoring/health
```

### 3. 备份策略

#### 数据库备份脚本
```bash
# 创建备份脚本
sudo nano /var/www/tempmail/scripts/maintenance/backup_database.sh
```

#### 备份脚本内容
```bash
#!/bin/bash

# 数据库备份脚本
BACKUP_DIR="/var/www/tempmail/backup/database"
DB_PATH="/var/www/tempmail/database/tempmail.db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/tempmail_backup_$DATE.db"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 执行备份
sqlite3 "$DB_PATH" ".backup $BACKUP_FILE"

# 压缩备份文件
gzip "$BACKUP_FILE"

# 删除7天前的备份
find "$BACKUP_DIR" -name "*.gz" -mtime +7 -delete

echo "数据库备份完成: ${BACKUP_FILE}.gz"
```

#### 配置文件备份脚本
```bash
# 创建配置备份脚本
sudo nano /var/www/tempmail/scripts/maintenance/backup_configs.sh
```

#### 配置备份脚本内容
```bash
#!/bin/bash

# 配置文件备份脚本
BACKUP_DIR="/var/www/tempmail/backup/configs"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/configs_backup_$DATE.tar.gz"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份配置文件
tar -czf "$BACKUP_FILE" \
    /var/www/tempmail/.env \
    /var/www/tempmail/gunicorn.conf.py \
    /etc/systemd/system/tempmail.service \
    /etc/nginx/sites-available/tempmail \
    /etc/postfix/main.cf \
    /etc/postfix/virtual \
    /etc/postfix/transport \
    /etc/aliases

# 删除30天前的备份
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete

echo "配置文件备份完成: $BACKUP_FILE"
```

#### 设置自动备份
```bash
# 设置脚本权限
chmod +x /var/www/tempmail/scripts/maintenance/backup_*.sh

# 添加到 crontab
sudo -u tempmail crontab -e

# 每天凌晨2点备份数据库
0 2 * * * /var/www/tempmail/scripts/maintenance/backup_database.sh

# 每周日凌晨3点备份配置文件
0 3 * * 0 /var/www/tempmail/scripts/maintenance/backup_configs.sh
```

### 4. 性能优化建议

#### 系统级优化
```bash
# 优化文件描述符限制
echo "tempmail soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "tempmail hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 1024" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 1024" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 应用级优化
```bash
# 编辑 Gunicorn 配置
nano /var/www/tempmail/gunicorn.conf.py

# 根据服务器配置调整工作进程数
# workers = (CPU核心数 * 2) + 1
workers = 3  # 适用于2核心服务器

# 调整连接数
worker_connections = 1000

# 启用预加载
preload_app = True
```

---

## 🔒 安全配置

### 1. 系统安全加固

#### 更新系统和软件包
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y
sudo apt autoremove -y

# CentOS/RHEL
sudo yum update -y
sudo yum autoremove -y

# 启用自动安全更新 (Ubuntu/Debian)
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

#### SSH 安全配置
```bash
# 编辑 SSH 配置
sudo nano /etc/ssh/sshd_config

# 推荐的安全设置
Port 22
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2

# 重启 SSH 服务
sudo systemctl restart sshd
```

#### 配置 Fail2Ban
```bash
# 安装 Fail2Ban
sudo apt install -y fail2ban  # Ubuntu/Debian
sudo yum install -y fail2ban  # CentOS/RHEL

# 创建本地配置文件
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# 编辑配置
sudo nano /etc/fail2ban/jail.local
```

#### Fail2Ban 配置示例
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[postfix]
enabled = true
port = smtp
logpath = /var/log/mail.log
maxretry = 3
```

### 2. 应用安全设置

#### 文件权限安全
```bash
# 设置严格的文件权限
sudo chown -R tempmail:tempmail /var/www/tempmail
sudo chmod -R 755 /var/www/tempmail
sudo chmod 600 /var/www/tempmail/.env
sudo chmod 644 /var/www/tempmail/database/tempmail.db
sudo chmod 755 /var/www/tempmail/logs
```

#### 数据库安全
```bash
# 设置数据库文件权限
sudo chmod 600 /var/www/tempmail/database/tempmail.db
sudo chown tempmail:tempmail /var/www/tempmail/database/tempmail.db

# 定期备份数据库
# (已在备份策略部分配置)
```

### 3. 网络安全

#### 防火墙配置 (详细)
```bash
# Ubuntu/Debian (UFW)
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要的服务
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 25/tcp

# 限制连接频率
sudo ufw limit ssh

# 启用防火墙
sudo ufw enable

# 检查状态
sudo ufw status verbose
```

#### 配置速率限制
```bash
# 在 Nginx 配置中添加速率限制
sudo nano /etc/nginx/sites-available/tempmail

# 在 http 块中添加
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;
limit_req_zone $binary_remote_addr zone=web:10m rate=1r/s;

# 在 location 块中应用
location /api/ {
    limit_req zone=api burst=5 nodelay;
    # ... 其他配置
}

location / {
    limit_req zone=web burst=10 nodelay;
    # ... 其他配置
}
```

### 4. 监控和告警安全

#### 配置安全监控
```bash
# 编辑监控配置
nano /var/www/tempmail/.env

# 启用安全相关监控
MONITORING_ENABLED=true
MONITORING_LEVEL=standard
MONITORING_EMAIL_ENABLED=true
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# 设置严格的阈值
MONITORING_CPU_THRESHOLD=70.0
MONITORING_MEMORY_THRESHOLD=80.0
MONITORING_DISK_THRESHOLD=85.0
```

#### 日志监控
```bash
# 创建安全日志监控脚本
sudo nano /var/www/tempmail/scripts/diagnostics/security_monitor.sh
```

#### 安全监控脚本内容
```bash
#!/bin/bash

# 安全监控脚本
LOG_FILE="/var/log/security_monitor.log"

# 检查失败的登录尝试
FAILED_LOGINS=$(grep "Failed password" /var/log/auth.log | wc -l)
if [ $FAILED_LOGINS -gt 10 ]; then
    echo "$(date): 检测到异常登录尝试: $FAILED_LOGINS 次" >> $LOG_FILE
fi

# 检查异常的网络连接
CONNECTIONS=$(netstat -an | grep :80 | wc -l)
if [ $CONNECTIONS -gt 100 ]; then
    echo "$(date): 检测到异常网络连接数: $CONNECTIONS" >> $LOG_FILE
fi

# 检查磁盘使用率
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "$(date): 磁盘使用率过高: $DISK_USAGE%" >> $LOG_FILE
fi
```

---

## 🔧 故障排除

### 1. 常见问题和解决方案

#### 服务启动失败

**问题**: 临时邮箱服务无法启动
```bash
# 检查服务状态
sudo systemctl status tempmail

# 查看详细日志
sudo journalctl -u tempmail -f

# 常见原因和解决方案:
```

**解决方案**:
```bash
# 1. 检查端口占用
sudo netstat -tlnp | grep 8080
# 如果端口被占用，杀死占用进程或更改端口

# 2. 检查权限
sudo chown -R tempmail:tempmail /var/www/tempmail
sudo chmod +x /var/www/tempmail/venv/bin/gunicorn

# 3. 检查虚拟环境
sudo -u tempmail /var/www/tempmail/venv/bin/python --version

# 4. 检查配置文件
sudo -u tempmail /var/www/tempmail/venv/bin/gunicorn --check-config -c gunicorn.conf.py app:app
```

#### 邮件接收问题

**问题**: 邮件无法正常接收和处理
```bash
# 检查 Postfix 状态
sudo systemctl status postfix

# 查看邮件日志
sudo tail -f /var/log/mail.log
sudo tail -f /var/www/tempmail/logs/mail_handler.log
```

**解决方案**:
```bash
# 1. 检查 DNS 配置
dig MX yourdomain.com
dig A mail.yourdomain.com

# 2. 测试邮件处理器
echo "test" | sudo -u tempmail /var/www/tempmail/venv/bin/python /var/www/tempmail/mail_handler.py <EMAIL>

# 3. 检查别名配置
sudo newaliases
sudo postmap /etc/postfix/virtual
sudo postmap /etc/postfix/transport

# 4. 重启 Postfix
sudo systemctl restart postfix
```

#### SSL 证书问题

**问题**: HTTPS 访问失败或证书错误
```bash
# 检查证书状态
sudo certbot certificates

# 检查证书文件
sudo ls -la /etc/letsencrypt/live/yourdomain.com/
```

**解决方案**:
```bash
# 1. 续期证书
sudo certbot renew

# 2. 重新获取证书
sudo certbot delete --cert-name yourdomain.com
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# 3. 检查 Nginx 配置
sudo nginx -t
sudo systemctl restart nginx
```

#### 数据库问题

**问题**: 数据库连接失败或数据丢失
```bash
# 检查数据库文件
ls -la /var/www/tempmail/database/

# 测试数据库连接
sqlite3 /var/www/tempmail/database/tempmail.db ".tables"
```

**解决方案**:
```bash
# 1. 检查文件权限
sudo chown tempmail:tempmail /var/www/tempmail/database/tempmail.db
sudo chmod 644 /var/www/tempmail/database/tempmail.db

# 2. 重新初始化数据库
sudo -u tempmail /var/www/tempmail/venv/bin/python -m flask init-db

# 3. 从备份恢复
gunzip /var/www/tempmail/backup/database/tempmail_backup_YYYYMMDD_HHMMSS.db.gz
cp /var/www/tempmail/backup/database/tempmail_backup_YYYYMMDD_HHMMSS.db /var/www/tempmail/database/tempmail.db
```

### 2. 诊断工具使用

#### 使用项目内置诊断工具
```bash
# 切换到项目目录
cd /var/www/tempmail

# 运行完整诊断
./scripts/diagnostics/final_verification.sh

# 检查服务状态
./scripts/diagnostics/diagnose_service_failure.sh

# 检查权限问题
./scripts/diagnostics/diagnose_permissions.sh

# 性能检查
./scripts/diagnostics/performance_check.sh
```

#### 系统级诊断
```bash
# 检查系统资源
htop
iotop
df -h
free -h

# 检查网络连接
netstat -tlnp
ss -tlnp

# 检查进程状态
ps aux | grep -E "(gunicorn|nginx|postfix)"

# 检查系统日志
sudo journalctl -xe
sudo tail -f /var/log/syslog
```

#### 应用级诊断
```bash
# 检查应用日志
tail -f /var/www/tempmail/logs/app.log
tail -f /var/www/tempmail/logs/mail_handler.log

# 测试 API 端点
curl -I http://localhost:8080/
curl -X POST http://localhost:8080/api/create-email \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "test"}'

# 检查监控状态
curl http://localhost:8080/api/monitoring/status
curl http://localhost:8080/api/monitoring/health
```

### 3. 性能问题排查

#### 高 CPU 使用率
```bash
# 查看 CPU 使用情况
top -p $(pgrep -f "gunicorn.*app:app" | tr '\n' ',' | sed 's/,$//')

# 检查工作进程数配置
grep workers /var/www/tempmail/gunicorn.conf.py

# 调整工作进程数 (建议: CPU核心数 * 2 + 1)
nano /var/www/tempmail/gunicorn.conf.py
sudo systemctl restart tempmail
```

#### 高内存使用率
```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 检查应用内存使用
systemctl status tempmail | grep Memory

# 优化配置
nano /var/www/tempmail/.env
# 调整缓存设置
FLASK_CACHE_THRESHOLD=200
SQLITE_CACHE_SIZE=4000
```

#### 响应时间慢
```bash
# 测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/

# 检查数据库性能
sqlite3 /var/www/tempmail/database/tempmail.db "PRAGMA optimize;"

# 检查日志文件大小
ls -lh /var/www/tempmail/logs/
# 如果日志文件过大，执行日志轮转
sudo logrotate -f /etc/logrotate.d/tempmail
```

### 4. 紧急恢复程序

#### 服务完全不可用
```bash
# 1. 立即检查关键服务
sudo systemctl status tempmail nginx postfix

# 2. 重启所有服务
sudo systemctl restart tempmail
sudo systemctl restart nginx
sudo systemctl restart postfix

# 3. 检查防火墙
sudo ufw status
# 如果防火墙阻止了访问，临时禁用
sudo ufw disable

# 4. 从备份恢复
# 恢复数据库
cp /var/www/tempmail/backup/database/latest_backup.db /var/www/tempmail/database/tempmail.db

# 恢复配置文件
tar -xzf /var/www/tempmail/backup/configs/latest_config.tar.gz -C /
```

#### 数据丢失恢复
```bash
# 1. 停止服务
sudo systemctl stop tempmail

# 2. 从最新备份恢复数据库
LATEST_BACKUP=$(ls -t /var/www/tempmail/backup/database/*.gz | head -1)
gunzip -c "$LATEST_BACKUP" > /var/www/tempmail/database/tempmail.db

# 3. 设置正确权限
sudo chown tempmail:tempmail /var/www/tempmail/database/tempmail.db
sudo chmod 644 /var/www/tempmail/database/tempmail.db

# 4. 重启服务
sudo systemctl start tempmail
```

---

## 📚 附录

### 1. 相关文档链接

- [生产级服务部署指南](PRODUCTION_SERVICE_DEPLOYMENT_GUIDE.md)
- [监控系统完整指南](MONITORING_SYSTEM_COMPLETE_GUIDE.md)
- [日志管理指南](LOG_MANAGEMENT_GUIDE.md)
- [应用优化指南](APPLICATION_OPTIMIZATION_GUIDE.md)

### 2. 有用的命令速查

#### 服务管理
```bash
# 查看服务状态
sudo systemctl status tempmail nginx postfix

# 重启服务
sudo systemctl restart tempmail

# 查看日志
sudo journalctl -u tempmail -f
tail -f /var/www/tempmail/logs/app.log

# 重新加载配置
sudo systemctl reload tempmail
```

#### 维护命令
```bash
# 清理过期邮件
sudo -u tempmail /var/www/tempmail/venv/bin/python /var/www/tempmail/cleanup_script.py

# 备份数据库
/var/www/tempmail/scripts/maintenance/backup_database.sh

# 检查磁盘空间
df -h

# 查看系统资源
htop
```

### 3. 配置文件模板

#### 最小化 .env 配置
```bash
# 生产环境最小配置
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your_32_character_secret_key_here
DATABASE_PATH=/var/www/tempmail/database/tempmail.db
DOMAIN_NAME=yourdomain.com
EMAIL_EXPIRATION_HOURS=24
LOG_FILE_APP=/var/www/tempmail/logs/app.log
LOG_FILE_MAIL_HANDLER=/var/www/tempmail/logs/mail_handler.log
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
```

### 4. 联系和支持

如果遇到本指南未涵盖的问题，请：

1. 查看项目的 GitHub Issues
2. 检查相关日志文件
3. 使用内置诊断工具
4. 参考相关文档

---

## 🎉 部署完成检查清单

部署完成后，请确认以下项目：

- [ ] **系统要求**: 操作系统和硬件配置满足要求
- [ ] **软件依赖**: 所有必需软件已安装并配置正确
- [ ] **用户和权限**: tempmail 用户已创建，文件权限设置正确
- [ ] **Python环境**: 虚拟环境已创建，依赖已安装
- [ ] **数据库**: SQLite 数据库已初始化，表结构正确
- [ ] **环境变量**: .env 文件已配置，包含所有必需变量
- [ ] **邮件服务**: Postfix 已配置，邮件处理器工作正常
- [ ] **Web服务**: Nginx 已配置，反向代理工作正常
- [ ] **SSL证书**: Let's Encrypt 证书已获取并配置
- [ ] **防火墙**: 必要端口已开放，安全规则已设置
- [ ] **服务启动**: 所有服务已启动并设置开机自启
- [ ] **功能测试**: Web界面可访问，邮件收发正常
- [ ] **监控系统**: 监控已启用，告警配置正确
- [ ] **日志管理**: 日志轮转已配置，日志正常记录
- [ ] **备份策略**: 自动备份已设置，备份文件正常生成
- [ ] **安全加固**: 系统安全设置已完成
- [ ] **文档记录**: 部署信息已记录，维护文档已准备

恭喜！您的临时邮箱系统已成功部署并可以投入使用。
