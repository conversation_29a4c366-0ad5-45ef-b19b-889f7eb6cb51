# MySQL数据库升级指南

本指南详细说明如何将临时邮箱系统从SQLite数据库升级到MySQL数据库。

## 📋 升级概述

### 升级内容
- ✅ 数据库系统：SQLite → MySQL 8.0+
- ✅ 连接管理：原生sqlite3 → SQLAlchemy + PyMySQL
- ✅ 配置管理：新增MySQL配置选项
- ✅ 数据迁移：完整的数据迁移工具
- ✅ 向后兼容：支持SQLite和MySQL双模式

### 技术栈更新
- **新增依赖**：PyMySQL 1.1.1, SQLAlchemy 2.0.36, Flask-SQLAlchemy 3.1.1
- **数据库引擎**：InnoDB存储引擎
- **字符集**：UTF8MB4 (完整Unicode支持)
- **连接池**：SQLAlchemy连接池管理

## 🚀 快速开始

### 1. 环境准备

```bash
# 1. 安装MySQL服务器 (Ubuntu/Debian)
sudo apt update
sudo apt install mysql-server mysql-client

# 2. 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 3. 安全配置MySQL
sudo mysql_secure_installation
```

### 2. 解决MySQL访问权限问题

**如果遇到 ERROR 1698 (28000): Access denied for user 'root'@'localhost'**

这是MySQL 8.0+的常见问题，使用以下方法解决：

#### 方法1：使用sudo访问（推荐）
```bash
# MySQL 8.0默认使用auth_socket插件，需要sudo访问
sudo mysql

# 或者
sudo mysql -u root
```

#### 方法2：使用权限修复脚本（自动化）
```bash
# 使用我们提供的修复脚本
./scripts/database/fix_mysql_permissions.sh

# 或者创建项目专用用户
./scripts/database/fix_mysql_permissions.sh -u tempmail_user your_secure_password
```

#### 方法3：手动修复root密码认证
```bash
# 1. 使用sudo登录MySQL
sudo mysql

# 2. 在MySQL中执行
```
```sql
-- 修改root用户认证方式
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_new_password';
FLUSH PRIVILEGES;
EXIT;
```
```bash
# 3. 现在可以使用密码登录
mysql -u root -p
```

### 3. 创建数据库和用户

```sql
-- 连接到MySQL（使用sudo或修复后的密码）
sudo mysql
-- 或者: mysql -u root -p

-- 创建数据库
CREATE DATABASE tempmail CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'tempmail_user'@'localhost' IDENTIFIED BY 'your_secure_password';
CREATE USER 'tempmail_user'@'%' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON tempmail.* TO 'tempmail_user'@'localhost';
GRANT ALL PRIVILEGES ON tempmail.* TO 'tempmail_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 验证用户创建
SELECT user, host, plugin FROM mysql.user WHERE user='tempmail_user';
```

### 4. 更新项目依赖

```bash
# 激活虚拟环境
source venv/bin/activate

# 安装新依赖
pip install -r requirements.txt
```

### 5. 配置环境变量

```bash
# 复制MySQL配置模板
cp .env.mysql.example .env

# 编辑配置文件
nano .env
```

**关键配置项：**
```bash
# 数据库类型
DATABASE_TYPE=mysql

# MySQL连接配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=tempmail
MYSQL_USERNAME=tempmail_user
MYSQL_PASSWORD=your_secure_password
```

### 6. 初始化MySQL数据库

```bash
# 使用自动化脚本
./scripts/database/init_mysql.sh

# 或手动执行
python3 database/mysql_cli.py init-all
```

### 7. 数据迁移（可选）

```bash
# 如果有现有SQLite数据需要迁移
python3 database/migrate_to_mysql.py --sqlite-path database/tempmail.db

# 预检查迁移
python3 database/migrate_to_mysql.py --dry-run
```

### 8. 启动应用

```bash
# 测试启动
python3 app.py

# 生产环境启动
gunicorn -c gunicorn.conf.py app:app
```

## 🔧 详细配置

### MySQL配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `MYSQL_HOST` | localhost | MySQL服务器地址 |
| `MYSQL_PORT` | 3306 | MySQL端口 |
| `MYSQL_DATABASE` | tempmail | 数据库名 |
| `MYSQL_USERNAME` | tempmail_user | 数据库用户名 |
| `MYSQL_PASSWORD` | - | 数据库密码（必填） |
| `MYSQL_CHARSET` | utf8mb4 | 字符集 |
| `MYSQL_COLLATION` | utf8mb4_unicode_ci | 排序规则 |

### 连接池配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `MYSQL_POOL_SIZE` | 10 | 连接池大小 |
| `MYSQL_MAX_OVERFLOW` | 20 | 最大溢出连接数 |
| `MYSQL_POOL_TIMEOUT` | 30 | 连接超时时间(秒) |
| `MYSQL_POOL_RECYCLE` | 3600 | 连接回收时间(秒) |

### SSL配置

```bash
# 禁用SSL（开发环境）
MYSQL_SSL_DISABLED=true

# 启用SSL（生产环境）
MYSQL_SSL_DISABLED=false
MYSQL_SSL_CA=/path/to/ca-cert.pem
MYSQL_SSL_CERT=/path/to/client-cert.pem
MYSQL_SSL_KEY=/path/to/client-key.pem
```

## 🛠️ 管理工具

### 数据库管理CLI

```bash
# 测试连接
python3 database/mysql_cli.py test-connection

# 显示配置
python3 database/mysql_cli.py show-config

# 检查表状态
python3 database/mysql_cli.py check-tables

# 优化表
python3 database/mysql_cli.py optimize-tables

# 完整初始化
python3 database/mysql_cli.py init-all
```

### 数据迁移工具

```bash
# 完整迁移
python3 database/migrate_to_mysql.py

# 指定SQLite文件路径
python3 database/migrate_to_mysql.py --sqlite-path /path/to/database.db

# 预检查（不执行实际迁移）
python3 database/migrate_to_mysql.py --dry-run
```

### 自动化初始化脚本

```bash
# 标准初始化
./scripts/database/init_mysql.sh

# 仅检查环境
./scripts/database/init_mysql.sh --check-only

# 创建数据库用户并初始化
./scripts/database/init_mysql.sh --create-user --root-password your_root_password
```

## 📊 性能优化

### MySQL服务器优化

**my.cnf配置建议：**
```ini
[mysqld]
# 基础配置
default-storage-engine = InnoDB
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 性能配置
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 连接配置
max_connections = 200
max_connect_errors = 10000
wait_timeout = 28800
interactive_timeout = 28800

# 查询缓存
query_cache_type = 1
query_cache_size = 32M
```

### 应用层优化

```bash
# 启用查询缓存
FLASK_CACHE_TYPE=redis
CACHE_REDIS_URL=redis://localhost:6379/0

# 连接池调优
MYSQL_POOL_SIZE=20
MYSQL_MAX_OVERFLOW=40
MYSQL_POOL_RECYCLE=1800
```

## 🔍 监控和维护

### 数据库监控

```sql
-- 查看连接状态
SHOW PROCESSLIST;

-- 查看表状态
SHOW TABLE STATUS;

-- 查看索引使用情况
SHOW INDEX FROM temporary_emails;

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

### 定期维护

```bash
# 优化表
python3 database/mysql_cli.py optimize-tables

# 检查表完整性
mysqlcheck -u tempmail_user -p tempmail

# 备份数据库
mysqldump -u tempmail_user -p tempmail > backup_$(date +%Y%m%d).sql
```

## 🚨 故障排除

### 常见问题

**1. ERROR 1698: Access denied for user 'root'@'localhost'**

这是MySQL 8.0+最常见的问题，原因是默认使用`auth_socket`插件：

```bash
# 解决方案1：使用sudo访问
sudo mysql

# 解决方案2：使用权限修复脚本
./scripts/database/fix_mysql_permissions.sh -c  # 检查状态
./scripts/database/fix_mysql_permissions.sh -f your_new_password  # 修复root
./scripts/database/fix_mysql_permissions.sh -u tempmail_user your_password  # 创建用户

# 解决方案3：手动修复
sudo mysql -e "
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'your_password';
FLUSH PRIVILEGES;
"
```

**2. 连接失败**
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口监听
netstat -tlnp | grep 3306

# 测试连接
mysql -h localhost -u tempmail_user -p

# 检查防火墙
sudo ufw status
sudo ufw allow 3306/tcp  # 如果需要远程访问
```

**3. 权限问题**
```sql
-- 检查用户权限
SHOW GRANTS FOR 'tempmail_user'@'localhost';

-- 检查用户认证插件
SELECT user, host, plugin, authentication_string FROM mysql.user WHERE user='tempmail_user';

-- 重新授权
GRANT ALL PRIVILEGES ON tempmail.* TO 'tempmail_user'@'localhost';
FLUSH PRIVILEGES;

-- 如果用户不存在，重新创建
DROP USER IF EXISTS 'tempmail_user'@'localhost';
CREATE USER 'tempmail_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON tempmail.* TO 'tempmail_user'@'localhost';
FLUSH PRIVILEGES;
```

**4. 字符集问题**
```sql
-- 检查数据库字符集
SHOW CREATE DATABASE tempmail;

-- 修改字符集
ALTER DATABASE tempmail CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 检查表字符集
SELECT table_name, table_collation FROM information_schema.tables
WHERE table_schema = 'tempmail';
```

**5. 连接池问题**
```bash
# 检查连接数
mysql -u tempmail_user -p -e "SHOW STATUS LIKE 'Threads_connected';"

# 检查最大连接数
mysql -u tempmail_user -p -e "SHOW VARIABLES LIKE 'max_connections';"

# 如果连接数不足，修改配置
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
# 添加: max_connections = 200
sudo systemctl restart mysql
```

### 日志分析

```bash
# 查看MySQL错误日志
sudo tail -f /var/log/mysql/error.log

# 查看应用日志
tail -f logs/app.log

# 查看初始化日志
tail -f logs/mysql_init.log
```

## 🔄 回滚方案

如果需要回滚到SQLite：

```bash
# 1. 修改环境变量
DATABASE_TYPE=sqlite

# 2. 重启应用
sudo systemctl restart tempmail

# 3. 验证功能
curl http://localhost:5000/api/generate-address
```

## 📚 参考资料

- [MySQL 8.0 官方文档](https://dev.mysql.com/doc/refman/8.0/en/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)
- [PyMySQL 文档](https://pymysql.readthedocs.io/)
- [Flask-SQLAlchemy 文档](https://flask-sqlalchemy.palletsprojects.com/)

---

## 🎯 升级检查清单

- [ ] MySQL服务器已安装并运行
- [ ] 数据库和用户已创建
- [ ] Python依赖已更新
- [ ] 环境变量已配置
- [ ] 数据库连接测试通过
- [ ] 表结构已创建
- [ ] 数据迁移已完成（如需要）
- [ ] 应用启动正常
- [ ] API功能测试通过
- [ ] 监控系统正常
- [ ] 备份策略已制定

完成以上检查清单后，MySQL升级即告完成！
