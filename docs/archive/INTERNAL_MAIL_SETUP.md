# 临时邮箱系统内部邮件通知配置指南

## 概述

本文档详细介绍如何配置和使用临时邮箱系统的内部邮件通知功能。该功能利用系统现有的Postfix基础设施，创建专用管理员邮箱来发送监控告警，实现完全自给自足的邮件通知系统。

## 架构设计

### 工作原理

```
监控系统 → 内部邮件发送器 → 管理员邮箱 → Postfix → 外部邮箱
    ↓              ↓              ↓          ↓         ↓
  告警触发    创建邮件内容    本地邮件系统   SMTP发送   收件人
```

### 核心组件

1. **InternalMailSender**: 内部邮件发送器
2. **管理员邮箱**: 自动创建的专用邮箱（如 `<EMAIL>`）
3. **Postfix集成**: 利用现有邮件基础设施
4. **数据库集成**: 管理员邮箱存储在现有数据库中

## 配置步骤

### 1. 基础配置

在 `.env` 文件中添加以下配置：

```bash
# 启用内部邮件系统
MONITORING_USE_INTERNAL_MAIL=true

# 邮件通知基础配置
MONITORING_EMAIL_ENABLED=true
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# 管理员邮箱配置
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
MONITORING_SENDMAIL_PATH=/usr/sbin/sendmail
MONITORING_MAIL_RETRY_ATTEMPTS=3

# 确保域名配置正确
DOMAIN_NAME=kuroneko.lol
```

### 2. 验证Postfix配置

确保Postfix已正确配置并能发送邮件：

```bash
# 测试sendmail是否可用
which sendmail

# 测试基本邮件发送
echo "Test email" | sendmail -t <EMAIL>

# 检查Postfix状态
systemctl status postfix
```

### 3. 测试内部邮件功能

运行测试脚本验证配置：

```bash
python test_internal_mail.py
```

## 功能特性

### 自动管理员邮箱创建

系统会自动创建一个专用的管理员邮箱：
- 邮箱地址：`{prefix}@{domain}` (如 `<EMAIL>`)
- 永不过期：设置100年有效期
- 自动清理：保留最近100封邮件

### 智能邮件发送

- **重试机制**：发送失败时自动重试
- **超时控制**：30秒发送超时
- **错误处理**：详细的错误日志记录
- **格式化内容**：专业的告警邮件格式

### 数据库集成

- 管理员邮箱存储在现有数据库中
- 与临时邮箱系统完全兼容
- 支持邮件历史记录查询

## 配置对比

### 内部邮件 vs 外部SMTP

| 特性 | 内部邮件系统 | 外部SMTP |
|------|-------------|----------|
| **依赖性** | 仅需本地Postfix | 需要外部SMTP服务 |
| **安全性** | 高（本地处理） | 中（需要SMTP密码） |
| **配置复杂度** | 低 | 中 |
| **发送速度** | 快 | 中等 |
| **送达率** | 依赖本地配置 | 通常较高 |
| **成本** | 无额外成本 | 可能有服务费用 |
| **维护** | 依赖系统管理 | 依赖服务提供商 |

### 推荐使用场景

**使用内部邮件系统：**
- ✅ 已有Postfix配置
- ✅ 注重数据隐私
- ✅ 希望减少外部依赖
- ✅ 邮件量不大

**使用外部SMTP：**
- ✅ 需要高送达率
- ✅ 不想维护邮件服务器
- ✅ 需要高级邮件功能
- ✅ 邮件量很大

## 高级配置

### 自定义邮件模板

内部邮件系统支持自定义告警邮件格式：

```python
# 在 internal_mail_sender.py 中自定义 _format_alert_body 方法
def _format_alert_body(self, alert_data: Dict[str, Any]) -> str:
    # 自定义邮件内容格式
    pass
```

### 邮件清理策略

配置自动清理策略：

```bash
# 设置保留的邮件数量（默认100封）
# 可以在代码中修改 cleanup_admin_emails 方法
```

### 发送路径自定义

如果sendmail不在标准路径：

```bash
MONITORING_SENDMAIL_PATH=/usr/local/bin/sendmail
```

## 故障排除

### 常见问题

1. **管理员邮箱创建失败**
   ```bash
   # 检查数据库权限
   ls -la database/
   # 检查数据库连接
   sqlite3 database/tempmail.db ".tables"
   ```

2. **邮件发送失败**
   ```bash
   # 检查sendmail路径
   which sendmail
   # 检查Postfix状态
   systemctl status postfix
   # 查看邮件日志
   tail -f /var/log/mail.log
   ```

3. **权限问题**
   ```bash
   # 确保应用有执行sendmail的权限
   sudo chmod +x /usr/sbin/sendmail
   ```

### 调试模式

启用详细日志记录：

```bash
# 设置日志级别
FLASK_LOG_LEVEL=DEBUG

# 查看监控日志
tail -f logs/monitoring.log
```

### 测试邮件发送

使用测试脚本验证功能：

```bash
# 运行完整测试
python test_internal_mail.py

# 检查管理员邮箱状态
python -c "
from monitoring.internal_mail_sender import InternalMailSender
from monitoring.config import MonitoringConfig
import logging

config = MonitoringConfig.from_env()
logger = logging.getLogger('test')
sender = InternalMailSender(config, logger)
print(sender.get_admin_email_info())
"
```

## API接口

### 监控API扩展

内部邮件系统添加了以下API端点：

```bash
# 获取管理员邮箱信息
GET /api/monitoring/admin-email

# 测试邮件发送
POST /api/monitoring/test-email
{
  "recipient": "<EMAIL>"
}

# 清理管理员邮件
POST /api/monitoring/cleanup-emails
```

## 最佳实践

### 生产环境建议

1. **邮件监控**：定期检查管理员邮箱状态
2. **日志轮转**：配置日志文件自动轮转
3. **备份策略**：包含管理员邮箱数据
4. **性能监控**：监控邮件发送性能

### 安全建议

1. **访问控制**：限制对管理员邮箱的访问
2. **日志安全**：确保日志文件安全存储
3. **网络安全**：保护Postfix配置安全

### 维护建议

1. **定期清理**：自动清理过期邮件
2. **状态检查**：定期验证邮件发送功能
3. **配置备份**：备份邮件相关配置

## 总结

内部邮件通知系统提供了一个优雅、自给自足的监控告警解决方案。通过利用现有的临时邮箱基础设施，实现了：

- **零外部依赖**：完全基于现有系统
- **高度集成**：与临时邮箱系统无缝集成
- **简化配置**：最小化配置要求
- **可靠性高**：基于成熟的Postfix技术

这种设计特别适合注重数据隐私、希望减少外部依赖的部署环境。
