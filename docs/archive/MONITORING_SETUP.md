# 临时邮箱系统监控设置指南

## 概述

本文档介绍如何设置和使用临时邮箱系统的轻量级监控功能。

## 特性

- **轻量级设计**：内存占用 ~15-25MB，CPU占用 ~2-4%
- **多级监控**：basic/standard/detailed 三个级别
- **实时告警**：支持邮件和Webhook通知
- **健康检查**：自动检测系统和应用状态
- **API接口**：提供RESTful API获取监控数据
- **自动清理**：自动清理过期数据和日志

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置监控

复制配置文件模板：
```bash
cp monitoring_config.env.example .env
```

编辑 `.env` 文件，配置基本监控参数：
```bash
# 启用监控
MONITORING_ENABLED=true

# 设置监控级别（推荐生产环境使用 basic）
MONITORING_LEVEL=basic

# 配置告警邮箱（可选）
MONITORING_EMAIL_ENABLED=true
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
MONITORING_ALERT_EMAIL_TO=<EMAIL>
```

### 3. 启动应用

```bash
python app.py
```

监控系统将自动启动并开始收集指标。

### 4. 验证监控

访问监控API端点：
```bash
# 检查监控状态
curl http://localhost:5000/api/monitoring/status

# 获取当前指标
curl http://localhost:5000/api/monitoring/metrics

# 执行健康检查
curl http://localhost:5000/api/monitoring/health
```

## 配置详解

### 监控级别

- **basic**：基础系统指标，最低资源占用
  - CPU、内存、磁盘使用率
  - 数据库连接状态
  - 应用进程指标

- **standard**：标准监控 + 详细应用指标
  - 包含basic级别所有指标
  - 数据库表记录数
  - 请求统计信息

- **detailed**：全面监控
  - 包含standard级别所有指标
  - 网络IO统计
  - 磁盘IO统计
  - 日志文件大小

### 告警阈值

默认告警阈值（可通过环境变量调整）：

| 指标 | 默认阈值 | 环境变量 |
|------|----------|----------|
| CPU使用率 | 80% | MONITORING_CPU_THRESHOLD |
| 内存使用率 | 85% | MONITORING_MEMORY_THRESHOLD |
| 磁盘使用率 | 90% | MONITORING_DISK_THRESHOLD |
| 响应时间 | 5秒 | MONITORING_RESPONSE_TIME_THRESHOLD |
| 错误率 | 10% | MONITORING_ERROR_RATE_THRESHOLD |

### 通知配置

#### 邮件通知配置

监控系统使用内部邮件系统发送告警通知：

```bash
MONITORING_EMAIL_ENABLED=true
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
MONITORING_ALERT_EMAIL_TO=<EMAIL>
```

#### 内部邮件系统详解

**优势：**
- ✅ 完全自给自足，无需外部SMTP服务器
- ✅ 利用现有Postfix基础设施
- ✅ 更好的安全性和隐私保护
- ✅ 无需管理SMTP密码
- ✅ 更快的邮件发送速度
- ✅ 零额外成本

**工作原理：**
1. 自动创建管理员邮箱（如 `<EMAIL>`）
2. 通过本地sendmail程序发送邮件
3. 利用Postfix将邮件投递到外部收件箱

**测试内部邮件功能：**
```bash
# 测试内部邮件系统
python test_internal_mail.py

# 演示完整功能
python demo_internal_mail_monitoring.py
```

**详细配置指南：**
参见 `docs/INTERNAL_MAIL_SETUP.md` 获取完整的配置说明。

#### Webhook通知

支持钉钉、企业微信、Slack等：

```bash
MONITORING_WEBHOOK_ENABLED=true
MONITORING_WEBHOOK_URL=https://your-webhook-url.com/alert
```

钉钉机器人示例：
```bash
MONITORING_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN
```

## API接口

### 监控状态
```
GET /api/monitoring/status
```

返回监控系统运行状态和请求统计。

### 当前指标
```
GET /api/monitoring/metrics
```

返回最新收集的系统指标。

### 指标摘要
```
GET /api/monitoring/metrics/summary?minutes=10
```

返回指定时间范围内的指标摘要统计。

### 告警信息
```
GET /api/monitoring/alerts?hours=24
```

返回活跃告警和历史告警信息。

### 健康检查
```
GET /api/monitoring/health
```

执行系统健康检查。

### 清除告警
```
POST /api/monitoring/alerts/<alert_id>/clear
```

手动清除指定告警。

## 命令行管理

### 查看监控状态
```bash
flask monitoring --action status
```

### 启动/停止监控
```bash
flask monitoring --action start
flask monitoring --action stop
flask monitoring --action restart
```

## 测试监控系统

运行测试脚本：
```bash
python test_monitoring.py
```

该脚本将测试：
- 监控配置
- 指标收集
- Flask集成
- 资源使用情况

## 生产环境建议

### 推荐配置

```bash
# 基础配置
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
MONITORING_METRICS_INTERVAL=60
MONITORING_HEALTH_INTERVAL=300

# 资源限制
MONITORING_MAX_MEMORY_MB=20
MONITORING_MAX_LOG_SIZE_MB=50

# 告警阈值
MONITORING_CPU_THRESHOLD=80.0
MONITORING_MEMORY_THRESHOLD=85.0
MONITORING_DISK_THRESHOLD=90.0

# 内部邮件通知
MONITORING_EMAIL_ENABLED=true
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# Webhook通知
MONITORING_WEBHOOK_ENABLED=false
```

### 性能优化

1. **选择合适的监控级别**：生产环境推荐使用 `basic` 级别
2. **调整采集间隔**：根据需要调整指标采集频率
3. **配置数据保留**：设置合理的数据保留时间
4. **监控资源使用**：定期检查监控系统自身的资源占用

### 安全考虑

1. **邮件配置**：使用应用专用密码，不要使用主密码
2. **Webhook安全**：确保Webhook URL的安全性
3. **API访问**：考虑为监控API添加认证
4. **日志安全**：确保监控日志不包含敏感信息

## 故障排除

### 监控无法启动

1. 检查依赖是否安装：`pip install -r requirements.txt`
2. 检查配置是否正确：`python -c "from monitoring.config import MonitoringConfig; print(MonitoringConfig.from_env().validate())"`
3. 查看应用日志：`tail -f logs/app.log`

### 告警不工作

**内部邮件系统：**
1. 检查Postfix是否运行：`systemctl status postfix`
2. 测试sendmail：`which sendmail`
3. 运行邮件测试：`python test_internal_mail.py`
4. 查看邮件日志：`tail -f /var/log/mail.log`

**通用：**
1. 查看监控日志：`tail -f logs/monitoring.log`
2. 检查收件人邮箱地址

### 资源使用过高

1. 降低监控级别：设置 `MONITORING_LEVEL=basic`
2. 增加采集间隔：设置 `MONITORING_METRICS_INTERVAL=120`
3. 减少数据保留时间：设置 `MONITORING_METRICS_RETENTION_HOURS=12`

## 更多信息

- 监控配置参考：`monitoring_config.env.example`
- 测试脚本：`test_monitoring.py`
- 源代码：`monitoring/` 目录
