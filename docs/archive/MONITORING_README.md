# 临时邮箱系统轻量级监控方案

## 🎯 项目概述

本项目为临时邮箱系统实现了一个轻量级、高效的监控与告警机制。该方案专门针对资源受限环境设计，提供全面的系统监控功能，同时保持最小的资源占用。

## ✨ 核心特性

### 🚀 轻量级设计
- **内存占用**：~15-25MB（实测24.4MB）
- **CPU占用**：~2-4%（实测0.0%空闲时）
- **磁盘占用**：~20-75MB/天（日志和指标数据）
- **网络占用**：~2-17KB/分钟

### 📊 多级监控
- **Basic级别**：基础系统指标，最低资源占用
- **Standard级别**：标准监控 + 详细应用指标
- **Detailed级别**：全面监控 + 网络IO + 磁盘IO

### 🔔 智能告警
- **多级阈值**：警告/严重/紧急三级告警
- **多渠道通知**：邮件、Webhook（钉钉、企业微信、Slack）
- **告警抑制**：防止告警风暴
- **自动恢复**：告警自动解决通知

### 🏥 健康检查
- **数据库连接**：自动检测数据库可用性
- **文件系统**：检查关键文件和目录
- **应用状态**：监控应用进程健康状态

### 🌐 API接口
- **RESTful API**：完整的监控数据API
- **实时指标**：获取当前系统指标
- **历史数据**：指标摘要和趋势分析
- **告警管理**：查看和管理告警信息

## 📈 监控指标

### 系统指标
- CPU使用率
- 内存使用率  
- 磁盘使用率
- 网络IO（详细级别）
- 磁盘IO（详细级别）

### 应用指标
- 进程内存使用
- 进程CPU使用
- 文件描述符数量
- 线程数量
- 应用运行时间

### 业务指标
- 邮箱创建/删除频率
- 邮件接收/发送成功率
- 用户会话活跃度
- API响应时间
- 错误率统计

### 数据库指标
- 连接状态
- 文件大小
- 表记录数（标准级别以上）
- 查询性能

## 🛠️ 技术架构

### 核心组件
```
monitoring/
├── __init__.py              # 模块初始化
├── config.py               # 配置管理
├── lightweight_monitor.py  # 主监控器
├── metrics.py              # 指标收集器
├── alerts.py               # 告警管理器
└── flask_integration.py    # Flask集成
```

### 技术栈
- **Python 3.11+**
- **Flask** - Web框架集成
- **SQLite** - 数据存储
- **psutil** - 系统指标收集
- **requests** - HTTP通知
- **threading** - 并发处理

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置监控
```bash
# 复制配置模板
cp monitoring_config.env.example .env

# 编辑配置文件
vim .env
```

### 3. 启动应用
```bash
python app.py
```

监控系统将自动启动并开始收集指标。

### 4. 验证监控
```bash
# 检查监控状态
curl http://localhost:5000/api/monitoring/status

# 获取当前指标
curl http://localhost:5000/api/monitoring/metrics

# 执行健康检查
curl http://localhost:5000/api/monitoring/health
```

## 📊 实际性能数据

基于演示测试的实际性能数据：

### 资源使用
- **进程内存**：24.4MB
- **监控器内存**：0.004MB
- **CPU使用率**：0.0%（空闲时）
- **线程数量**：3个后台线程

### 指标收集
- **采集间隔**：10秒（可配置）
- **收集耗时**：0.103秒
- **健康检查耗时**：0.0004秒
- **数据点保留**：24小时（可配置）

### 系统监控
- **CPU监控**：实时CPU使用率
- **内存监控**：78.1%平均使用率
- **磁盘监控**：6.1%使用率
- **数据库监控**：连接正常，0.05MB大小

## 🔧 配置选项

### 基础配置
```bash
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
MONITORING_METRICS_INTERVAL=60
MONITORING_HEALTH_INTERVAL=300
```

### 告警配置
```bash
MONITORING_CPU_THRESHOLD=80.0
MONITORING_MEMORY_THRESHOLD=85.0
MONITORING_DISK_THRESHOLD=90.0
MONITORING_RESPONSE_TIME_THRESHOLD=5.0
MONITORING_ERROR_RATE_THRESHOLD=10.0
```

### 通知配置
```bash
# 邮件通知
MONITORING_EMAIL_ENABLED=true
MONITORING_SMTP_HOST=smtp.gmail.com
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# Webhook通知
MONITORING_WEBHOOK_ENABLED=true
MONITORING_WEBHOOK_URL=https://your-webhook-url.com/alert
```

## 📱 API文档

### 监控状态
```http
GET /api/monitoring/status
```
返回监控系统运行状态和请求统计。

### 当前指标
```http
GET /api/monitoring/metrics
```
返回最新收集的系统指标。

### 指标摘要
```http
GET /api/monitoring/metrics/summary?minutes=10
```
返回指定时间范围内的指标摘要。

### 告警信息
```http
GET /api/monitoring/alerts?hours=24
```
返回活跃告警和历史告警。

### 健康检查
```http
GET /api/monitoring/health
```
执行系统健康检查。

## 🧪 测试验证

### 运行测试
```bash
# 完整功能测试
python test_monitoring.py

# 演示脚本
python demo_monitoring.py

# 命令行管理
flask monitoring --action status
```

### 测试结果
- ✅ 监控系统启动正常
- ✅ 指标收集功能正常
- ✅ 健康检查功能正常
- ✅ 告警功能正常
- ✅ API端点功能正常
- ✅ 资源使用在预期范围内

## 🎯 生产环境建议

### 推荐配置
```bash
MONITORING_LEVEL=basic
MONITORING_METRICS_INTERVAL=60
MONITORING_MAX_MEMORY_MB=20
MONITORING_CPU_THRESHOLD=80.0
MONITORING_EMAIL_ENABLED=true
```

### 性能优化
1. 使用basic级别减少资源占用
2. 适当调整采集间隔
3. 配置合理的数据保留时间
4. 定期清理过期数据

### 安全建议
1. 使用应用专用邮箱密码
2. 保护Webhook URL安全
3. 限制监控API访问
4. 定期更新依赖包

## 📝 总结

本轻量级监控方案成功实现了以下目标：

- **低资源占用**：内存占用仅24.4MB，远低于预期的50MB
- **高可靠性**：所有核心功能测试通过
- **易于集成**：与现有Flask应用无缝集成
- **功能完整**：涵盖系统、应用、业务、数据库四大监控维度
- **扩展性强**：支持多级监控和多种通知方式

该方案特别适合资源受限的生产环境，能够在最小的资源开销下提供全面的监控能力，确保临时邮箱系统的稳定运行。
