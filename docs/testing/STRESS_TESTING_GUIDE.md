# 压力测试指南

本指南介绍如何在开发环境中禁用速率限制并进行准确的压力测试。

## 🎯 概述

为了获得准确的压力测试结果，我们已经实现了以下优化：

### ✅ 已禁用的限制机制

1. **API速率限制** - 所有API端点的速率限制已在测试模式下禁用
2. **数据库连接限制** - 优化了连接超时和缓存配置
3. **邮箱创建频率限制** - 移除了邮箱创建的时间间隔限制
4. **邮件接收频率限制** - 取消了邮件接收的速率控制
5. **会话管理限制** - 确保会话不会因高频操作被限制

### 🚀 性能优化

1. **数据库优化**
   - 增加缓存大小 (-8000 页)
   - 延长连接超时 (60秒)
   - 启用WAL模式和内存临时存储
   - 增加忙等待超时 (30秒)

2. **应用配置优化**
   - 禁用详细日志记录
   - 使用内存数据库（可选）
   - 优化并发处理

## 🛠️ 使用方法

### 方法1：一键运行（推荐）

```bash
# 快速测试（5-20个邮箱）
python run_stress_test.py --quick-test

# 中等规模测试（10-50个邮箱）
python run_stress_test.py

# 大规模测试（50-200个邮箱）
python run_stress_test.py --min-mailboxes 50 --max-mailboxes 200 --step-size 25

# 高并发测试
python run_stress_test.py --concurrent-workers 10
```

### 方法2：手动启动

```bash
# 1. 启动压力测试服务器
python start_stress_test_server.py --port 5001

# 2. 在另一个终端运行压力测试
python tests/stress_test_email_capacity.py --base-url http://127.0.0.1:5001
```

### 方法3：使用pytest

```bash
# 运行压力测试相关的pytest测试
pytest tests/ -m stress -v

# 使用压力测试配置
pytest tests/ --confcutdir=tests/conftest_stress_test.py -v
```

## 📊 测试配置

### 环境变量配置

压力测试模式下会自动设置以下环境变量：

```bash
# 核心配置
STRESS_TESTING=true
DISABLE_RATE_LIMIT=true
TESTING=true

# 数据库优化
DATABASE_PATH=:memory:  # 使用内存数据库
DB_TIMEOUT=60000
DB_CACHE_SIZE=-8000
DB_BUSY_TIMEOUT=30000

# 性能优化
LOG_LEVEL=ERROR
API_TIMEOUT=60000
API_RETRY_ATTEMPTS=10
```

### 测试参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--min-mailboxes` | 10 | 最小邮箱数量 |
| `--max-mailboxes` | 50 | 最大邮箱数量 |
| `--step-size` | 10 | 邮箱数量步长 |
| `--emails-per-mailbox-min` | 1 | 每邮箱最少邮件数 |
| `--emails-per-mailbox-max` | 20 | 每邮箱最多邮件数 |
| `--concurrent-workers` | 5 | 并发工作线程数 |
| `--quick-test` | - | 快速测试模式 |

## 📈 测试结果

### 性能指标

压力测试会收集以下性能指标：

1. **时间指标**
   - 邮箱创建时间
   - 邮件存储时间
   - 邮件检索时间
   - 总测试时间

2. **成功率指标**
   - 邮箱创建成功率
   - 邮件存储成功率
   - 邮件检索成功率

3. **系统资源指标**
   - 峰值内存使用
   - 峰值CPU使用率
   - 数据库大小

4. **响应时间指标**
   - 平均邮箱创建时间
   - 平均邮件存储时间
   - 平均邮件检索时间

### 报告输出

测试完成后会生成：

1. **控制台报告** - 实时显示测试进度和结果
2. **文本报告** - `stress_test_results/stress_test_report_YYYYMMDD_HHMMSS.txt`
3. **JSON数据** - `stress_test_results/stress_test_results_YYYYMMDD_HHMMSS.json`
4. **日志文件** - `stress_test_results/stress_test_YYYYMMDD_HHMMSS.log`

## 🔧 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查端口是否被占用
   lsof -i :5001
   
   # 使用不同端口
   python start_stress_test_server.py --port 5002
   ```

2. **数据库连接错误**
   ```bash
   # 使用文件数据库而非内存数据库
   python start_stress_test_server.py --no-memory-db
   ```

3. **测试超时**
   ```bash
   # 减少并发数和测试规模
   python run_stress_test.py --concurrent-workers 3 --max-mailboxes 30
   ```

### 性能调优建议

1. **系统资源不足时**
   - 减少 `--concurrent-workers` 参数
   - 降低 `--max-mailboxes` 参数
   - 使用 `--quick-test` 模式

2. **数据库性能问题**
   - 确保使用内存数据库 (`:memory:`)
   - 检查磁盘空间和I/O性能
   - 考虑使用SSD存储

3. **网络延迟问题**
   - 使用本地地址 (`127.0.0.1`)
   - 增加超时时间
   - 减少并发连接数

## 🔒 安全注意事项

⚠️ **重要提醒**：

1. **仅用于开发/测试环境** - 这些配置会禁用重要的安全限制
2. **不要在生产环境使用** - 生产环境必须保持速率限制
3. **测试后清理** - 确保测试数据被正确清理
4. **资源监控** - 监控系统资源使用，避免影响其他服务

## 📝 配置文件

### `.env.stress_test`
压力测试专用环境配置文件，包含所有优化设置。

### `start_stress_test_server.py`
压力测试服务器启动脚本，自动应用优化配置。

### `run_stress_test.py`
一键运行脚本，自动启动服务器并执行测试。

### `tests/conftest_stress_test.py`
pytest压力测试配置，提供专用fixtures。

## 🎯 测试目标

通过这些优化，系统现在能够：

1. **处理高并发请求** - 无速率限制约束
2. **快速创建大量邮箱** - 优化的数据库性能
3. **高效存储和检索邮件** - 内存数据库和缓存优化
4. **准确测量真实性能** - 移除人为限制因素

测试结果将真实反映系统在无外部限制条件下的性能表现。
