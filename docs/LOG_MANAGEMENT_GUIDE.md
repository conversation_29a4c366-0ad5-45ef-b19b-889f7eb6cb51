# 临时邮箱系统日志管理指南

## 概述

本文档详细介绍临时邮箱系统的统一日志轮转方案，包括配置、监控、维护和故障排除。

## 日志文件结构

### 主要日志文件

```
logs/
├── app.log                 # 应用程序日志（中频）
├── mail_handler.log        # 邮件处理日志（高频）
├── monitoring.log          # 监控系统日志（低频）
├── gunicorn_access.log     # Web服务器访问日志（中频）
├── gunicorn_error.log      # Web服务器错误日志（中频）
├── health-check.log        # 健康检查日志（低频）
├── logrotate.log          # 日志轮转记录
└── log_monitor.log        # 监控脚本日志

temp_logs/
└── *.log                  # 临时日志文件
```

## 轮转策略

### 分层轮转策略

| 日志类型 | 文件 | 轮转频率 | 保留期 | 大小触发 |
|---------|------|---------|--------|----------|
| 高频日志 | mail_handler.log | 每日 | 14天 | 50MB |
| 中频日志 | app.log, gunicorn_*.log | 每日 | 30天 | 100MB |
| 低频日志 | monitoring.log, health-check.log | 每周 | 52周 | 10MB |
| 临时日志 | temp_logs/*.log | 每日 | 3天 | - |

### 轮转特性

- **压缩**: 使用gzip压缩旧日志文件
- **延迟压缩**: 避免影响正在写入的日志
- **日期后缀**: 使用日期时间戳作为文件后缀
- **权限管理**: 保持正确的文件权限
- **服务通知**: 轮转后通知服务重新打开日志文件

## 安装和配置

### 1. 初始设置

```bash
# 创建必要的目录和文件
sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh setup

# 安装logrotate配置
sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh install

# 测试配置
sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh test
```

### 2. 设置Cron任务

```bash
# 编辑root用户的crontab
sudo crontab -e

# 添加以下内容（参考 config/templates/logrotate-cron.conf）
0 2 * * * /usr/sbin/logrotate /etc/logrotate.d/tempmail >/dev/null 2>&1
0 */4 * * * /var/www/tempmail/scripts/maintenance/log_rotation_monitor.py >/dev/null 2>&1
0 3 * * 0 /var/www/tempmail/scripts/maintenance/logrotate_manager.sh cleanup >/dev/null 2>&1
```

### 3. 配置监控告警

编辑 `config/log_monitor_config.json` 文件来自定义监控阈值和告警设置：

```json
{
  "thresholds": {
    "high_frequency_logs": {
      "size_warning": "40M",
      "size_critical": "80M",
      "files": ["mail_handler.log"]
    }
  },
  "alerts": {
    "email_enabled": true,
    "email_recipients": ["<EMAIL>"],
    "cooldown_minutes": 60
  }
}
```

## 日常维护

### 管理脚本使用

```bash
# 查看日志状态
/var/www/tempmail/scripts/maintenance/logrotate_manager.sh status

# 监控轮转状态
/var/www/tempmail/scripts/maintenance/logrotate_manager.sh monitor

# 手动执行轮转（测试）
sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh force

# 清理超期日志
sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh cleanup
```

### 监控脚本使用

```bash
# 运行完整监控检查
/var/www/tempmail/scripts/maintenance/log_rotation_monitor.py

# 仅生成报告（不发送告警）
/var/www/tempmail/scripts/maintenance/log_rotation_monitor.py --no-alerts

# 生成JSON格式报告
/var/www/tempmail/scripts/maintenance/log_rotation_monitor.py --report-only
```

## 监控和告警

### 监控指标

1. **文件大小监控**
   - 警告阈值：根据日志类型设定
   - 临界阈值：防止磁盘空间耗尽

2. **轮转状态监控**
   - 检查logrotate状态文件
   - 验证轮转文件存在性
   - 监控轮转频率

3. **磁盘使用监控**
   - 警告阈值：80%
   - 临界阈值：90%

### 告警机制

- **邮件告警**: 自动发送到配置的收件人
- **冷却期**: 防止告警邮件轰炸（默认60分钟）
- **分级告警**: 区分警告和严重级别

## 故障排除

### 常见问题

#### 1. 日志轮转不工作

**症状**: 日志文件持续增长，没有轮转文件生成

**排查步骤**:
```bash
# 检查logrotate配置
sudo logrotate -d /etc/logrotate.d/tempmail

# 检查logrotate状态
sudo cat /var/lib/logrotate/status | grep tempmail

# 手动执行轮转
sudo logrotate -f /etc/logrotate.d/tempmail

# 检查系统日志
sudo journalctl -u logrotate
```

**可能原因**:
- 配置文件语法错误
- 文件权限问题
- 磁盘空间不足
- 服务未正确重载

#### 2. 权限问题

**症状**: 轮转后无法写入新日志文件

**解决方法**:
```bash
# 检查文件权限
ls -la /var/www/tempmail/logs/

# 修复权限
sudo chown -R admin:tempmail /var/www/tempmail/logs/
sudo chmod 664 /var/www/tempmail/logs/*.log
```

#### 3. 磁盘空间不足

**症状**: 轮转失败，系统告警

**解决方法**:
```bash
# 检查磁盘使用
df -h /var/www/tempmail/logs/

# 清理超期日志
sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh cleanup

# 手动删除大文件
find /var/www/tempmail/logs/ -name "*.log.*.gz" -mtime +60 -delete
```

#### 4. 服务未重载

**症状**: 轮转后服务仍写入旧文件

**解决方法**:
```bash
# 检查服务状态
systemctl status tempmail tempmail-optimized

# 手动重载服务
sudo systemctl reload tempmail
sudo systemctl reload tempmail-optimized
```

### 日志分析

#### 查看轮转记录
```bash
# 查看轮转日志
tail -f /var/www/tempmail/logs/logrotate.log

# 查看监控日志
tail -f /var/www/tempmail/logs/log_monitor.log
```

#### 分析大文件
```bash
# 找出最大的日志文件
find /var/www/tempmail/logs/ -name "*.log" -type f -exec ls -lh {} \; | sort -k5 -hr

# 分析文件增长趋势
stat /var/www/tempmail/logs/mail_handler.log
```

## 性能优化

### 减少日志输出

1. **调整日志级别**: 在生产环境中使用INFO或WARNING级别
2. **过滤重复日志**: 避免记录重复的信息
3. **异步日志**: 使用异步日志写入减少I/O阻塞

### 优化轮转策略

1. **调整轮转频率**: 根据实际使用情况调整
2. **压缩策略**: 平衡压缩率和CPU使用
3. **保留期优化**: 根据合规要求和存储容量调整

## 备份和恢复

### 日志备份

```bash
# 创建日志备份
tar -czf logs_backup_$(date +%Y%m%d).tar.gz /var/www/tempmail/logs/

# 定期备份重要日志
rsync -av /var/www/tempmail/logs/ /backup/tempmail/logs/
```

### 恢复日志

```bash
# 恢复日志文件
tar -xzf logs_backup_20250611.tar.gz -C /

# 修复权限
sudo chown -R admin:tempmail /var/www/tempmail/logs/
```

## 最佳实践

1. **定期检查**: 每周检查日志轮转状态
2. **监控告警**: 及时响应监控告警
3. **容量规划**: 根据业务增长预估日志容量
4. **安全考虑**: 保护敏感日志信息
5. **文档更新**: 及时更新配置变更文档

## 相关文件

- 配置文件: `config/tempmail-logrotate.conf`
- 管理脚本: `scripts/maintenance/logrotate_manager.sh`
- 监控脚本: `scripts/maintenance/log_rotation_monitor.py`
- Cron配置: `config/templates/logrotate-cron.conf`
- 监控配置: `config/log_monitor_config.json`

## 联系支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查相关日志文件
3. 联系系统管理员
