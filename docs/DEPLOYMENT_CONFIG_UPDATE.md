# 部署配置更新说明

## 内存监控配置优化 (Commit: 11aad08)

### 📋 **需要手动更新的配置**

由于`.env`文件包含敏感信息已被gitignore，在部署此次更新时需要手动更新以下配置：

```bash
# 在 .env 文件中更新
MONITORING_MAX_MEMORY_MB=50  # 从原来的20调整为50
```

### 🔧 **部署步骤**

1. **拉取代码更新**
   ```bash
   cd /var/www/tempmail
   git pull origin develop-refresh
   ```

2. **更新环境配置**
   ```bash
   # 编辑 .env 文件
   nano .env
   
   # 确保包含以下配置
   MONITORING_MAX_MEMORY_MB=50
   MONITORING_MEMORY_THRESHOLD=85.0
   ```

3. **重启服务**
   ```bash
   sudo systemctl restart tempmail-optimized
   ```

4. **验证配置**
   ```bash
   # 检查服务状态
   systemctl status tempmail-optimized
   
   # 观察监控日志
   tail -f logs/monitoring.log
   ```

### 📊 **配置变更详情**

| 配置项 | 原值 | 新值 | 说明 |
|--------|------|------|------|
| `MONITORING_MAX_MEMORY_MB` | 20 | 50 | 基础内存限制 |
| 应用内存告警阈值 | 40MB | 150MB | 实际告警触发点 |
| 告警算法 | `配置值 × 2` | `max(配置值 × 3, 100)` | 更合理的计算方式 |

### ✅ **预期效果**

- **减少误报**: 43MB正常内存使用不再触发告警
- **保持监控**: 真正的内存问题(>150MB)仍会被检测
- **提高效率**: 显著减少不必要的告警邮件

### 🧪 **测试验证**

使用新增的测试工具验证配置：
```bash
python3 scripts/diagnostics/memory_monitoring_test.py
```

### 📚 **相关文档**

- 详细优化指南: `docs/MEMORY_MONITORING_OPTIMIZATION_GUIDE.md`
- 测试工具: `scripts/diagnostics/memory_monitoring_test.py`

---

**更新日期**: 2025-06-12  
**Commit Hash**: 11aad0838eb1e52dd5364bbfad3cdc0e08822780  
**分支**: develop-refresh
