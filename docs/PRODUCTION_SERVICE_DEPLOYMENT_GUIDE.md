# 生产级服务部署完整指南

## 🎯 概述

您已经有一个运行中的 `tempmail.service`，本指南将帮您：
1. 保持现有服务稳定运行
2. 部署优化版服务进行测试
3. 选择最适合的部署方案
4. 实现生产级进程管理

## 📊 当前状态分析

### 现有服务 (tempmail.service)
- ✅ **状态**：运行中，3天稳定运行
- ✅ **配置**：使用Unix Socket，3个工作进程
- ✅ **用户**：www-data (标准Web服务用户)
- ⚠️ **优化空间**：未使用应用级优化配置

### 优化机会
- 🔧 集成Gunicorn优化配置
- 📊 添加详细监控和健康检查
- 🛡️ 增强安全设置和资源限制
- 📝 改进日志管理和轮转

## 🚀 部署方案选择

### 方案1：保守升级 (推荐)
**适用场景**：生产环境，需要最小风险
```bash
# 更新现有服务以使用优化配置
./manage_tempmail_service.sh update-current

# 重启服务应用更改
sudo systemctl restart tempmail
```

### 方案2：并行部署
**适用场景**：需要测试优化效果
```bash
# 安装优化服务（端口8080）
./manage_tempmail_service.sh install-optimized

# 启动优化服务进行测试
sudo systemctl start tempmail-optimized
```

### 方案3：完全切换
**适用场景**：确认优化效果后的正式切换
```bash
# 切换到优化服务
./manage_tempmail_service.sh switch-optimized
```

## 📋 详细实施步骤

### 步骤1：准备工作

1. **备份当前配置**：
```bash
# 备份服务文件
sudo cp /etc/systemd/system/tempmail.service /etc/systemd/system/tempmail.service.backup

# 备份数据库
cp database/tempmail.db database/tempmail.db.backup.$(date +%Y%m%d)
```

2. **验证当前状态**：
```bash
./manage_tempmail_service.sh status
./manage_tempmail_service.sh health
```

### 步骤2：选择部署方案

#### 方案1实施：保守升级
```bash
# 1. 更新服务配置
./manage_tempmail_service.sh update-current

# 2. 重启服务
sudo systemctl restart tempmail

# 3. 验证服务状态
./manage_tempmail_service.sh health

# 4. 检查日志
./manage_tempmail_service.sh logs tempmail
```

#### 方案2实施：并行部署
```bash
# 1. 安装优化服务
./manage_tempmail_service.sh install-optimized

# 2. 启动优化服务
sudo systemctl start tempmail-optimized

# 3. 测试两个服务
curl -I http://localhost:8080/  # 优化服务
# 原服务通过Nginx代理访问

# 4. 性能对比测试
python verify_optimizations.py --url http://localhost:8080
```

#### 方案3实施：完全切换
```bash
# 1. 确保优化服务已安装
./manage_tempmail_service.sh install-optimized

# 2. 执行切换
./manage_tempmail_service.sh switch-optimized

# 3. 验证切换结果
./manage_tempmail_service.sh status
./manage_tempmail_service.sh health

# 4. 如需回滚
./manage_tempmail_service.sh switch-original
```

### 步骤3：配置日志轮转

```bash
# 安装日志轮转配置
sudo cp tempmail-logrotate.conf /etc/logrotate.d/tempmail

# 测试日志轮转配置
sudo logrotate -d /etc/logrotate.d/tempmail

# 手动执行日志轮转测试
sudo logrotate -f /etc/logrotate.d/tempmail
```

### 步骤4：设置监控和告警

```bash
# 创建健康检查脚本
cat > /usr/local/bin/tempmail-health-check.sh << 'EOF'
#!/bin/bash
cd /var/www/tempmail
./manage_tempmail_service.sh health > /tmp/tempmail-health.log 2>&1
if [ $? -ne 0 ]; then
    echo "Tempmail service health check failed" | logger -t tempmail-monitor
fi
EOF

chmod +x /usr/local/bin/tempmail-health-check.sh

# 添加到crontab（每5分钟检查一次）
echo "*/5 * * * * /usr/local/bin/tempmail-health-check.sh" | sudo crontab -
```

## 🔧 服务管理命令

### 基础管理
```bash
# 查看所有服务状态
./manage_tempmail_service.sh status

# 健康检查
./manage_tempmail_service.sh health

# 查看日志
./manage_tempmail_service.sh logs [service-name]
```

### 服务控制
```bash
# 启动/停止/重启服务
sudo systemctl start tempmail
sudo systemctl stop tempmail
sudo systemctl restart tempmail

# 重新加载配置（优雅重启）
sudo systemctl reload tempmail

# 查看服务状态
sudo systemctl status tempmail
```

### 日志管理
```bash
# 查看实时日志
sudo journalctl -u tempmail -f

# 查看最近的错误
sudo journalctl -u tempmail -p err

# 查看特定时间段的日志
sudo journalctl -u tempmail --since "2025-06-11 10:00:00"
```

## 📊 性能监控

### 关键指标监控
```bash
# 内存使用
systemctl status tempmail | grep Memory

# CPU使用
top -p $(pgrep -f "gunicorn.*app:app" | tr '\n' ',' | sed 's/,$//')

# 连接数监控
ss -tlnp | grep tempmail

# 响应时间监控
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/
```

### 自动化监控脚本
```bash
# 创建监控脚本
cat > monitor_tempmail.sh << 'EOF'
#!/bin/bash
echo "=== $(date) ==="
echo "Service Status:"
systemctl is-active tempmail tempmail-optimized

echo "Memory Usage:"
systemctl status tempmail tempmail-optimized | grep Memory

echo "Process Count:"
pgrep -f "gunicorn.*app:app" | wc -l

echo "Response Test:"
curl -s -o /dev/null -w "Response Time: %{time_total}s\n" http://localhost:8080/ 2>/dev/null || echo "Service not responding"
echo "========================"
EOF

chmod +x monitor_tempmail.sh
```

## 🛡️ 安全最佳实践

### 服务安全
- ✅ 使用非特权用户运行
- ✅ 限制文件系统访问
- ✅ 设置资源限制
- ✅ 启用进程隔离

### 网络安全
```bash
# 如果使用HTTP端口，配置防火墙
sudo ufw allow 8080/tcp  # 仅在需要时开放

# 配置Nginx反向代理（推荐）
# 将在后续指南中详细说明
```

## 🔄 回滚计划

### 快速回滚
```bash
# 如果优化服务有问题，立即切换回原服务
./manage_tempmail_service.sh switch-original
```

### 配置回滚
```bash
# 恢复原始服务配置
sudo cp /etc/systemd/system/tempmail.service.backup /etc/systemd/system/tempmail.service
sudo systemctl daemon-reload
sudo systemctl restart tempmail
```

## 📈 性能优化建议

### 根据负载调整
- **轻负载**：保持2-3个工作进程
- **中等负载**：增加到4-6个工作进程
- **高负载**：考虑水平扩展或负载均衡

### 资源优化
```bash
# 调整工作进程数
# 编辑 gunicorn.conf.py 或服务文件中的 --workers 参数

# 监控资源使用
htop
iotop
```

## 🎯 推荐部署路径

### 对于生产环境：
1. **第一阶段**：使用方案1（保守升级）
2. **第二阶段**：部署方案2进行A/B测试
3. **第三阶段**：确认效果后执行方案3

### 对于测试环境：
- 直接使用方案2或方案3进行测试

## 📞 故障排除

### 常见问题
1. **服务启动失败**：检查日志 `./manage_tempmail_service.sh logs`
2. **权限问题**：确认用户和组设置正确
3. **端口冲突**：检查端口占用情况
4. **配置错误**：验证配置文件语法

### 紧急联系
- 查看系统日志：`sudo journalctl -xe`
- 检查磁盘空间：`df -h`
- 检查内存使用：`free -h`

---

## 📚 相关文档

- [应用级优化指南](docs/APPLICATION_OPTIMIZATION_GUIDE.md)
- [优化实施总结](OPTIMIZATION_IMPLEMENTATION_SUMMARY.md)
- [监控系统指南](docs/MONITORING_SYSTEM_COMPLETE_GUIDE.md)
