#!/bin/bash

# 临时邮箱服务管理脚本
# 管理现有服务和优化服务的切换、监控和维护

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
CURRENT_SERVICE="tempmail"
OPTIMIZED_SERVICE="tempmail-optimized"
PROJECT_ROOT="/var/www/tempmail"
SERVICE_DIR="/etc/systemd/system"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查权限
check_sudo() {
    if ! sudo -n true 2>/dev/null; then
        log_error "此操作需要sudo权限"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    echo -e "${BLUE}=== 临时邮箱服务状态 ===${NC}"
    echo ""
    
    # 检查现有服务
    echo -e "${BLUE}1. 现有服务 (tempmail):${NC}"
    if systemctl is-active --quiet $CURRENT_SERVICE; then
        echo -e "   状态: ${GREEN}运行中${NC}"
        echo -e "   端口: Unix Socket (/run/tempmail/tempemail.sock)"
        systemctl status $CURRENT_SERVICE --no-pager -l | head -10
    else
        echo -e "   状态: ${RED}未运行${NC}"
    fi
    
    echo ""
    
    # 检查优化服务
    echo -e "${BLUE}2. 优化服务 (tempmail-optimized):${NC}"
    if systemctl is-active --quiet $OPTIMIZED_SERVICE 2>/dev/null; then
        echo -e "   状态: ${GREEN}运行中${NC}"
        echo -e "   端口: HTTP 5001"
        systemctl status $OPTIMIZED_SERVICE --no-pager -l | head -10
    else
        echo -e "   状态: ${RED}未运行/未安装${NC}"
    fi
    
    echo ""
    
    # 显示端口占用
    echo -e "${BLUE}3. 端口占用情况:${NC}"
    netstat -tlnp 2>/dev/null | grep -E ":500[01]|tempmail" || echo "   无相关端口监听"
    
    echo ""
    
    # 显示进程信息
    echo -e "${BLUE}4. 相关进程:${NC}"
    ps aux | grep -E "gunicorn.*app:app" | grep -v grep || echo "   无Gunicorn进程"
}

# 安装优化服务
install_optimized() {
    log_info "安装优化版服务..."
    
    check_sudo
    
    # 检查服务文件是否存在
    if [ ! -f "$PROJECT_ROOT/tempmail-optimized.service" ]; then
        log_error "优化服务文件不存在: $PROJECT_ROOT/tempmail-optimized.service"
        exit 1
    fi
    
    # 复制服务文件
    log_info "复制服务文件到系统目录..."
    sudo cp "$PROJECT_ROOT/tempmail-optimized.service" "$SERVICE_DIR/"
    
    # 重新加载systemd
    log_info "重新加载systemd配置..."
    sudo systemctl daemon-reload
    
    # 启用服务
    log_info "启用优化服务..."
    sudo systemctl enable $OPTIMIZED_SERVICE
    
    log_info "优化服务安装完成"
}

# 切换到优化服务
switch_to_optimized() {
    log_info "切换到优化服务..."
    
    check_sudo
    
    # 安装优化服务（如果未安装）
    if [ ! -f "$SERVICE_DIR/$OPTIMIZED_SERVICE.service" ]; then
        install_optimized
    fi
    
    # 停止现有服务
    log_info "停止现有服务..."
    sudo systemctl stop $CURRENT_SERVICE
    
    # 启动优化服务
    log_info "启动优化服务..."
    sudo systemctl start $OPTIMIZED_SERVICE
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if systemctl is-active --quiet $OPTIMIZED_SERVICE; then
        log_info "✅ 优化服务启动成功"
        log_info "服务现在运行在端口5001"
        log_info "可以通过 http://localhost:5001 访问"
    else
        log_error "❌ 优化服务启动失败"
        log_info "正在恢复原服务..."
        sudo systemctl start $CURRENT_SERVICE
        exit 1
    fi
}

# 切换回原服务
switch_to_original() {
    log_info "切换回原服务..."
    
    check_sudo
    
    # 停止优化服务
    if systemctl is-active --quiet $OPTIMIZED_SERVICE 2>/dev/null; then
        log_info "停止优化服务..."
        sudo systemctl stop $OPTIMIZED_SERVICE
    fi
    
    # 启动原服务
    log_info "启动原服务..."
    sudo systemctl start $CURRENT_SERVICE
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if systemctl is-active --quiet $CURRENT_SERVICE; then
        log_info "✅ 原服务启动成功"
        log_info "服务现在运行在Unix Socket"
    else
        log_error "❌ 原服务启动失败"
        exit 1
    fi
}

# 更新现有服务配置
update_current_service() {
    log_info "更新现有服务以使用优化配置..."
    
    check_sudo
    
    # 备份现有服务文件
    log_info "备份现有服务配置..."
    sudo cp "$SERVICE_DIR/$CURRENT_SERVICE.service" "$SERVICE_DIR/$CURRENT_SERVICE.service.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 创建更新后的服务配置
    cat > /tmp/tempmail-updated.service << 'EOF'
[Unit]
Description=Temporary Email System - Updated Gunicorn WSGI Server
After=network.target

[Service]
Type=notify
User=admin
Group=tempmail
WorkingDirectory=/var/www/tempmail

# 环境变量
Environment=PATH=/var/www/tempmail/venv/bin
Environment=PYTHONPATH=/var/www/tempmail
Environment=FLASK_APP=app.py
Environment=FLASK_ENV=production
EnvironmentFile=-/var/www/tempmail/.env

# 运行时目录
RuntimeDirectory=tempmail
RuntimeDirectoryMode=0750

# 使用优化配置，但保持Unix Socket
ExecStart=/var/www/tempmail/venv/bin/gunicorn \
    --config /var/www/tempmail/gunicorn.conf.py \
    --bind unix:/run/tempmail/tempemail.sock \
    --pid /run/tempmail/gunicorn.pid \
    app:app

# 改进的重启策略
Restart=always
RestartSec=3
StartLimitInterval=60s
StartLimitBurst=3

# 优雅停止
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
ExecReload=/bin/kill -HUP $MAINPID

# 基础安全设置
NoNewPrivileges=true
PrivateTmp=true
ReadWritePaths=/var/www/tempmail/logs /var/www/tempmail/database /var/www/tempmail/run

# 资源限制
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

    # 应用更新
    sudo cp /tmp/tempmail-updated.service "$SERVICE_DIR/$CURRENT_SERVICE.service"
    sudo systemctl daemon-reload
    
    log_info "服务配置已更新，需要重启服务以生效"
    log_warn "运行 'sudo systemctl restart tempmail' 来应用更改"
}

# 服务日志查看
view_logs() {
    local service=${1:-$CURRENT_SERVICE}
    
    echo -e "${BLUE}=== $service 服务日志 ===${NC}"
    echo ""
    
    # 显示systemd日志
    echo -e "${BLUE}1. Systemd日志 (最近50行):${NC}"
    sudo journalctl -u $service -n 50 --no-pager
    
    echo ""
    
    # 显示应用日志
    echo -e "${BLUE}2. 应用日志:${NC}"
    if [ -f "$PROJECT_ROOT/logs/app.log" ]; then
        tail -20 "$PROJECT_ROOT/logs/app.log"
    else
        echo "应用日志文件不存在"
    fi
    
    echo ""
    
    # 显示Gunicorn日志
    echo -e "${BLUE}3. Gunicorn日志:${NC}"
    if [ -f "$PROJECT_ROOT/logs/gunicorn_error.log" ]; then
        tail -20 "$PROJECT_ROOT/logs/gunicorn_error.log"
    else
        echo "Gunicorn错误日志文件不存在"
    fi
}

# 健康检查
health_check() {
    echo -e "${BLUE}=== 服务健康检查 ===${NC}"
    echo ""
    
    # 检查服务状态
    for service in $CURRENT_SERVICE $OPTIMIZED_SERVICE; do
        echo -e "${BLUE}检查 $service:${NC}"
        if systemctl is-active --quiet $service 2>/dev/null; then
            echo -e "  状态: ${GREEN}运行中${NC}"
            
            # 检查端口/Socket
            if [ "$service" = "$CURRENT_SERVICE" ]; then
                if sudo test -S "/run/tempmail/tempemail.sock" 2>/dev/null; then
                    echo -e "  Socket: ${GREEN}正常${NC}"
                else
                    echo -e "  Socket: ${RED}异常或无权限检查${NC}"
                fi
            else
                if netstat -tlnp 2>/dev/null | grep -q ":5001.*LISTEN"; then
                    echo -e "  端口5001: ${GREEN}监听中${NC}"
                    
                    # HTTP健康检查
                    if curl -f http://localhost:5001/ > /dev/null 2>&1; then
                        echo -e "  HTTP响应: ${GREEN}正常${NC}"
                    else
                        echo -e "  HTTP响应: ${RED}异常${NC}"
                    fi
                else
                    echo -e "  端口5001: ${RED}未监听${NC}"
                fi
            fi
        else
            echo -e "  状态: ${RED}未运行${NC}"
        fi
        echo ""
    done
}

# 显示帮助
show_help() {
    echo "临时邮箱服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  status              显示所有服务状态"
    echo "  install-optimized   安装优化版服务"
    echo "  switch-optimized    切换到优化服务"
    echo "  switch-original     切换回原服务"
    echo "  update-current      更新现有服务配置"
    echo "  logs [service]      查看服务日志"
    echo "  health              健康检查"
    echo "  help                显示此帮助"
    echo ""
    echo "示例:"
    echo "  $0 status"
    echo "  $0 switch-optimized"
    echo "  $0 logs tempmail"
    echo "  $0 health"
}

# 主函数
main() {
    case "${1:-status}" in
        status)
            show_status
            ;;
        install-optimized)
            install_optimized
            ;;
        switch-optimized)
            switch_to_optimized
            ;;
        switch-original)
            switch_to_original
            ;;
        update-current)
            update_current_service
            ;;
        logs)
            view_logs "$2"
            ;;
        health)
            health_check
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
