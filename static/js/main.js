document.addEventListener('DOMContentLoaded', () => {
    // 环境检查
    const isDevelopment = window.appConfig && window.appConfig.ENV === 'development';

    // 初始化国际化
    if (window.i18n) {
        // 设置语言选择器的当前值
        const languageSelector = document.getElementById('language-selector');
        if (languageSelector) {
            languageSelector.value = window.i18n.getCurrentLanguage();
            languageSelector.addEventListener('change', (e) => {
                window.i18n.setLanguage(e.target.value);
            });
        }

        // 初始化页面文本
        window.i18n.updatePageTexts();
    }

    // DOM 元素获取
    const emailAddressEl = document.getElementById('email-address');
    const copyBtn = document.getElementById('copy-btn');
    const refreshBtn = document.getElementById('refresh-btn');
    const newEmailBtn = document.getElementById('new-email-btn');
    const customEmailBtn = document.getElementById('custom-email-btn'); // 添加自定义按钮引用
    const deleteEmailBtn = document.getElementById('delete-email-btn'); // 添加删除按钮引用
    const emailList = document.getElementById('email-list') || document.getElementById('inbox');
    const focusStatus = document.getElementById('focus-status');
    const visibilityStatus = document.getElementById('visibility-status');
    const pollingInterval = document.getElementById('polling-interval');

    // 应用状态变量
    let currentEmail = null;
    let lastReceivedTimestamp = null;
    let pollingTimer = null;
    let isPollingActive = true;
    let normalRefreshInterval = 10000;
    let reducedRefreshInterval = 30000;
    let currentRefreshInterval = normalRefreshInterval;
    const STORAGE_KEY = 'temp_email_data';

    // 会话管理
    let sessionId = null;
    const SESSION_STORAGE_KEY = 'temp_email_session_id';

    // 添加邮件列表状态管理
    let currentEmailsCache = []; // 缓存当前的邮件列表
    let hasReceivedEmails = false; // 标记是否已经收到过邮件

    // 工具函数
    function escapeHtml(unsafe) {
        // 转义HTML内容，防止XSS攻击
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 会话管理函数
    function getOrCreateSessionId() {
        // 尝试从localStorage获取现有的sessionId（修复：改为localStorage以保持会话持久性）
        let storedSessionId = localStorage.getItem(SESSION_STORAGE_KEY);

        if (!storedSessionId) {
            // 生成新的sessionId
            storedSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem(SESSION_STORAGE_KEY, storedSessionId);
            console.log('生成新的会话ID:', storedSessionId);
        } else {
            console.log('使用现有会话ID:', storedSessionId);
        }

        sessionId = storedSessionId;
        return sessionId;
    }

    function clearSession() {
        localStorage.removeItem(SESSION_STORAGE_KEY);
        sessionId = null;
        console.log('会话已清除');
    }

    function validateEmailPrefix(prefix) {
        // 验证邮箱前缀
        if (!prefix) {
            const errorText = window.i18n ? window.i18n.t('error.prefix_empty') : "前缀不能为空";
            return { valid: false, error: errorText };
        }

        if (prefix.length > 20) {
            const errorText = window.i18n ? window.i18n.t('error.prefix_too_long') : "前缀长度不能超过20个字符";
            return { valid: false, error: errorText };
        }

        const prefixRegex = /^[a-zA-Z0-9\-]{1,20}$/;
        if (!prefixRegex.test(prefix)) {
            const errorText = window.i18n ? window.i18n.t('error.prefix_invalid') : "前缀只能包含字母、数字和连字符";
            return { valid: false, error: errorText };
        }

        return { valid: true };
    }

    // 会话数据管理
    function getFromStorage() {
        try {
            const storedData = localStorage.getItem(STORAGE_KEY);
            if (!storedData) return null;

            const data = JSON.parse(storedData);
            if (!data || !data.address || !data.expires_at) {
                console.error('Invalid storage data format');
                localStorage.removeItem(STORAGE_KEY);
                return null;
            }

            const expiresAt = new Date(data.expires_at);
            const now = new Date();
            if (isNaN(expiresAt.getTime())) {
                console.error('Invalid expiration date in storage');
                localStorage.removeItem(STORAGE_KEY);
                return null;
            }

            if (now >= expiresAt) {
                console.log('Email address has expired');
                localStorage.removeItem(STORAGE_KEY);
                return null;
            }
            return data;
        } catch (e) {
            console.error('存储数据读取错误:', e);
            localStorage.removeItem(STORAGE_KEY);
            return null;
        }
    }

    // 错误提示函数
    function showError(msg, autoHide = true) {
        const errorDiv = document.createElement('div');
        errorDiv.id = 'error-message';
        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4 flex items-center shadow-md transition-opacity duration-300';

        const iconSpan = document.createElement('span');
        iconSpan.className = 'mr-2 text-red-500';
        iconSpan.innerHTML = '<i class="fas fa-exclamation-circle"></i>';
        errorDiv.appendChild(iconSpan);

        const msgSpan = document.createElement('span');
        msgSpan.className = 'flex-grow';
        msgSpan.textContent = msg;
        errorDiv.appendChild(msgSpan);

        const closeBtn = document.createElement('button');
        closeBtn.className = 'ml-2 text-red-500 hover:text-red-700 focus:outline-none';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.onclick = function() {
            errorDiv.classList.add('opacity-0');
            setTimeout(() => errorDiv.remove(), 300);
        };
        errorDiv.appendChild(closeBtn);

        const existingError = document.getElementById('error-message');
        if (existingError) {
            existingError.remove();
        }

        const emailBox = emailAddressEl.closest('.mb-8');
        if (emailBox) {
            emailBox.appendChild(errorDiv);
        } else {
            document.querySelector('main').appendChild(errorDiv);
        }

        console.error(msg);

        if (autoHide) {
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.classList.add('opacity-0');
                    setTimeout(() => {
                        if (errorDiv.parentNode) {
                            errorDiv.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }
    }

    // 欢迎信息显示
    function showWelcomeMessage() {
        if (!emailList) return;

        const noMessages = document.getElementById('no-messages');
        if (noMessages) {
            noMessages.style.display = 'none';
        }

        const formattedTime = '2 min ago';

        const welcomeTitle = window.i18n ? window.i18n.t('welcome.title') : '欢迎使用临时邮箱';
        const welcomeMessage = window.i18n ? window.i18n.t('welcome.message') : '感谢您使用我们的临时邮箱服务。您的邮箱地址将在24小时后过期。';

        emailList.innerHTML = `
            <div class="email-item p-4 border rounded-lg shadow-sm bg-blue-50 hover:bg-blue-100 cursor-pointer mb-3">
                <div class="flex justify-between items-start">
                    <div class="font-medium text-blue-600">${welcomeTitle}</div>
                    <div class="text-xs text-gray-500">${formattedTime}</div>
                </div>
                <div class="text-sm mt-2 text-gray-700">
                    ${welcomeMessage}
                </div>
            </div>
        `;
        console.log("Welcome message displayed");
    }

    // 创建邮件项DOM元素
    function createEmailItem(email) {
        const emailItem = document.createElement('div');
        emailItem.className = 'email-item p-3 border-b hover:bg-gray-50 cursor-pointer haptic-feedback transition-transform duration-200';
        emailItem.dataset.id = email.id;

        const formattedDate = new Date(email.received_at).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(/\//g, '-');

        const noSubject = window.i18n ? window.i18n.t('email.no_subject') : '(无主题)';
        const unknownSender = window.i18n ? window.i18n.t('email.unknown_sender') : '未知发件人';
        const fromLabel = window.i18n ? window.i18n.t('email.from') : '从';

        // 创建邮件内容容器
        const emailContent = document.createElement('div');
        emailContent.className = 'email-content relative';
        emailContent.innerHTML = `
            <div class="flex justify-between items-start">
                <div class="font-medium">${escapeHtml(email.subject || noSubject)}</div>
                <div class="text-xs text-gray-500">${formattedDate}</div>
            </div>
            <div class="text-sm text-gray-600">${fromLabel}: ${escapeHtml(email.sender || unknownSender)}</div>
            <div class="text-sm mt-1 text-gray-700">${escapeHtml(email.summary || '')}</div>
        `;

        emailItem.appendChild(emailContent);

        // 添加点击事件
        emailItem.addEventListener('click', (e) => {
            // 防止在滑动操作时触发点击
            if (!emailItem.classList.contains('swiped')) {
                viewEmail(email.id);
            }
        });

        // 添加触摸事件以支持移动端交互
        if ('ontouchstart' in window) {
            emailItem.addEventListener('touchstart', () => {
                emailItem.classList.add('touching');
            });

            emailItem.addEventListener('touchend', () => {
                emailItem.classList.remove('touching');
            });
        }

        return emailItem;
    }

    // 渲染邮件列表
    function renderEmailList() {
        if (!emailList) return;

        if (currentEmailsCache.length === 0) {
            if (!hasReceivedEmails) {
                // 从未收到过邮件，显示欢迎信息
                console.log('显示欢迎信息 - 从未收到过邮件');
                showWelcomeMessage();
            } else {
                // 曾经有邮件但现在没有，显示空状态
                console.log('显示空状态 - 曾经有邮件但现在没有');
                const noMessages = document.getElementById('no-messages');
                if (noMessages) {
                    noMessages.style.display = 'block';
                }
                emailList.innerHTML = '';
            }
            return;
        }

        // 有邮件时隐藏"无邮件"提示
        const noMessages = document.getElementById('no-messages');
        if (noMessages) {
            noMessages.style.display = 'none';
        }

        // 清空现有列表
        emailList.innerHTML = '';

        // 按接收时间排序（新邮件在前）
        const sortedEmails = currentEmailsCache.sort((a, b) =>
            new Date(b.received_at) - new Date(a.received_at)
        );

        // 创建邮件项
        sortedEmails.forEach(email => {
            const emailItem = createEmailItem(email);
            emailList.appendChild(emailItem);
        });

        console.log(`渲染了 ${currentEmailsCache.length} 封邮件`);
    }

    // 获取新邮箱
    async function fetchNewEmail(customPrefix = null) {
        console.log("Fetching new email address...", customPrefix ? `with custom prefix: ${customPrefix}` : '');
        if (pollingTimer) {
            clearInterval(pollingTimer);
            pollingTimer = null;
        }

        // 重置状态
        currentEmailsCache = [];
        hasReceivedEmails = false;
        lastReceivedTimestamp = null;

        // 强制关闭两栏模式，回到单栏显示
        disableTwoColumnMode();

        try {
            // 确保有sessionId
            const currentSessionId = getOrCreateSessionId();
            const result = await window.apiConfig.generateAddress(customPrefix, currentSessionId);
            console.log("fetchNewEmail API response:", result);
            if (result.success) {
                currentEmail = result.data.address;
                emailAddressEl.textContent = currentEmail;

                // 显示单栏欢迎信息
                if (emailList) {
                    showWelcomeMessage();
                } else {
                    console.error("邮件列表元素不存在!");
                }

                const errorDiv = document.getElementById('error-message');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }

                // 保存到本地存储
                localStorage.setItem('temp_email_data', JSON.stringify(result.data));
                console.log(`New email generated and saved: ${currentEmail}`);
                startPolling();

                return true;
            } else {
                // 检查是否是邮箱地址冲突错误
                if (result.error_code === 'EMAIL_ALREADY_EXISTS' && result.data && result.data.suggestions) {
                    // 创建一个特殊的错误对象，包含建议信息
                    const conflictError = new Error(result.error || '邮箱地址已被占用');
                    conflictError.response = result;
                    throw conflictError;
                }
                throw new Error(result.error || '无法创建新邮箱，请稍后再试');
            }
        } catch (error) {
            console.error("Error fetching new email:", error);

            // 检查是否是邮箱地址冲突错误（带建议）
            if (error.response && error.response.error_code === 'EMAIL_ALREADY_EXISTS' && error.response.data && error.response.data.suggestions) {
                // 重新抛出错误，让调用者处理建议
                throw error;
            }

            // 根据错误类型提供用户友好的错误信息
            let userFriendlyMsg = '无法创建临时邮箱';
            if (error.message.includes('重试')) {
                userFriendlyMsg = '网络连接不稳定，请检查网络后重试';
            } else if (error.message.includes('timeout') || error.message.includes('超时')) {
                userFriendlyMsg = '网络请求超时，请稍后再试';
            } else if (error.message.includes('429') || error.message.includes('频繁')) {
                userFriendlyMsg = '操作太频繁，请稍等片刻再试';
            } else if (error.message.includes('500')) {
                userFriendlyMsg = '系统繁忙，请稍后再试';
            } else if (error.message.includes('404')) {
                userFriendlyMsg = '请求的资源不存在';
            } else if (error.message) {
                userFriendlyMsg += '：' + error.message;
            }

            showError(userFriendlyMsg);
            throw error;
        }
    }

    // 轮询相关函数
    function startPolling() {
        if (pollingTimer) clearInterval(pollingTimer);

        if (document.visibilityState === 'hidden') {
            console.log('页面不可见，不启动轮询');
            updateStatusIndicators();
            return;
        }

        fetchInbox().catch(err => console.error("Error in initial fetchInbox:", err));

        if (window.appConfig && window.appConfig.AUTO_REFRESH_INTERVAL) {
            normalRefreshInterval = parseInt(window.appConfig.AUTO_REFRESH_INTERVAL);
            currentRefreshInterval = normalRefreshInterval;
            console.log(`Using server configured refresh interval: ${normalRefreshInterval}ms`);
        } else {
            console.log(`Using default refresh interval: ${normalRefreshInterval}ms`);
        }

        isPollingActive = true;
        console.log(`启动轮询，间隔: ${currentRefreshInterval}ms`);
        pollingTimer = setInterval(() => {
            if (isPollingActive) {
                if (document.visibilityState === 'visible') {
                    if (isDevelopment) {
                        console.log(`执行定时轮询 (${currentRefreshInterval}ms)`);
                    }
                    fetchInbox().catch(err => console.error("Error in polling fetchInbox:", err));
                } else if (isDevelopment) {
                    console.log('页面不可见，跳过本次轮询');
                }
            }
        }, currentRefreshInterval);

        updateStatusIndicators();
    }

    function stopPolling() {
        if (pollingTimer) {
            clearInterval(pollingTimer);
            pollingTimer = null;
            console.log('轮询已停止');
        }
        isPollingActive = false;
        updateStatusIndicators();
    }

    // pausePolling 函数已移除，因为未被使用

    function resumePolling() {
        isPollingActive = true;
        console.log('轮询已恢复');
    }

    function setReducedPollingRate() {
        if (pollingTimer) {
            clearInterval(pollingTimer);
            currentRefreshInterval = reducedRefreshInterval;
            console.log(`切换到降低频率的轮询: ${reducedRefreshInterval}ms`);

            pollingTimer = setInterval(() => {
                if (isPollingActive) {
                    if (document.visibilityState === 'visible') {
                        if (isDevelopment) {
                            console.log(`执行降低频率的轮询 (${reducedRefreshInterval}ms)`);
                        }
                        fetchInbox().catch(err => console.error("降低频率轮询获取邮件出错:", err));
                    } else if (isDevelopment) {
                        console.log('页面不可见，跳过本次降低频率的轮询');
                    }
                }
            }, currentRefreshInterval);

            updateStatusIndicators();
        }
    }

    function setNormalPollingRate() {
        if (pollingTimer) {
            clearInterval(pollingTimer);
            currentRefreshInterval = normalRefreshInterval;
            console.log(`恢复正常轮询间隔: ${normalRefreshInterval}ms`);

            pollingTimer = setInterval(() => {
                if (isPollingActive) {
                    if (document.visibilityState === 'visible') {
                        if (isDevelopment) {
                            console.log(`执行正常频率的轮询 (${normalRefreshInterval}ms)`);
                        }
                        fetchInbox().catch(err => console.error("正常频率轮询获取邮件出错:", err));
                    } else if (isDevelopment) {
                        console.log('页面不可见，跳过本次正常频率的轮询');
                    }
                }
            }, currentRefreshInterval);

            updateStatusIndicators();
        }
    }

    // 获取收件箱 - 修改后的版本
    async function fetchInbox() {
        if (document.visibilityState === 'hidden') {
            if (isDevelopment) {
                console.log('页面不可见，跳过本次轮询');
            }
            return;
        }

        if (!currentEmail) {
            console.log("No email address set, using test fixed address");
            currentEmail = "<EMAIL>";
            if (emailAddressEl) {
                emailAddressEl.textContent = currentEmail;
            } else {
                console.error("邮箱地址元素不存在!");
                return;
            }
        }
        console.log(`Fetching inbox for ${currentEmail}, last_received: ${lastReceivedTimestamp}`);

        try {
            const result = await window.apiConfig.getEmails(currentEmail, lastReceivedTimestamp);
            console.log('API response:', result);

            if (!result.success) {
                if (result.error === "邮箱地址已过期") {
                    localStorage.removeItem(STORAGE_KEY);
                    showError('临时邮箱已过期，正在为您生成新邮箱...', false);
                    console.log('邮箱已过期，停止自动刷新');
                    stopPolling();
                    if (refreshBtn) {
                        refreshBtn.disabled = true;
                    }
                    await fetchNewEmail();
                    return;
                }
                else if (result.error === "没有邮件" || !result.data || (result.data && result.data.emails && result.data.emails.length === 0)) {
                    // 没有新邮件时的处理
                    if (currentEmailsCache.length === 0 && !hasReceivedEmails) {
                        // 如果从未收到过邮件，确保显示欢迎信息
                        console.log('没有新邮件且从未收到过邮件，显示欢迎信息');
                        renderEmailList();
                    } else {
                        // 保持现有状态
                        console.log('没有新邮件，保持现有邮件列表');
                    }
                    return;
                }
                if (emailList) {
                    emailList.innerHTML = `<div class="bg-gray-50 p-4 rounded-lg border text-red-600">${result.error || 'Failed to load inbox'}</div>`;
                } else {
                    console.error("邮件列表元素不存在!");
                }
                return;
            }

            lastReceivedTimestamp = result.data.timestamp;
            const emails = result.data.emails;

            if (emails && emails.length > 0) {
                // 标记已收到邮件
                hasReceivedEmails = true;

                // 更新邮件缓存
                if (currentEmailsCache.length === 0) {
                    // 初次加载或缓存为空，替换整个缓存
                    currentEmailsCache = [...emails];
                    console.log('初次加载邮件或缓存为空，替换整个缓存');
                } else {
                    // 增量更新，添加新邮件
                    emails.forEach(email => {
                        const existingIndex = currentEmailsCache.findIndex(e => e.id === email.id);
                        if (existingIndex === -1) {
                            currentEmailsCache.unshift(email); // 新邮件添加到开头
                            console.log(`添加新邮件: ${email.subject}`);
                        }
                    });
                }

                // 重新渲染邮件列表
                renderEmailList();

                console.log(`更新邮件缓存，共 ${currentEmailsCache.length} 封邮件`);
            } else {
                // 没有邮件时的处理
                if (currentEmailsCache.length === 0 && !hasReceivedEmails) {
                    // 从未收到过邮件，显示欢迎信息
                    console.log('API返回无邮件且从未收到过邮件，显示欢迎信息');
                    renderEmailList();
                } else {
                    // 保持现有缓存不变
                    console.log('API返回无邮件，保持现有缓存');
                }
            }

            return true;
        } catch (error) {
            console.error("Error fetching inbox:", error);
            if (emailList) {
                emailList.innerHTML = `<div class="bg-gray-50 p-4 rounded-lg border text-red-600">获取邮件失败: ${error.message}</div>`;
            } else {
                console.error("邮件列表元素不存在!");
            }

            throw error;
        }
    }

    // 历史邮箱相关函数
    async function showEmailHistory() {
        const modal = document.getElementById('history-modal');
        const loadingDiv = document.getElementById('history-loading');
        const emptyDiv = document.getElementById('history-empty');
        const listDiv = document.getElementById('history-list');

        // 显示弹窗
        modal.classList.remove('hidden');

        // 显示加载状态
        loadingDiv.classList.remove('hidden');
        emptyDiv.classList.add('hidden');
        listDiv.innerHTML = '';

        try {
            const currentSessionId = getOrCreateSessionId();
            const result = await window.apiConfig.getEmailHistory(currentSessionId);

            loadingDiv.classList.add('hidden');

            if (result.success && result.data.history && result.data.history.length > 0) {
                renderEmailHistory(result.data.history);
            } else {
                emptyDiv.classList.remove('hidden');
            }
        } catch (error) {
            console.error('获取历史记录失败:', error);
            loadingDiv.classList.add('hidden');
            const errorText = window.i18n ? window.i18n.t('error.history_fetch_failed') : '获取历史记录失败';
            listDiv.innerHTML = `
                <div class="text-center py-4 text-red-600">
                    <i class="fas fa-exclamation-triangle mb-2"></i>
                    <p>${errorText}</p>
                    <p class="text-sm">${error.message}</p>
                </div>
            `;
        }
    }

    function renderEmailHistory(history) {
        const listDiv = document.getElementById('history-list');
        listDiv.innerHTML = '';

        history.forEach(item => {
            const historyItem = document.createElement('div');
            const isActive = item.is_active;
            const isExpired = item.is_expired;
            const isDeleted = !item.exists_in_db;
            const canSelect = !isExpired && !isDeleted;

            historyItem.className = `p-3 border rounded-lg transition-colors ${
                isActive ? 'bg-blue-50 border-blue-300' :
                canSelect ? 'hover:bg-gray-50 border-gray-200 cursor-pointer' :
                'bg-gray-100 border-gray-200 cursor-not-allowed'
            }`;

            const createdDate = new Date(item.created_at).toLocaleString('zh-CN');
            const expiresDate = new Date(item.expires_at).toLocaleString('zh-CN');

            // 为已删除或过期的邮箱添加删除线样式
            const emailTextStyle = (isExpired || isDeleted) ? 'line-through text-gray-500' : '';
            const timeTextStyle = (isExpired || isDeleted) ? 'line-through text-gray-400' : 'text-gray-500';

            const createdLabel = window.i18n ? window.i18n.t('history.created') : '创建';
            const expiresLabel = window.i18n ? window.i18n.t('history.expires') : '过期';
            const currentLabel = window.i18n ? window.i18n.t('history.current') : '当前';
            const expiredLabel = window.i18n ? window.i18n.t('history.expired') : '已过期';
            const deletedLabel = window.i18n ? window.i18n.t('history.deleted') : '已删除';

            historyItem.innerHTML = `
                <div class="flex justify-between items-start">
                    <div class="flex-grow">
                        <div class="font-medium text-sm ${isActive ? 'text-blue-600' : ''} ${emailTextStyle}">${escapeHtml(item.email_address)}</div>
                        <div class="text-xs mt-1 ${timeTextStyle}">
                            ${createdLabel}: ${createdDate}
                        </div>
                        <div class="text-xs ${timeTextStyle}">
                            ${expiresLabel}: ${expiresDate}
                        </div>
                    </div>
                    <div class="ml-2 flex flex-col items-end">
                        ${isActive ? `<span class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">${currentLabel}</span>` : ''}
                        ${isExpired ? `<span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded mt-1">${expiredLabel}</span>` : ''}
                        ${isDeleted && !isExpired ? `<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded mt-1">${deletedLabel}</span>` : ''}
                    </div>
                </div>
            `;

            // 只有可选择且非当前活跃的邮箱才能点击切换
            if (canSelect && !isActive) {
                historyItem.addEventListener('click', () => switchToHistoryEmail(item.email_address));
            }

            // 为不可访问的邮箱添加提示
            if (!canSelect) {
                historyItem.title = isExpired ? '邮箱已过期，无法访问' : '邮箱已删除，无法访问';
            }

            listDiv.appendChild(historyItem);
        });
    }

    async function switchToHistoryEmail(emailAddress) {
        try {
            const currentSessionId = getOrCreateSessionId();
            const result = await window.apiConfig.switchEmail(currentSessionId, emailAddress);

            if (result.success) {
                // 更新当前邮箱
                currentEmail = result.data.address;
                emailAddressEl.textContent = currentEmail;

                // 重置邮件状态
                currentEmailsCache = [];
                hasReceivedEmails = false;
                lastReceivedTimestamp = null;

                // 关闭弹窗
                document.getElementById('history-modal').classList.add('hidden');

                // 强制关闭两栏模式
                disableTwoColumnMode();

                // 显示欢迎信息并开始轮询
                showWelcomeMessage();
                startPolling();

                console.log(`已切换到历史邮箱: ${emailAddress}`);
            } else {
                const errorText = window.i18n ? window.i18n.t('error.switch_email_failed') : '切换邮箱失败';
                showError(`${errorText}: ${result.error}`);
            }
        } catch (error) {
            console.error('切换邮箱失败:', error);
            const errorText = window.i18n ? window.i18n.t('error.switch_email_failed') : '切换邮箱失败';
            showError(`${errorText}: ${error.message}`);
        }
    }

    function closeEmailHistory() {
        document.getElementById('history-modal').classList.add('hidden');
    }

    // 两栏模式相关函数
    function enableTwoColumnMode() {
        const inboxSection = document.getElementById('inbox-section');
        if (inboxSection) {
            inboxSection.classList.add('two-column-mode');
        }
    }

    function disableTwoColumnMode() {
        const inboxSection = document.getElementById('inbox-section');
        if (inboxSection) {
            inboxSection.classList.remove('two-column-mode');
            console.log('两栏模式已关闭');
        }

        // 清除所有邮件项的激活状态
        document.querySelectorAll('.email-item').forEach(item => {
            item.classList.remove('active');
        });

        // 重置右侧内容区域
        const contentArea = document.getElementById('message-content-area');
        if (contentArea) {
            const selectMessage = window.i18n ? window.i18n.t('inbox.select_message') : '选择邮件查看内容';
            contentArea.innerHTML = `
                <div class="message-placeholder">
                    <div class="text-center">
                        <i class="fas fa-envelope-open text-4xl mb-4"></i>
                        <p>${selectMessage}</p>
                    </div>
                </div>
            `;
        }
    }

    function displayMessageContent(emailData) {
        const contentArea = document.getElementById('message-content-area');
        if (!contentArea) return;

        const unknownSender = window.i18n ? window.i18n.t('email.unknown_sender') : '未知发件人';
        const fromLabel = window.i18n ? window.i18n.t('email.from') : 'From';
        const toLabel = window.i18n ? window.i18n.t('email.to') : 'To';
        const dateLabel = window.i18n ? window.i18n.t('email.date') : 'Date';
        const noSubject = window.i18n ? window.i18n.t('email.no_subject') : '(无主题)';
        const emptyContent = window.i18n ? window.i18n.t('email.empty_content') : '邮件内容为空';

        const contentHTML = `
            <div class="message-content">
                <div class="border-b pb-4 mb-4">
                    <div class="flex justify-between">
                        <div>
                            <p class="font-medium">${fromLabel}: ${escapeHtml(emailData.sender || unknownSender)}</p>
                            <p class="text-sm text-gray-500">${toLabel}: <span class="email-display">${escapeHtml(currentEmail)}</span></p>
                        </div>
                        <p class="text-sm text-gray-500">${dateLabel}: ${new Date(emailData.received_at).toLocaleString()}</p>
                    </div>
                    <h4 class="text-lg font-semibold mt-2">${escapeHtml(emailData.subject || noSubject)}</h4>
                </div>
                <div class="prose max-w-none">
                    <div class="whitespace-pre-wrap">${escapeHtml(emailData.body_text || emailData.body_html || emptyContent)}</div>
                </div>
            </div>
        `;

        contentArea.innerHTML = contentHTML;
    }

    // 查看邮件详情
    async function viewEmail(emailId) {
        try {
            const result = await window.apiConfig.getEmailContent(emailId, currentEmail);

            if (result.success) {
                const emailData = result.data;

                enableTwoColumnMode();
                displayMessageContent(emailData);

                document.querySelectorAll('.email-item').forEach(item => {
                    item.classList.remove('active');
                });
                const selectedItem = document.querySelector(`[data-id="${emailId}"]`);
                if (selectedItem) {
                    selectedItem.classList.add('active');
                }

            } else {
                const errorText = window.i18n ? window.i18n.t('error.load_email_failed') : '无法加载邮件内容，请稍后再试';
                showError(errorText);
            }
        } catch (error) {
            console.error("Error viewing email:", error);
            const errorText = window.i18n ? window.i18n.t('error.load_email_refresh') : '无法加载邮件内容，请刷新后重试';
            showError(errorText);
        }
    }

    // 状态指示器更新
    function updateStatusIndicators() {
        if (focusStatus) {
            focusStatus.textContent = document.hasFocus() ? 'Focused' : 'Not Focused';
            focusStatus.className = document.hasFocus() ? 'text-green-600' : 'text-orange-600';
        }

        if (visibilityStatus) {
            visibilityStatus.textContent = document.visibilityState === 'visible' ? 'Visible' : 'Hidden';
            visibilityStatus.className = document.visibilityState === 'visible' ? 'text-green-600' : 'text-red-600';
        }

        if (pollingInterval) {
            pollingInterval.textContent = `${currentRefreshInterval}ms`;
        }
    }

    // 初始化应用
    async function initializeApp() {
        const savedData = getFromStorage();
        if (savedData) {
            currentEmail = savedData.address;
            emailAddressEl.textContent = currentEmail;
            console.log(`Loaded saved email: ${currentEmail}`);

            // 先显示欢迎信息，然后开始轮询
            showWelcomeMessage();
            startPolling();
        } else {
            console.log("No saved email found, generating new one...");
            await fetchNewEmail();
        }
    }

    initializeApp().catch(err => console.error("App initialization error:", err));

    // 事件监听器
    if (copyBtn) {
        copyBtn.addEventListener('click', async () => {
            if (!currentEmail) {
                const errorText = window.i18n ? window.i18n.t('error.no_email_to_copy') : '没有邮箱地址可复制';
                showError(errorText);
                return;
            }

            try {
                copyBtn.disabled = true;
                copyBtn.classList.add('loading-pulse');

                await navigator.clipboard.writeText(currentEmail);
                const copiedText = window.i18n ? window.i18n.t('status.copied') : '已复制!';
                const copyText = window.i18n ? window.i18n.t('button.copy') : '复制';
                copyBtn.innerHTML = `<i class="fas fa-check mr-2"></i>${copiedText}`;
                copyBtn.classList.remove('loading-pulse');

                setTimeout(() => {
                    copyBtn.innerHTML = `<i class="fas fa-copy mr-2"></i>${copyText}`;
                    copyBtn.disabled = false;
                }, 2000);
            } catch (err) {
                console.error('Copy failed:', err);
                copyBtn.classList.remove('loading-pulse');
                copyBtn.disabled = false;
                const errorText = window.i18n ? window.i18n.t('error.copy_failed') : '复制失败，请手动复制邮箱地址';
                showError(errorText);
            }
        });
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            if (!currentEmail) {
                const errorText = window.i18n ? window.i18n.t('error.no_email_to_refresh') : '请先生成邮箱地址';
                showError(errorText);
                return;
            }

            try {
                refreshBtn.disabled = true;
                refreshBtn.classList.add('loading-pulse');
                const refreshingText = window.i18n ? window.i18n.t('status.refreshing') : '刷新中';
                refreshBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${refreshingText}`;

                // 确保动画至少显示1秒
                const startTime = Date.now();
                const minDuration = 1000; // 最少显示1秒

                await fetchInbox();

                // 计算剩余时间
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minDuration - elapsedTime);

                // 如果还有剩余时间，等待完成
                if (remainingTime > 0) {
                    await new Promise(resolve => setTimeout(resolve, remainingTime));
                }

                refreshBtn.classList.remove('loading-pulse');
                const refreshText = window.i18n ? window.i18n.t('button.refresh') : '刷新';
                refreshBtn.innerHTML = `<i class="fas fa-sync-alt mr-2"></i>${refreshText}`;
                refreshBtn.disabled = false;
            } catch (err) {
                console.error('Refresh failed:', err);
                refreshBtn.classList.remove('loading-pulse');
                const refreshText = window.i18n ? window.i18n.t('button.refresh') : '刷新';
                refreshBtn.innerHTML = `<i class="fas fa-sync-alt mr-2"></i>${refreshText}`;
                refreshBtn.disabled = false;
                const errorText = window.i18n ? window.i18n.t('error.refresh_failed') : '刷新失败，请稍后再试';
                showError(errorText);
            }
        });
    }

    if (newEmailBtn) {
        newEmailBtn.addEventListener('click', async () => {
            if (newEmailBtn.disabled) {
                // 防止重复点击
                return;
            }

            try {
                // 开始加载状态
                newEmailBtn.disabled = true;
                newEmailBtn.classList.add('loading-pulse');
                const generatingText = window.i18n ? window.i18n.t('status.generating') : '生成中...';
                newEmailBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${generatingText}`;

                // 清除本地存储
                localStorage.removeItem(STORAGE_KEY);

                // 获取新邮箱
                await fetchNewEmail();

                // 成功状态 - 短暂显示成功状态
                newEmailBtn.classList.remove('loading-pulse');
                const generatedText = window.i18n ? window.i18n.t('status.generated') : '已生成!';
                newEmailBtn.innerHTML = `<i class="fas fa-check mr-2"></i>${generatedText}`;

                // 2秒后恢复原始状态
                setTimeout(() => {
                    if (newEmailBtn) {
                        const newEmailText = window.i18n ? window.i18n.t('button.new_email') : '新邮箱';
                        newEmailBtn.innerHTML = `<i class="fas fa-plus mr-2"></i>${newEmailText}`;
                        newEmailBtn.disabled = false;
                    }
                }, 2000);

            } catch (err) {
                console.error('Generate new email failed:', err);

                // 错误状态
                newEmailBtn.classList.remove('loading-pulse');
                const failedText = window.i18n ? window.i18n.t('status.failed') : '失败';
                newEmailBtn.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${failedText}`;

                // 2秒后恢复原始状态
                setTimeout(() => {
                    if (newEmailBtn) {
                        const newEmailText = window.i18n ? window.i18n.t('button.new_email') : '新邮箱';
                        newEmailBtn.innerHTML = `<i class="fas fa-plus mr-2"></i>${newEmailText}`;
                        newEmailBtn.disabled = false;
                    }
                }, 2000);
            }
        });
    }

    // 邮箱建议相关函数
    function showEmailSuggestions(suggestions, requestedAddress) {
        const suggestionModal = document.getElementById('suggestion-modal');
        const suggestionList = document.getElementById('suggestion-list');
        const suggestionLoading = document.getElementById('suggestion-loading');
        const suggestionEmpty = document.getElementById('suggestion-empty');

        // 隐藏加载状态
        suggestionLoading.classList.add('hidden');

        if (!suggestions || suggestions.length === 0) {
            suggestionEmpty.classList.remove('hidden');
            suggestionList.innerHTML = '';
        } else {
            suggestionEmpty.classList.add('hidden');

            // 生成建议列表
            suggestionList.innerHTML = '';
            suggestions.forEach((suggestion, index) => {
                const suggestionItem = document.createElement('div');
                suggestionItem.className = 'suggestion-item p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors haptic-feedback';
                suggestionItem.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div class="font-mono text-blue-600 break-all">${suggestion}</div>
                        <button class="select-suggestion-btn bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors min-h-[32px]" data-suggestion="${suggestion}">
                            <span data-i18n="suggestion.select">选择此地址</span>
                        </button>
                    </div>
                `;

                // 添加点击事件
                const selectBtn = suggestionItem.querySelector('.select-suggestion-btn');
                selectBtn.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    await selectSuggestion(suggestion);
                });

                suggestionList.appendChild(suggestionItem);
            });
        }

        // 显示弹窗
        suggestionModal.classList.remove('hidden');
    }

    function closeSuggestionModal() {
        const suggestionModal = document.getElementById('suggestion-modal');
        suggestionModal.classList.add('hidden');
    }

    async function selectSuggestion(suggestedAddress) {
        try {
            // 显示加载状态
            const suggestionModal = document.getElementById('suggestion-modal');
            const suggestionLoading = document.getElementById('suggestion-loading');
            const suggestionList = document.getElementById('suggestion-list');

            suggestionList.classList.add('hidden');
            suggestionLoading.classList.remove('hidden');

            // 确保有sessionId
            const currentSessionId = getOrCreateSessionId();

            // 调用API创建建议的邮箱
            const result = await window.apiConfig.createSuggestedEmail(suggestedAddress, currentSessionId);

            if (result.success) {
                // 成功创建邮箱
                currentEmail = result.data.address;
                emailAddressEl.textContent = currentEmail;

                // 保存到本地存储
                localStorage.setItem(STORAGE_KEY, JSON.stringify(result.data));

                // 关闭弹窗
                closeSuggestionModal();

                // 重置邮件状态
                currentEmailsCache = [];
                hasReceivedEmails = false;
                lastReceivedTimestamp = null;

                // 强制关闭两栏模式
                disableTwoColumnMode();

                // 显示欢迎信息并开始轮询
                showWelcomeMessage();
                startPolling();

                console.log(`成功创建建议邮箱: ${currentEmail}`);

                // 恢复自定义按钮状态
                if (customEmailBtn) {
                    customEmailBtn.classList.remove('loading-pulse');
                    const createdText = window.i18n ? window.i18n.t('status.created') : '已创建!';
                    customEmailBtn.innerHTML = `<i class="fas fa-check mr-2"></i>${createdText}`;

                    setTimeout(() => {
                        if (customEmailBtn) {
                            const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                            customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                            customEmailBtn.disabled = false;
                        }
                    }, 2000);
                }

            } else {
                throw new Error(result.error || '创建建议邮箱失败');
            }

        } catch (error) {
            console.error('选择建议邮箱失败:', error);

            // 隐藏加载状态
            const suggestionLoading = document.getElementById('suggestion-loading');
            const suggestionList = document.getElementById('suggestion-list');
            suggestionLoading.classList.add('hidden');
            suggestionList.classList.remove('hidden');

            // 显示错误信息
            let errorMsg = window.i18n ? window.i18n.t('error.create_suggestion_failed') : '创建建议邮箱失败';
            if (error.message.includes('no longer available') || error.message.includes('已被占用')) {
                errorMsg = window.i18n ? window.i18n.t('error.suggestion_unavailable') : '建议的邮箱地址已不可用';
            }
            showError(errorMsg);

            // 恢复自定义按钮状态
            if (customEmailBtn) {
                customEmailBtn.classList.remove('loading-pulse');
                const failedText = window.i18n ? window.i18n.t('status.failed') : '失败';
                customEmailBtn.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${failedText}`;

                setTimeout(() => {
                    if (customEmailBtn) {
                        const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                        customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                        customEmailBtn.disabled = false;
                    }
                }, 2000);
            }
        }
    }

    // 自定义前缀处理函数（供移动端模块调用）
    window.mobileCustomPrefixCallback = async (customPrefix) => {
        if (customEmailBtn.disabled) {
            return;
        }

        // 使用验证函数
        const validation = validateEmailPrefix(customPrefix);
        if (!validation.valid) {
            showError(validation.error);
            return;
        }

        try {
            // 开始加载状态
            customEmailBtn.disabled = true;
            customEmailBtn.classList.add('loading-pulse');
            const creatingText = window.i18n ? window.i18n.t('status.creating') : '创建中...';
            customEmailBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${creatingText}`;

            // 清除本地存储
            localStorage.removeItem(STORAGE_KEY);

            // 获取新邮箱（带自定义前缀）
            await fetchNewEmail(customPrefix);

            // 成功状态 - 短暂显示成功状态
            customEmailBtn.classList.remove('loading-pulse');
            const createdText = window.i18n ? window.i18n.t('status.created') : '已创建!';
            customEmailBtn.innerHTML = `<i class="fas fa-check mr-2"></i>${createdText}`;

            // 2秒后恢复原始状态
            setTimeout(() => {
                if (customEmailBtn) {
                    const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                    customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                    customEmailBtn.disabled = false;
                }
            }, 2000);

        } catch (err) {
            console.error('Generate custom email failed:', err);

            // 检查是否是邮箱地址冲突错误
            if (err.response && err.response.error_code === 'EMAIL_ALREADY_EXISTS' && err.response.data && err.response.data.suggestions) {
                // 显示建议邮箱
                showEmailSuggestions(err.response.data.suggestions, err.response.data.requested_address);
                return; // 不恢复按钮状态，等待用户选择
            }

            // 其他错误的处理
            customEmailBtn.classList.remove('loading-pulse');
            const failedText = window.i18n ? window.i18n.t('status.failed') : '失败';
            customEmailBtn.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${failedText}`;

            // 2秒后恢复原始状态
            setTimeout(() => {
                if (customEmailBtn) {
                    const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                    customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                    customEmailBtn.disabled = false;
                }
            }, 2000);
        }
    };

    // Custom Email 按钮事件监听器 - 检测是否为移动设备
    if (customEmailBtn) {
        customEmailBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (customEmailBtn.disabled) {
                // 防止重复点击
                return;
            }

            // 检测是否为触摸设备，如果是则由移动端模块处理
            if ('ontouchstart' in window && window.mobileEnhancement) {
                // 移动端模块会处理这个点击事件
                return;
            }

            // 桌面端使用原生prompt
            const promptText = window.i18n ? window.i18n.t('prompt.custom_prefix') : '请输入自定义邮箱前缀（只能包含字母、数字和连字符，长度1-20字符）:';
            const customPrefix = prompt(promptText);

            // 用户取消或输入为空
            if (!customPrefix) {
                return;
            }

            // 调用处理函数
            await window.mobileCustomPrefixCallback(customPrefix);
        });
    }

    // 删除邮箱和重置功能
    async function deleteAndResetEmail() {
        if (!currentEmail) {
            const errorText = window.i18n ? window.i18n.t('error.no_email_to_delete') : '没有邮箱可以删除';
            showError(errorText);
            return;
        }

        try {
            // 停止轮询
            stopPolling();

            // 调用删除API（如果后端支持）
            try {
                const result = await window.apiConfig.deleteEmail(currentEmail);

                if (!result.success) {
                    console.warn('删除邮箱API调用失败，但继续重置:', result.error);
                }
            } catch (apiError) {
                console.warn('删除邮箱API调用出错，但继续重置:', apiError);
            }

            // 无论API是否成功，都进行本地重置
            console.log(`正在删除邮箱: ${currentEmail}`);

            // 清除所有状态
            currentEmail = null;
            lastReceivedTimestamp = null;
            currentEmailsCache = [];
            hasReceivedEmails = false;

            // 清除本地存储
            localStorage.removeItem(STORAGE_KEY);

            // 强制关闭两栏模式
            disableTwoColumnMode();

            // 清空邮件列表显示
            if (emailList) {
                emailList.innerHTML = '';
            }

            // 清空邮箱地址显示
            if (emailAddressEl) {
                emailAddressEl.textContent = '正在生成新邮箱...';
            }

            console.log('邮箱状态已重置，正在生成新邮箱...');

            // 生成新邮箱
            await fetchNewEmail();

            console.log('删除并重置完成');
            return true;

        } catch (error) {
            console.error('删除邮箱过程中出错:', error);
            // 即使出错也尝试生成新邮箱
            try {
                await fetchNewEmail();
            } catch (newEmailError) {
                console.error('生成新邮箱失败:', newEmailError);
                const errorText = window.i18n ? window.i18n.t('error.delete_failed') : '删除失败，请稍后再试';
                showError(errorText);
            }
            throw error;
        }
    }

    // 添加删除按钮事件监听器
    if (deleteEmailBtn) {
        deleteEmailBtn.addEventListener('click', async () => {
            if (deleteEmailBtn.disabled) {
                // 防止重复点击
                return;
            }

            // 确认删除
            const confirmText = window.i18n ? window.i18n.t('confirm.delete_email') : '确定要删除当前邮箱和所有邮件吗？此操作不可撤销。';
            if (!confirm(confirmText)) {
                return;
            }

            try {
                // 开始加载状态
                deleteEmailBtn.disabled = true;
                deleteEmailBtn.classList.add('loading-pulse');
                const deletingText = window.i18n ? window.i18n.t('status.deleting') : '删除中...';
                deleteEmailBtn.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${deletingText}`;

                // 执行删除和重置
                await deleteAndResetEmail();

                // 成功状态
                deleteEmailBtn.classList.remove('loading-pulse');
                const deletedText = window.i18n ? window.i18n.t('status.deleted') : '已删除!';
                deleteEmailBtn.innerHTML = `<i class="fas fa-check mr-2"></i>${deletedText}`;

                // 2秒后恢复原始状态
                setTimeout(() => {
                    if (deleteEmailBtn) {
                        const deleteResetText = window.i18n ? window.i18n.t('button.delete_reset') : '删除并重置';
                        deleteEmailBtn.innerHTML = `<i class="fas fa-trash mr-2"></i>${deleteResetText}`;
                        deleteEmailBtn.disabled = false;
                    }
                }, 2000);

            } catch (err) {
                console.error('Delete and reset failed:', err);

                // 错误状态
                deleteEmailBtn.classList.remove('loading-pulse');
                const failedText = window.i18n ? window.i18n.t('status.failed') : '失败';
                deleteEmailBtn.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${failedText}`;

                // 2秒后恢复原始状态
                setTimeout(() => {
                    if (deleteEmailBtn) {
                        const deleteResetText = window.i18n ? window.i18n.t('button.delete_reset') : '删除并重置';
                        deleteEmailBtn.innerHTML = `<i class="fas fa-trash mr-2"></i>${deleteResetText}`;
                        deleteEmailBtn.disabled = false;
                    }
                }, 2000);
            }
        });
    }

    // History按钮事件监听器
    const historyBtn = document.getElementById('history-btn');
    if (historyBtn) {
        historyBtn.addEventListener('click', () => {
            showEmailHistory();
        });
    }

    // 关闭历史弹窗按钮事件监听器
    const closeHistoryModalBtn = document.getElementById('close-history-modal');
    if (closeHistoryModalBtn) {
        closeHistoryModalBtn.addEventListener('click', () => {
            closeEmailHistory();
        });
    }

    // 点击弹窗背景关闭
    const historyModal = document.getElementById('history-modal');
    if (historyModal) {
        historyModal.addEventListener('click', (e) => {
            if (e.target === historyModal) {
                closeEmailHistory();
            }
        });
    }

    // 建议弹窗事件监听器
    const closeSuggestionModalBtn = document.getElementById('close-suggestion-modal');
    if (closeSuggestionModalBtn) {
        closeSuggestionModalBtn.addEventListener('click', () => {
            closeSuggestionModal();
            // 恢复自定义按钮状态
            if (customEmailBtn) {
                customEmailBtn.classList.remove('loading-pulse');
                const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                customEmailBtn.disabled = false;
            }
        });
    }

    // 重新输入按钮事件监听器
    const tryAgainBtn = document.getElementById('try-again-btn');
    if (tryAgainBtn) {
        tryAgainBtn.addEventListener('click', () => {
            closeSuggestionModal();
            // 恢复自定义按钮状态
            if (customEmailBtn) {
                customEmailBtn.classList.remove('loading-pulse');
                const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                customEmailBtn.disabled = false;
            }
            // 重新触发自定义邮箱创建流程
            if (customEmailBtn) {
                customEmailBtn.click();
            }
        });
    }

    // 点击建议弹窗背景关闭
    const suggestionModal = document.getElementById('suggestion-modal');
    if (suggestionModal) {
        suggestionModal.addEventListener('click', (e) => {
            if (e.target === suggestionModal) {
                closeSuggestionModal();
                // 恢复自定义按钮状态
                if (customEmailBtn) {
                    customEmailBtn.classList.remove('loading-pulse');
                    const customText = window.i18n ? window.i18n.t('button.custom') : '自定义';
                    customEmailBtn.innerHTML = `<i class="fas fa-edit mr-2"></i>${customText}`;
                    customEmailBtn.disabled = false;
                }
            }
        });
    }

    // 关闭按钮事件监听器
    const emailCloseBtn = document.getElementById('close-email-btn');
    if (emailCloseBtn) {
        emailCloseBtn.addEventListener('click', () => {
            disableTwoColumnMode();
        });
    }

    // 页面可见性变化处理
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
            console.log('页面变为可见，恢复正常轮询');
            setNormalPollingRate();
            resumePolling();
        } else {
            console.log('页面变为不可见，降低轮询频率');
            setReducedPollingRate();
        }
        updateStatusIndicators();
    });

    // 窗口焦点变化处理
    window.addEventListener('focus', () => {
        console.log('窗口获得焦点');
        setNormalPollingRate();
        resumePolling();
        updateStatusIndicators();
    });

    window.addEventListener('blur', () => {
        console.log('窗口失去焦点');
        setReducedPollingRate();
        updateStatusIndicators();
    });

    // 初始状态指示器更新
    updateStatusIndicators();
});