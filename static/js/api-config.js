/**
 * API配置管理模块
 * 统一管理所有API端点和请求配置
 */
class APIConfig {
    constructor() {
        // 默认配置
        this.config = {
            baseUrl: '',  // 空字符串表示使用相对路径
            timeout: 10000,  // 10秒超时
            retryAttempts: 3,
            retryDelay: 1000,  // 1秒
            endpoints: {
                generateAddress: '/api/generate-address',
                createSuggestedEmail: '/api/create-suggested-email',
                getEmails: '/api/emails',
                getEmailContent: '/api/email',
                deleteEmail: '/api/delete-email',
                getEmailHistory: '/api/email-history',
                switchEmail: '/api/switch-email'
            },
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        // 从全局配置中获取设置（如果存在）
        this.loadFromGlobalConfig();
    }

    /**
     * 从全局配置加载API设置
     */
    loadFromGlobalConfig() {
        if (window.appConfig) {
            // 如果有API基础URL配置
            if (window.appConfig.API_BASE_URL) {
                this.config.baseUrl = window.appConfig.API_BASE_URL;
            }

            // 如果有超时配置
            if (window.appConfig.API_TIMEOUT) {
                this.config.timeout = parseInt(window.appConfig.API_TIMEOUT);
            }

            // 如果有重试配置
            if (window.appConfig.API_RETRY_ATTEMPTS) {
                this.config.retryAttempts = parseInt(window.appConfig.API_RETRY_ATTEMPTS);
            }

            // 如果有重试延迟配置
            if (window.appConfig.API_RETRY_DELAY) {
                this.config.retryDelay = parseInt(window.appConfig.API_RETRY_DELAY);
            }

            console.log('API配置已从全局配置加载:', this.config);
        }
    }

    /**
     * 获取完整的API URL
     * @param {string} endpoint - 端点路径
     * @param {Object} params - URL参数
     * @returns {string} 完整的URL
     */
    getUrl(endpoint, params = {}) {
        const endpointPath = this.config.endpoints[endpoint] || endpoint;
        let url = this.config.baseUrl + endpointPath;

        // 添加查询参数
        if (Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                    searchParams.append(key, value);
                }
            });
            url += '?' + searchParams.toString();
        }

        return url;
    }

    /**
     * 获取默认请求选项
     * @param {Object} options - 额外的请求选项
     * @returns {Object} 合并后的请求选项
     */
    getRequestOptions(options = {}) {
        return {
            headers: { ...this.config.headers },
            timeout: this.config.timeout,
            ...options
        };
    }

    /**
     * 带重试机制的fetch请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} fetch响应
     */
    async fetchWithRetry(url, options = {}) {
        const requestOptions = this.getRequestOptions(options);
        let lastError;

        for (let attempt = 0; attempt < this.config.retryAttempts; attempt++) {
            try {
                console.log(`API请求 (尝试 ${attempt + 1}/${this.config.retryAttempts}): ${url}`);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

                const response = await fetch(url, {
                    ...requestOptions,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                // 如果是网络错误或5xx错误，则重试
                if (!response.ok && response.status >= 500) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return response;

            } catch (error) {
                lastError = error;
                console.warn(`API请求失败 (尝试 ${attempt + 1}/${this.config.retryAttempts}):`, error.message);

                // 如果不是最后一次尝试，等待后重试
                if (attempt < this.config.retryAttempts - 1) {
                    await this.delay(this.config.retryDelay * (attempt + 1)); // 递增延迟
                }
            }
        }

        throw new Error(`API请求失败，已重试${this.config.retryAttempts}次: ${lastError.message}`);
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 生成新邮箱地址
     * @param {string} customPrefix - 可选的自定义前缀
     * @param {string} sessionId - 可选的会话ID
     * @returns {Promise<Object>} API响应
     */
    async generateAddress(customPrefix = null, sessionId = null) {
        const url = this.getUrl('generateAddress');
        const requestBody = {};

        if (customPrefix) {
            requestBody.custom_prefix = customPrefix;
        }

        if (sessionId) {
            requestBody.session_id = sessionId;
        }

        const response = await this.fetchWithRetry(url, {
            method: 'POST',
            body: JSON.stringify(requestBody)  // 总是发送JSON，即使是空对象
        });
        return await response.json();
    }

    /**
     * 使用建议的邮箱地址创建邮箱
     * @param {string} suggestedAddress - 建议的邮箱地址
     * @param {string} sessionId - 可选的会话ID
     * @returns {Promise<Object>} API响应
     */
    async createSuggestedEmail(suggestedAddress, sessionId = null) {
        const url = this.getUrl('createSuggestedEmail');
        const requestBody = {
            suggested_address: suggestedAddress
        };

        if (sessionId) {
            requestBody.session_id = sessionId;
        }

        const response = await this.fetchWithRetry(url, {
            method: 'POST',
            body: JSON.stringify(requestBody)
        });
        return await response.json();
    }

    /**
     * 获取邮件列表
     * @param {string} address - 邮箱地址
     * @param {string} lastReceived - 最后接收时间
     * @returns {Promise<Object>} API响应
     */
    async getEmails(address, lastReceived = null) {
        const params = { address };
        if (lastReceived) {
            params.last_received = lastReceived;
        }

        const url = this.getUrl('getEmails', params);
        const response = await this.fetchWithRetry(url);
        return await response.json();
    }

    /**
     * 获取邮件详细内容
     * @param {number} emailId - 邮件ID
     * @param {string} address - 邮箱地址
     * @returns {Promise<Object>} API响应
     */
    async getEmailContent(emailId, address) {
        const url = this.getUrl('getEmailContent') + `/${emailId}`;
        const params = { address };
        const fullUrl = url + '?' + new URLSearchParams(params).toString();

        const response = await this.fetchWithRetry(fullUrl);
        return await response.json();
    }

    /**
     * 删除邮箱
     * @param {string} address - 邮箱地址
     * @returns {Promise<Object>} API响应
     */
    async deleteEmail(address) {
        const url = this.getUrl('deleteEmail');
        const response = await this.fetchWithRetry(url, {
            method: 'DELETE',
            body: JSON.stringify({ address })
        });
        return await response.json();
    }

    /**
     * 获取邮箱历史记录
     * @param {string} sessionId - 会话ID
     * @returns {Promise<Object>} API响应
     */
    async getEmailHistory(sessionId) {
        const params = { session_id: sessionId };
        const url = this.getUrl('getEmailHistory', params);
        const response = await this.fetchWithRetry(url);
        return await response.json();
    }

    /**
     * 切换到指定的历史邮箱
     * @param {string} sessionId - 会话ID
     * @param {string} emailAddress - 要切换到的邮箱地址
     * @returns {Promise<Object>} API响应
     */
    async switchEmail(sessionId, emailAddress) {
        const url = this.getUrl('switchEmail');
        const response = await this.fetchWithRetry(url, {
            method: 'POST',
            body: JSON.stringify({
                session_id: sessionId,
                email_address: emailAddress
            })
        });
        return await response.json();
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新的配置选项
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('API配置已更新:', this.config);
    }

    /**
     * 获取当前配置
     * @returns {Object} 当前配置
     */
    getConfig() {
        return { ...this.config };
    }
}

// 创建全局API配置实例
window.apiConfig = new APIConfig();

// 导出以供使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIConfig;
}
