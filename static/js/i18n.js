/**
 * 国际化(i18n)支持模块
 * 支持中文和英文语言切换
 */
class I18n {
    constructor() {
        this.currentLanguage = 'zh-CN'; // 默认中文
        this.translations = {
            'zh-CN': {
                // 页面标题和描述
                'page.title': '临时邮箱 - 免费一次性邮箱服务',
                'page.description': '免费临时邮箱服务，保护您的隐私。创建自动过期的一次性邮箱地址。',

                // 导航栏
                'nav.title': '临时邮箱',
                'nav.home': '首页',
                'nav.faq': '常见问题',
                'nav.contact': '联系我们',

                // 主要内容
                'main.title': '您的临时邮箱地址',
                'button.copy': '复制',
                'button.refresh': '刷新',
                'button.new_email': '新邮箱',
                'button.custom': '自定义',
                'button.history': '切换邮箱',
                'button.delete_reset': '删除邮箱',
                'button.close': '关闭',
                'button.cancel': '取消',
                'button.confirm': '确认',

                // 自定义前缀
                'custom.title': '自定义邮箱前缀',
                'custom.label': '邮箱前缀（1-20字符，支持字母、数字、连字符）',
                'custom.hint': '提示：前缀将用于生成您的临时邮箱地址',
                'custom.placeholder': '例如：myemail123',

                // 收件箱
                'inbox.title': '收件箱',
                'inbox.no_messages': '暂无邮件',
                'inbox.no_messages_desc': '收到邮件时会显示在这里',
                'inbox.select_message': '选择邮件查看内容',

                // 历史记录弹窗
                'history.title': '邮箱历史记录',
                'history.loading': '加载历史记录...',
                'history.empty': '暂无历史记录',
                'history.empty_desc': '生成邮箱后会显示在这里',
                'history.current': '当前',
                'history.expired': '已过期',
                'history.deleted': '已删除',
                'history.created': '创建',
                'history.expires': '过期',
                'history.tooltip_expired': '邮箱已过期，无法访问',
                'history.tooltip_deleted': '邮箱已删除，无法访问',

                // 按钮状态
                'status.copied': '已复制!',
                'status.refreshing': '刷新中',
                'status.generating': '生成中...',
                'status.generated': '已生成!',
                'status.creating': '创建中...',
                'status.created': '已创建!',
                'status.deleting': '删除中...',
                'status.deleted': '已删除!',
                'status.failed': '失败',

                // 欢迎消息
                'welcome.title': '欢迎使用临时邮箱',
                'welcome.message': '感谢您使用我们的临时邮箱服务。您的邮箱地址将在24小时后过期。',

                // 错误消息
                'error.no_email_to_copy': '没有邮箱地址可复制',
                'error.copy_failed': '复制失败，请手动复制邮箱地址',
                'error.no_email_to_refresh': '请先生成邮箱地址',
                'error.refresh_failed': '刷新失败，请稍后再试',
                'error.no_email_to_delete': '没有邮箱可以删除',
                'error.delete_failed': '删除失败，请稍后再试',
                'error.prefix_empty': '前缀不能为空',
                'error.prefix_too_long': '前缀长度不能超过20个字符',
                'error.prefix_invalid': '前缀只能包含字母、数字和连字符',
                'error.load_email_failed': '无法加载邮件内容，请稍后再试',
                'error.load_email_refresh': '无法加载邮件内容，请刷新后重试',
                'error.switch_email_failed': '切换邮箱失败',
                'error.history_fetch_failed': '获取历史记录失败',
                'error.email_already_exists': '邮箱地址已被占用',
                'error.suggestion_unavailable': '建议的邮箱地址已不可用',
                'error.create_suggestion_failed': '创建建议邮箱失败',

                // 邮箱建议相关
                'suggestion.title': '邮箱地址已被占用',
                'suggestion.message': '您输入的邮箱地址已被占用，请选择以下建议的替代地址：',
                'suggestion.select': '选择此地址',
                'suggestion.try_again': '重新输入',
                'suggestion.loading': '正在生成建议...',
                'suggestion.no_suggestions': '暂无可用建议，请稍后重试',

                // 确认对话框
                'confirm.delete_email': '确定要删除当前邮箱和所有邮件吗？此操作不可撤销。',
                'prompt.custom_prefix': '请输入自定义邮箱前缀（只能包含字母、数字和连字符，长度1-20字符）:',

                // 邮件相关
                'email.from': '从',
                'email.to': '到',
                'email.date': '日期',
                'email.no_subject': '(无主题)',
                'email.unknown_sender': '未知发件人',
                'email.empty_content': '邮件内容为空',

                // 页脚
                'footer.title': '临时邮箱',
                'footer.description': '免费一次性临时邮箱服务',
                'footer.links': '链接',
                'footer.legal': '法律',
                'footer.terms': '服务条款',
                'footer.privacy': '隐私政策',
                'footer.copyright': '© 2025 临时邮箱. 保留所有权利.',

                // 时间格式
                'time.minutes_ago': '分钟前',
                'time.hours_ago': '小时前',
                'time.days_ago': '天前'
            },
            'en-US': {
                // 页面标题和描述
                'page.title': 'TempMail - Disposable Temporary Email Service',
                'page.description': 'Free temporary email service to protect your privacy. Create disposable email addresses that expire automatically.',

                // 导航栏
                'nav.title': 'TempMail',
                'nav.home': 'Home',
                'nav.faq': 'FAQ',
                'nav.contact': 'Contact',

                // 主要内容
                'main.title': 'Your Temporary Email Address',
                'button.copy': 'Copy',
                'button.refresh': 'Refresh',
                'button.new_email': 'New Email',
                'button.custom': 'Custom',
                'button.history': 'Switch Email',
                'button.delete_reset': 'Delete Email',
                'button.close': 'Close',
                'button.cancel': 'Cancel',
                'button.confirm': 'Confirm',

                // Custom prefix
                'custom.title': 'Custom Email Prefix',
                'custom.label': 'Email prefix (1-20 characters, letters, numbers, hyphens)',
                'custom.hint': 'Tip: The prefix will be used to generate your temporary email address',
                'custom.placeholder': 'e.g.: myemail123',

                // 收件箱
                'inbox.title': 'Inbox Messages',
                'inbox.no_messages': 'No messages yet',
                'inbox.no_messages_desc': 'Messages will appear here when received',
                'inbox.select_message': 'Select a message to view its content',

                // 历史记录弹窗
                'history.title': 'Email History',
                'history.loading': 'Loading history...',
                'history.empty': 'No history yet',
                'history.empty_desc': 'Generated emails will appear here',
                'history.current': 'Current',
                'history.expired': 'Expired',
                'history.deleted': 'Deleted',
                'history.created': 'Created',
                'history.expires': 'Expires',
                'history.tooltip_expired': 'Email has expired and cannot be accessed',
                'history.tooltip_deleted': 'Email has been deleted and cannot be accessed',

                // 按钮状态
                'status.copied': 'Copied!',
                'status.refreshing': 'Refreshing',
                'status.generating': 'Generating...',
                'status.generated': 'Generated!',
                'status.creating': 'Creating...',
                'status.created': 'Created!',
                'status.deleting': 'Deleting...',
                'status.deleted': 'Deleted!',
                'status.failed': 'Failed',

                // 欢迎消息
                'welcome.title': 'Welcome to TempMail',
                'welcome.message': 'Thank you for using our temporary email service. Your address will expire in 24 hours.',

                // 错误消息
                'error.no_email_to_copy': 'No email address to copy',
                'error.copy_failed': 'Copy failed, please copy the email address manually',
                'error.no_email_to_refresh': 'Please generate an email address first',
                'error.refresh_failed': 'Refresh failed, please try again later',
                'error.no_email_to_delete': 'No email to delete',
                'error.delete_failed': 'Delete failed, please try again later',
                'error.prefix_empty': 'Prefix cannot be empty',
                'error.prefix_too_long': 'Prefix cannot exceed 20 characters',
                'error.prefix_invalid': 'Prefix can only contain letters, numbers and hyphens',
                'error.load_email_failed': 'Unable to load email content, please try again later',
                'error.load_email_refresh': 'Unable to load email content, please refresh and try again',
                'error.switch_email_failed': 'Failed to switch email',
                'error.history_fetch_failed': 'Failed to fetch history',
                'error.email_already_exists': 'Email address already exists',
                'error.suggestion_unavailable': 'Suggested email address is no longer available',
                'error.create_suggestion_failed': 'Failed to create suggested email',

                // 邮箱建议相关
                'suggestion.title': 'Email Address Already Taken',
                'suggestion.message': 'The email address you entered is already taken. Please choose from the following suggested alternatives:',
                'suggestion.select': 'Select this address',
                'suggestion.try_again': 'Try again',
                'suggestion.loading': 'Generating suggestions...',
                'suggestion.no_suggestions': 'No suggestions available, please try again later',

                // 确认对话框
                'confirm.delete_email': 'Are you sure you want to delete the current email and all messages? This action cannot be undone.',
                'prompt.custom_prefix': 'Please enter a custom email prefix (letters, numbers and hyphens only, 1-20 characters):',

                // 邮件相关
                'email.from': 'From',
                'email.to': 'To',
                'email.date': 'Date',
                'email.no_subject': '(No Subject)',
                'email.unknown_sender': 'Unknown Sender',
                'email.empty_content': 'Email content is empty',

                // 页脚
                'footer.title': 'TempMail',
                'footer.description': 'Free disposable temporary email service',
                'footer.links': 'Links',
                'footer.legal': 'Legal',
                'footer.terms': 'Terms',
                'footer.privacy': 'Privacy',
                'footer.copyright': '© 2025 TempMail. All rights reserved.',

                // 时间格式
                'time.minutes_ago': 'minutes ago',
                'time.hours_ago': 'hours ago',
                'time.days_ago': 'days ago'
            }
        };

        // 从localStorage获取保存的语言设置
        const savedLanguage = localStorage.getItem('temp_email_language');
        if (savedLanguage && this.translations[savedLanguage]) {
            this.currentLanguage = savedLanguage;
        }
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象，用于替换占位符
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        const translation = this.translations[this.currentLanguage][key] || key;

        // 替换参数占位符
        return translation.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
            return params[paramKey] || match;
        });
    }

    /**
     * 设置当前语言
     * @param {string} language - 语言代码
     */
    setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            localStorage.setItem('temp_email_language', language);
            this.updatePageTexts();
        }
    }

    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 获取支持的语言列表
     * @returns {Array} 支持的语言列表
     */
    getSupportedLanguages() {
        return Object.keys(this.translations);
    }

    /**
     * 更新页面中的所有文本
     */
    updatePageTexts() {
        // 更新页面标题和描述
        document.title = this.t('page.title');
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.content = this.t('page.description');
        }

        // 更新所有带有data-i18n属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });

        // 更新所有带有data-i18n-placeholder属性的元素
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });

        // 更新所有带有data-i18n-title属性的元素
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });
    }

    /**
     * 格式化相对时间
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的相对时间
     */
    formatRelativeTime(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMinutes < 60) {
            return `${diffMinutes} ${this.t('time.minutes_ago')}`;
        } else if (diffHours < 24) {
            return `${diffHours} ${this.t('time.hours_ago')}`;
        } else {
            return `${diffDays} ${this.t('time.days_ago')}`;
        }
    }
}

// 创建全局i18n实例
window.i18n = new I18n();

// 导出以供使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18n;
}
