/* ===== 高分辨率屏幕响应式适配样式 ===== */
/* 针对1080p、2K、4K及高DPI显示器的优化 */

/* ===== 1080p 标准桌面显示器 (1920x1080) ===== */
@media (min-width: 1920px) and (max-width: 2559px) {
    /* 容器和布局优化 */
    .container {
        max-width: 1400px; /* 避免内容过于分散 */
        padding: 30px;
    }
    
    .max-w-6xl {
        max-width: 1200px; /* 主内容区域合理宽度 */
    }
    
    /* 字体大小优化 */
    body {
        font-size: 16px;
        line-height: 1.7;
    }
    
    h1 {
        font-size: 2.5rem;
        line-height: 1.3;
    }
    
    h2 {
        font-size: 2rem;
        line-height: 1.4;
    }
    
    h3 {
        font-size: 1.5rem;
        line-height: 1.4;
    }
    
    /* 按钮组优化 */
    .button-group {
        gap: 1.5rem;
        margin-top: 1.5rem;
    }
    
    .button-group button {
        padding: 12px 24px;
        font-size: 1rem;
        min-height: 48px;
        border-radius: 8px;
    }
    
    .button-group button i {
        font-size: 1.2em;
        margin-right: 8px;
    }
    
    /* 邮箱地址显示区域 */
    .email-box {
        padding: 24px;
        font-size: 1.1rem;
        border-radius: 12px;
        min-height: 80px;
    }
    
    /* 收件箱区域优化 */
    .inbox-section.two-column-mode {
        gap: 2rem;
        grid-template-columns: 1fr 1.8fr;
    }
    
    .message-list {
        max-height: calc(100vh - 400px);
        min-height: 500px;
    }
    
    .two-column-mode .message-content-area {
        max-height: calc(100vh - 400px);
        min-height: 500px;
        padding-left: 2rem;
    }
    
    /* 邮件列表项优化 */
    .email-item, .message-preview {
        padding: 18px;
        margin-bottom: 8px;
        border-radius: 8px;
    }
    
    .email-item .email-sender {
        font-size: 1rem;
        margin-bottom: 6px;
    }
    
    .email-item .email-subject {
        font-size: 0.95rem;
        margin-bottom: 4px;
    }
    
    .email-item .email-time {
        font-size: 0.85rem;
    }
    
    /* 模态框优化 */
    .mobile-modal-content {
        max-width: 600px;
        border-radius: 16px;
        padding: 8px;
    }
    
    /* 导航栏优化 */
    header .container {
        padding: 24px 30px;
    }
    
    .nav-list {
        gap: 2.5rem;
    }
    
    .nav-link {
        font-size: 1rem;
        padding: 8px 0;
    }
    
    /* 页脚优化 */
    footer {
        padding: 3rem 0;
        margin-top: 3rem;
    }
    
    footer .container {
        padding: 0 30px;
    }
}

/* ===== 2K 高分辨率显示器 (2560x1440) ===== */
@media (min-width: 2560px) and (max-width: 3839px) {
    /* 容器和布局优化 */
    .container {
        max-width: 1600px;
        padding: 40px;
    }
    
    .max-w-6xl {
        max-width: 1400px;
    }
    
    /* 字体大小优化 */
    body {
        font-size: 18px;
        line-height: 1.8;
    }
    
    h1 {
        font-size: 3rem;
        line-height: 1.2;
    }
    
    h2 {
        font-size: 2.25rem;
        line-height: 1.3;
    }
    
    h3 {
        font-size: 1.75rem;
        line-height: 1.4;
    }
    
    /* 按钮组优化 */
    .button-group {
        gap: 2rem;
        margin-top: 2rem;
    }
    
    .button-group button {
        padding: 16px 32px;
        font-size: 1.1rem;
        min-height: 56px;
        border-radius: 10px;
    }
    
    .button-group button i {
        font-size: 1.3em;
        margin-right: 10px;
    }
    
    /* 邮箱地址显示区域 */
    .email-box {
        padding: 32px;
        font-size: 1.25rem;
        border-radius: 16px;
        min-height: 100px;
    }
    
    /* 收件箱区域优化 */
    .inbox-section.two-column-mode {
        gap: 2.5rem;
        grid-template-columns: 1fr 2fr;
    }
    
    .message-list {
        max-height: calc(100vh - 450px);
        min-height: 600px;
    }
    
    .two-column-mode .message-content-area {
        max-height: calc(100vh - 450px);
        min-height: 600px;
        padding-left: 2.5rem;
    }
    
    /* 邮件列表项优化 */
    .email-item, .message-preview {
        padding: 24px;
        margin-bottom: 12px;
        border-radius: 10px;
    }
    
    .email-item .email-sender {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }
    
    .email-item .email-subject {
        font-size: 1rem;
        margin-bottom: 6px;
    }
    
    .email-item .email-time {
        font-size: 0.9rem;
    }
    
    /* 模态框优化 */
    .mobile-modal-content {
        max-width: 700px;
        border-radius: 20px;
        padding: 12px;
    }
    
    /* 导航栏优化 */
    header .container {
        padding: 32px 40px;
    }
    
    .nav-list {
        gap: 3rem;
    }
    
    .nav-link {
        font-size: 1.1rem;
        padding: 10px 0;
    }
    
    /* 页脚优化 */
    footer {
        padding: 4rem 0;
        margin-top: 4rem;
    }
    
    footer .container {
        padding: 0 40px;
    }
}

/* ===== 4K 超高分辨率显示器 (3840x2160及以上) ===== */
@media (min-width: 3840px) {
    /* 容器和布局优化 */
    .container {
        max-width: 2000px;
        padding: 50px;
    }
    
    .max-w-6xl {
        max-width: 1800px;
    }
    
    /* 字体大小优化 */
    body {
        font-size: 20px;
        line-height: 1.8;
    }
    
    h1 {
        font-size: 3.5rem;
        line-height: 1.2;
    }
    
    h2 {
        font-size: 2.75rem;
        line-height: 1.3;
    }
    
    h3 {
        font-size: 2rem;
        line-height: 1.4;
    }
    
    /* 按钮组优化 */
    .button-group {
        gap: 2.5rem;
        margin-top: 2.5rem;
    }
    
    .button-group button {
        padding: 20px 40px;
        font-size: 1.2rem;
        min-height: 64px;
        border-radius: 12px;
    }
    
    .button-group button i {
        font-size: 1.4em;
        margin-right: 12px;
    }
    
    /* 邮箱地址显示区域 */
    .email-box {
        padding: 40px;
        font-size: 1.4rem;
        border-radius: 20px;
        min-height: 120px;
    }
    
    /* 收件箱区域优化 */
    .inbox-section.two-column-mode {
        gap: 3rem;
        grid-template-columns: 1fr 2.2fr;
    }
    
    .message-list {
        max-height: calc(100vh - 500px);
        min-height: 700px;
    }
    
    .two-column-mode .message-content-area {
        max-height: calc(100vh - 500px);
        min-height: 700px;
        padding-left: 3rem;
    }
    
    /* 邮件列表项优化 */
    .email-item, .message-preview {
        padding: 30px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .email-item .email-sender {
        font-size: 1.25rem;
        margin-bottom: 10px;
    }
    
    .email-item .email-subject {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }
    
    .email-item .email-time {
        font-size: 1rem;
    }
    
    /* 模态框优化 */
    .mobile-modal-content {
        max-width: 900px;
        border-radius: 24px;
        padding: 16px;
    }
    
    /* 导航栏优化 */
    header .container {
        padding: 40px 50px;
    }
    
    .nav-list {
        gap: 4rem;
    }
    
    .nav-link {
        font-size: 1.25rem;
        padding: 12px 0;
    }
    
    /* 页脚优化 */
    footer {
        padding: 5rem 0;
        margin-top: 5rem;
    }
    
    footer .container {
        padding: 0 50px;
    }
}

/* ===== 高DPI显示器适配 (Retina, Mac M1等) ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx) {
    /* 图标和按钮边框优化 */
    .button-group button {
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    /* 邮箱地址框边框优化 */
    .email-box {
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* 邮件列表项边框优化 */
    .email-item, .message-preview {
        border: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    /* 模态框阴影优化 */
    .mobile-modal-content {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* 导航栏阴影优化 */
    header {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* 字体渲染优化 */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* ===== 超宽屏显示器适配 (21:9, 32:9等) ===== */
@media (min-aspect-ratio: 21/9) {
    /* 限制内容宽度，避免过度拉伸 */
    .container {
        max-width: 1600px;
    }

    .max-w-6xl {
        max-width: 1400px;
    }

    /* 收件箱区域在超宽屏上的优化 */
    .inbox-section.two-column-mode {
        grid-template-columns: 1fr 2.5fr;
        gap: 3rem;
    }

    /* 按钮组在超宽屏上保持紧凑 */
    .button-group {
        max-width: 1200px;
        justify-content: flex-start;
    }
}

/* ===== 垂直空间优化 (针对较矮的高分辨率屏幕) ===== */
@media (min-width: 1920px) and (max-height: 1200px) {
    /* 减少垂直间距 */
    .container {
        padding-top: 20px;
        padding-bottom: 20px;
    }

    header .container {
        padding-top: 16px;
        padding-bottom: 16px;
    }

    .message-list {
        max-height: calc(100vh - 350px);
    }

    .two-column-mode .message-content-area {
        max-height: calc(100vh - 350px);
    }

    footer {
        margin-top: 2rem;
        padding: 2rem 0;
    }
}

/* ===== 性能优化 - 高分辨率屏幕 ===== */
@media (min-width: 1920px) {
    /* 启用硬件加速 */
    .email-item,
    .message-preview,
    .button-group button,
    .mobile-modal-content {
        transform: translateZ(0);
        will-change: transform;
    }

    /* 优化滚动性能 */
    .message-list,
    .message-content-area {
        contain: layout style paint;
    }

    /* 减少重绘 */
    .email-item:hover,
    .message-preview:hover,
    .button-group button:hover {
        transform: translateZ(0) translateY(-1px);
    }
}

/* ===== 可访问性优化 - 高分辨率屏幕 ===== */
@media (min-width: 1920px) {
    /* 焦点指示器优化 */
    button:focus,
    input:focus,
    select:focus {
        outline: 3px solid #3b82f6;
        outline-offset: 2px;
    }

    /* 高对比度模式支持 */
    @media (prefers-contrast: high) {
        .email-item,
        .message-preview {
            border: 2px solid #000;
        }

        .button-group button {
            border: 2px solid currentColor;
        }
    }

    /* 减少动画偏好支持 */
    @media (prefers-reduced-motion: reduce) {
        .email-item,
        .message-preview,
        .button-group button {
            transition: none;
        }
    }
}

/* ===== 打印样式优化 - 高分辨率 ===== */
@media print and (min-resolution: 300dpi) {
    body {
        font-size: 12pt;
        line-height: 1.5;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .button-group,
    header,
    footer {
        display: none;
    }

    .email-box {
        border: 2pt solid #000;
        padding: 12pt;
    }

    .email-item {
        border: 1pt solid #ccc;
        margin-bottom: 6pt;
        padding: 8pt;
    }
}
