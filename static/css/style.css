/* 基本重置和全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f4f7f6;
    color: #333;
    display: flex;
    justify-content: center;
}

.container {
    width: 100%;
    max-width: 900px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 2em;
}

/* 邮箱信息面板 */
.email-info-panel {
    background-color: #eaf2f8;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 25px;
    border: 1px solid #d4e6f1;
}

.email-display {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 1.1em;
}

#current-email-label {
    margin-right: 8px;
    color: #555;
}

#current-email {
    font-weight: bold;
    color: #2980b9;
    word-break: break-all; /* 长地址换行 */
    margin-right: 10px;
}

#copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #3498db;
    display: inline-flex; /* 使SVG居中 */
    align-items: center;
}
#copy-btn:hover {
    color: #217dbb;
}
#copy-btn svg {
    width: 18px; /* 调整图标大小 */
    height: 18px;
}


.expiration-time {
    font-size: 0.9em;
    color: #7f8c8d;
    display: block;
    margin-bottom: 15px;
}

.actions {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.actions button.button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 18px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 1em;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-right: 10px;
}
.actions button.button:hover {
    background-color: #2980b9;
}
.actions button.button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}


/* 内容区域布局 */
.content-area {
    display: flex;
    gap: 20px; /* 列表和内容之间的间距 */
}

.email-list-container {
    flex: 1; /* 占据可用空间的一半，或者根据内容调整 */
    min-width: 300px; /* 最小宽度 */
    max-height: 500px; /* 限制高度并允许滚动 */
    overflow-y: auto; /* 垂直滚动条 */
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

/* 邮件列表项样式 */
.email-item {
    padding: 15px;
    border-bottom: 1px solid #e1e4e8;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 6px;
    background: white;
}

.email-item:last-child {
    border-bottom: none;
}

.email-item:hover {
    background-color: #f6f8fa;
}

.email-item.active {
    background-color: #f1f8ff;
    border-left: 3px solid #0366d6;
}

.email-item .email-sender {
    font-weight: 600;
    color: #24292e;
    font-size: 0.95em;
}

.email-item .email-subject {
    color: #57606a;
    font-size: 0.9em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.email-item .email-time {
    color: #6e7781;
    font-size: 0.8em;
}

/* 新邮件高亮动画 */
.email-item.new-email {
    animation: newEmailHighlight 2s ease-out;
}

@keyframes newEmailHighlight {
    0% {
        background-color: #fff8c5;
        transform: translateX(-5px);
    }
    100% {
        background-color: white;
        transform: translateX(0);
    }
}

/* 占位符样式 */
.placeholder {
    text-align: center;
    padding: 30px 15px;
    color: #57606a;
    font-size: 0.95em;
    background: #f6f8fa;
    border-radius: 6px;
    margin: 10px;
}

/* 加载状态淡入淡出效果 */
.loading-fade {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.email-content-container {
    flex: 2; /* 占据可用空间的两倍，或者根据内容调整 */
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}


/* 邮件列表 */
#email-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#email-list li {
    padding: 12px 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s ease;
}
#email-list li:last-child {
    border-bottom: none;
}
#email-list li:hover, #email-list li.active {
    background-color: #f0f8ff; /* 淡蓝色高亮 */
}
#email-list li.placeholder {
    color: #888;
    text-align: center;
    padding: 20px;
    cursor: default;
}
#email-list li .email-sender {
    font-weight: bold;
    display: block;
    color: #333;
}
#email-list li .email-subject {
    display: block;
    color: #555;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    max-width: 95%; /* 防止文本溢出容器 */
}
#email-list li .email-time {
    font-size: 0.8em;
    color: #777;
    display: block;
    margin-top: 4px;
}

.email-item.new-email {
    animation: highlightNew 3s ease-out;
}

@keyframes highlightNew {
    0% {
        background-color: rgba(255, 255, 0, 0.3);
        transform: translateX(-5px);
    }
    100% {
        background-color: transparent;
        transform: translateX(0);
    }
}

/* 邮件内容 */
.email-content-container h2, .email-list-container h2 {
    margin-top: 0;
    font-size: 1.4em;
    color: #34495e;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

#email-content-header p {
    margin: 5px 0;
    font-size: 0.95em;
}
#email-content-header strong {
    color: #333;
}

.email-body-text {
    white-space: pre-wrap; /* 保留空白符和换行符 */
    word-wrap: break-word; /* 长单词换行 */
    background-color: #fdfdfd;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #eee;
    margin-top: 15px;
    min-height: 150px; /* 给一个最小高度 */
    max-height: 400px; /* 限制最大高度并滚动 */
    overflow-y: auto;
    font-size: 0.9em;
    line-height: 1.5;
}

/* 页脚 */
footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    font-size: 0.9em;
    color: #7f8c8d;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .content-area {
        flex-direction: column; /* 在小屏幕上垂直堆叠 */
    }
    .email-list-container, .email-content-container {
        min-width: auto;
        max-height: 300px; /* 调整小屏幕上的最大高度 */
    }
    header h1 {
        font-size: 1.6em;
    }
    .actions button.button {
        padding: 8px 12px;
        font-size: 0.9em;
    }
}

/* --- 通知区域样式 --- */
.notification {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    color: #fff;
    display: flex; /* 使用flex布局让关闭按钮在右侧 */
    justify-content: space-between; /* 内容和关闭按钮分开 */
    align-items: center; /* 垂直居中 */
    opacity: 0; /* 初始透明 */
    transform: translateY(-20px); /* 初始向上偏移 */
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    display: none; /* 默认隐藏，JS控制显示 */
}

.notification.show {
    display: flex; /* 或者 block，取决于你的布局需求 */
    opacity: 1;
    transform: translateY(0);
}

.notification.error {
    background-color: #e74c3c; /* 红色 - 错误 */
    border: 1px solid #c0392b;
}

.notification.success {
    background-color: #2ecc71; /* 绿色 - 成功 */
    border: 1px solid #27ae60;
}

.notification.info {
    background-color: #3498db; /* 蓝色 - 信息 */
    border: 1px solid #2980b9;
}

#notification-message {
    flex-grow: 1; /* 占据剩余空间 */
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    line-height: 1;
    cursor: pointer;
    padding: 0 0 0 15px; /* 左边留出一些间距 */
    opacity: 0.7;
}
.notification-close:hover {
    opacity: 1;
}

/* 邮箱建议相关样式 */
.suggestion-item {
    transition: all 0.2s ease;
}

.suggestion-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.select-suggestion-btn {
    white-space: nowrap;
    flex-shrink: 0;
}

/* 移动端建议样式优化 */
@media (max-width: 768px) {
    .suggestion-item {
        padding: 12px;
    }

    .suggestion-item .flex {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .select-suggestion-btn {
        width: 100%;
        justify-content: center;
        padding: 8px 16px;
        min-height: 44px;
    }

    .suggestion-item .font-mono {
        word-break: break-all;
        margin-bottom: 4px;
        text-align: center;
    }
}

/* 移动端弹窗内容样式 */
.mobile-modal-content {
    max-height: 90vh;
    overflow-y: auto;
}
