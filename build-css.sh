#!/bin/bash

# Tailwind CSS v3 构建脚本
echo "🚀 开始构建优化的Tailwind CSS v3..."

# 检查是否安装了Node.js和npm
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 需要安装Node.js"
    echo "请访问 https://nodejs.org/ 下载安装"
    exit 1
fi

# 创建输出目录
mkdir -p static/css

# 使用Tailwind CSS v3的方式构建
echo "📦 使用Tailwind CSS v3构建样式..."

# 创建输入CSS文件
cat > static/css/input.css << 'EOF'
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200 hover:bg-blue-700;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-700 font-bold py-2 px-4 rounded-lg transition-colors duration-200 hover:bg-gray-300;
  }

  .email-box {
    @apply bg-gray-100 p-4 rounded-lg font-mono text-blue-600 font-bold break-all;
  }

  .card {
    @apply bg-white shadow-lg rounded-lg p-6;
  }

  .haptic-feedback:active {
    @apply transform scale-95 transition-transform duration-100;
  }
}

/* 移动端优化 */
@layer utilities {
  @media (max-width: 640px) {
    .button-group {
      @apply grid grid-cols-2 gap-2;
    }

    .email-box {
      @apply text-sm p-3;
    }
  }
}
EOF

# 使用Tailwind CLI构建CSS
echo "🔨 运行Tailwind CSS构建..."
npx tailwindcss -i static/css/input.css -o static/css/tailwind-output.css --minify

# 检查构建结果
if [ -f "static/css/tailwind-output.css" ]; then
    echo "🎉 Tailwind CSS v3 构建成功！"
    echo "💡 现在可以在生产环境中使用优化的CSS文件了！"

    # 获取文件大小
    file_size=$(du -h static/css/tailwind-output.css | cut -f1)
    echo "📊 文件大小: $file_size"

    # 清理临时文件
    rm -f static/css/input.css
else
    echo "❌ 构建失败！"
    exit 1
fi
