"""
MySQL数据库表结构定义
基于现有SQLite表结构设计，优化为MySQL最佳实践
"""

from sqlalchemy import text
from sqlalchemy.engine import Engine
import logging


class MySQLSchemaManager:
    """MySQL表结构管理器"""
    
    def __init__(self, engine: Engine):
        self.engine = engine
        self.logger = logging.getLogger(__name__)
    
    def get_create_table_statements(self) -> list[str]:
        """获取创建表的SQL语句列表"""
        return [
            # 临时邮箱表
            """
            CREATE TABLE IF NOT EXISTS temporary_emails (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                address VARCHAR(255) NOT NULL,
                session_id VARCHAR(128) NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                
                -- 索引
                UNIQUE KEY uk_address (address),
                KEY idx_session_id (session_id),
                KEY idx_expires_at (expires_at),
                KEY idx_created_at (created_at)
            ) ENGINE=InnoDB 
              DEFAULT CHARSET=utf8mb4 
              COLLATE=utf8mb4_unicode_ci 
              COMMENT='临时邮箱地址表'
            """,
            
            # 接收邮件表
            """
            CREATE TABLE IF NOT EXISTS received_mails (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                email_address_id BIGINT UNSIGNED NOT NULL,
                sender VARCHAR(255) NULL,
                subject TEXT NULL,
                body_text LONGTEXT NULL,
                body_html LONGTEXT NULL,
                received_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                
                -- 外键约束
                CONSTRAINT fk_received_mails_email_address_id 
                    FOREIGN KEY (email_address_id) 
                    REFERENCES temporary_emails (id) 
                    ON DELETE CASCADE,
                
                -- 索引
                KEY idx_email_address_id (email_address_id),
                KEY idx_received_at (received_at),
                KEY idx_sender (sender)
            ) ENGINE=InnoDB 
              DEFAULT CHARSET=utf8mb4 
              COLLATE=utf8mb4_unicode_ci 
              COMMENT='接收邮件表'
            """,
            
            # 邮箱历史记录表
            """
            CREATE TABLE IF NOT EXISTS email_history (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                session_id VARCHAR(128) NOT NULL,
                email_address VARCHAR(255) NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT FALSE,
                
                -- 外键约束
                CONSTRAINT fk_email_history_email_address 
                    FOREIGN KEY (email_address) 
                    REFERENCES temporary_emails (address) 
                    ON DELETE CASCADE,
                
                -- 索引
                KEY idx_session_id (session_id),
                KEY idx_email_address (email_address),
                KEY idx_created_at (created_at),
                KEY idx_is_active (is_active)
            ) ENGINE=InnoDB 
              DEFAULT CHARSET=utf8mb4 
              COLLATE=utf8mb4_unicode_ci 
              COMMENT='邮箱历史记录表'
            """
        ]
    
    def create_tables(self) -> bool:
        """创建所有表"""
        try:
            statements = self.get_create_table_statements()
            
            with self.engine.connect() as conn:
                for statement in statements:
                    self.logger.info(f"执行SQL: {statement[:100]}...")
                    conn.execute(text(statement))
                    conn.commit()
            
            self.logger.info("MySQL表结构创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建MySQL表结构失败: {e}")
            return False
    
    def drop_tables(self) -> bool:
        """删除所有表（谨慎使用）"""
        try:
            drop_statements = [
                "SET FOREIGN_KEY_CHECKS = 0",
                "DROP TABLE IF EXISTS email_history",
                "DROP TABLE IF EXISTS received_mails", 
                "DROP TABLE IF EXISTS temporary_emails",
                "SET FOREIGN_KEY_CHECKS = 1"
            ]
            
            with self.engine.connect() as conn:
                for statement in drop_statements:
                    conn.execute(text(statement))
                    conn.commit()
            
            self.logger.info("MySQL表删除完成")
            return True
            
        except Exception as e:
            self.logger.error(f"删除MySQL表失败: {e}")
            return False
    
    def check_tables_exist(self) -> dict[str, bool]:
        """检查表是否存在"""
        tables = ['temporary_emails', 'received_mails', 'email_history']
        result = {}
        
        try:
            with self.engine.connect() as conn:
                for table in tables:
                    query = text("""
                        SELECT COUNT(*) as count 
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE() 
                        AND table_name = :table_name
                    """)
                    
                    row = conn.execute(query, {"table_name": table}).fetchone()
                    result[table] = row[0] > 0 if row else False
            
            return result
            
        except Exception as e:
            self.logger.error(f"检查表存在性失败: {e}")
            return {table: False for table in tables}
    
    def get_table_info(self) -> dict:
        """获取表信息"""
        try:
            with self.engine.connect() as conn:
                # 获取表统计信息
                query = text("""
                    SELECT 
                        table_name,
                        table_rows,
                        data_length,
                        index_length,
                        table_comment
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE()
                    AND table_name IN ('temporary_emails', 'received_mails', 'email_history')
                """)
                
                result = conn.execute(query)
                tables_info = {}
                
                for row in result:
                    tables_info[row[0]] = {
                        'rows': row[1] or 0,
                        'data_size': row[2] or 0,
                        'index_size': row[3] or 0,
                        'comment': row[4] or ''
                    }
                
                return tables_info
                
        except Exception as e:
            self.logger.error(f"获取表信息失败: {e}")
            return {}
    
    def optimize_tables(self) -> bool:
        """优化表"""
        try:
            tables = ['temporary_emails', 'received_mails', 'email_history']
            
            with self.engine.connect() as conn:
                for table in tables:
                    conn.execute(text(f"OPTIMIZE TABLE {table}"))
                    conn.commit()
            
            self.logger.info("表优化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"表优化失败: {e}")
            return False
