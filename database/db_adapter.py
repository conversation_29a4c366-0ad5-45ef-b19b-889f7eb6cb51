"""
数据库适配器 - 支持SQLite和MySQL的统一接口
提供平滑的数据库迁移支持
"""

import os
import sqlite3
import logging
from typing import Optional, Dict, Any, List, Tuple, Union
from pathlib import Path
from contextlib import contextmanager
from flask import current_app
from sqlalchemy import text
from sqlalchemy.engine import Engine

from .mysql_config import MySQLConnectionManager, get_mysql_config
from .mysql_schema import MySQLSchemaManager


class DatabaseAdapter:
    """数据库适配器基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_connection(self):
        """获取数据库连接"""
        raise NotImplementedError
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询并返回结果"""
        raise NotImplementedError
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响行数"""
        raise NotImplementedError
    
    def close(self):
        """关闭连接"""
        pass


class SQLiteAdapter(DatabaseAdapter):
    """SQLite数据库适配器"""
    
    def __init__(self, db_path: Union[str, Path]):
        super().__init__()
        self.db_path = str(db_path)
        self._connection = None
    
    @contextmanager
    def get_connection(self):
        """获取SQLite连接"""
        conn = None
        try:
            # 使用与原app.py相同的连接配置
            timeout = int(os.getenv('SQLITE_TIMEOUT', '20'))
            conn = sqlite3.connect(self.db_path, timeout=timeout)
            conn.row_factory = sqlite3.Row
            
            # 应用性能优化设置
            conn.execute('PRAGMA journal_mode=WAL')
            conn.execute('PRAGMA synchronous=NORMAL')
            conn.execute('PRAGMA temp_store=MEMORY')
            conn.execute('PRAGMA mmap_size=268435456')
            
            cache_size = int(os.getenv('SQLITE_CACHE_SIZE', '-8000'))
            conn.execute(f'PRAGMA cache_size={cache_size}')
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询并返回结果"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响行数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor.rowcount


class MySQLAdapter(DatabaseAdapter):
    """MySQL数据库适配器"""
    
    def __init__(self):
        super().__init__()
        self.config = get_mysql_config()
        self.connection_manager = MySQLConnectionManager(self.config)
        self._engine: Optional[Engine] = None
    
    @contextmanager
    def get_connection(self):
        """获取MySQL连接"""
        if not self._engine:
            self._engine = self.connection_manager.get_engine()
        
        conn = None
        try:
            conn = self._engine.connect()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询并返回结果"""
        with self.get_connection() as conn:
            if params:
                result = conn.execute(text(query), params)
            else:
                result = conn.execute(text(query))
            
            rows = result.fetchall()
            return [dict(row._mapping) for row in rows]
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响行数"""
        with self.get_connection() as conn:
            if params:
                result = conn.execute(text(query), params)
            else:
                result = conn.execute(text(query))
            
            conn.commit()
            return result.rowcount
    
    def close(self):
        """关闭连接"""
        self.connection_manager.close()


class DatabaseManager:
    """数据库管理器 - 统一的数据库操作接口"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._adapter: Optional[DatabaseAdapter] = None
        self._db_type = None
    
    def initialize(self, db_type: str = None) -> bool:
        """初始化数据库适配器"""
        try:
            if db_type is None:
                db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
            
            self._db_type = db_type
            
            if db_type == 'mysql':
                self._adapter = MySQLAdapter()
                self.logger.info("MySQL适配器已初始化")
                
                # 测试连接
                success, message = self._adapter.connection_manager.test_connection()
                if not success:
                    self.logger.error(f"MySQL连接测试失败: {message}")
                    return False
                
                # 创建数据库和表
                if not self._adapter.connection_manager.create_database_if_not_exists():
                    return False
                
                schema_manager = MySQLSchemaManager(self._adapter.connection_manager.get_engine())
                if not schema_manager.create_tables():
                    return False
                
            elif db_type == 'sqlite':
                db_path = current_app.config.get('DATABASE_PATH') if current_app else 'database/tempmail.db'
                self._adapter = SQLiteAdapter(db_path)
                self.logger.info(f"SQLite适配器已初始化: {db_path}")
                
            else:
                self.logger.error(f"不支持的数据库类型: {db_type}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False
    
    @property
    def adapter(self) -> DatabaseAdapter:
        """获取当前数据库适配器"""
        if not self._adapter:
            if not self.initialize():
                raise RuntimeError("数据库适配器未初始化")
        return self._adapter
    
    @property
    def db_type(self) -> str:
        """获取当前数据库类型"""
        return self._db_type or 'sqlite'
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询"""
        return self.adapter.execute_query(query, params)
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新"""
        return self.adapter.execute_update(query, params)
    
    def close(self):
        """关闭数据库连接"""
        if self._adapter:
            self._adapter.close()


# 全局数据库管理器实例
db_manager = DatabaseManager()
