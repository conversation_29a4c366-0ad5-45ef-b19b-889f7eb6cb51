#!/usr/bin/env python3
"""
SQLite到MySQL数据迁移脚本
将现有SQLite数据库中的数据迁移到MySQL数据库
"""

import os
import sys
import sqlite3
import logging
from pathlib import Path
from typing import List, Dict, Any
import click
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from database.mysql_config import get_mysql_config, MySQLConnectionManager
from database.mysql_schema import MySQLSchemaManager
from sqlalchemy import text

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SQLiteToMySQLMigrator:
    """SQLite到MySQL数据迁移器"""
    
    def __init__(self, sqlite_path: str):
        self.sqlite_path = sqlite_path
        self.mysql_config = get_mysql_config()
        self.mysql_manager = MySQLConnectionManager(self.mysql_config)
        self.migration_stats = {
            'temporary_emails': 0,
            'received_mails': 0,
            'email_history': 0
        }
    
    def validate_sqlite_database(self) -> bool:
        """验证SQLite数据库"""
        if not os.path.exists(self.sqlite_path):
            logger.error(f"SQLite数据库文件不存在: {self.sqlite_path}")
            return False
        
        try:
            conn = sqlite3.connect(self.sqlite_path)
            cursor = conn.cursor()
            
            # 检查必要的表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['temporary_emails', 'received_mails', 'email_history']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                logger.error(f"SQLite数据库缺少必要的表: {missing_tables}")
                return False
            
            conn.close()
            logger.info("SQLite数据库验证通过")
            return True
            
        except Exception as e:
            logger.error(f"验证SQLite数据库失败: {e}")
            return False
    
    def setup_mysql_database(self) -> bool:
        """设置MySQL数据库"""
        try:
            # 测试连接
            success, message = self.mysql_manager.test_connection()
            if not success:
                logger.error(f"MySQL连接失败: {message}")
                return False
            
            # 创建数据库
            if not self.mysql_manager.create_database_if_not_exists():
                logger.error("MySQL数据库创建失败")
                return False
            
            # 创建表结构
            engine = self.mysql_manager.get_engine()
            schema_manager = MySQLSchemaManager(engine)
            
            if not schema_manager.create_tables():
                logger.error("MySQL表结构创建失败")
                return False
            
            logger.info("MySQL数据库设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置MySQL数据库失败: {e}")
            return False
    
    def get_sqlite_data(self, table_name: str) -> List[Dict[str, Any]]:
        """从SQLite获取表数据"""
        try:
            conn = sqlite3.connect(self.sqlite_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute(f"SELECT * FROM {table_name}")
            rows = cursor.fetchall()
            
            data = [dict(row) for row in rows]
            conn.close()
            
            logger.info(f"从SQLite读取 {table_name} 表数据: {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"读取SQLite表 {table_name} 失败: {e}")
            return []
    
    def migrate_temporary_emails(self) -> bool:
        """迁移临时邮箱表"""
        logger.info("开始迁移 temporary_emails 表...")
        
        try:
            data = self.get_sqlite_data('temporary_emails')
            if not data:
                logger.info("temporary_emails 表无数据需要迁移")
                return True
            
            engine = self.mysql_manager.get_engine()
            
            with engine.connect() as conn:
                for row in data:
                    # 插入数据到MySQL
                    query = text("""
                        INSERT INTO temporary_emails 
                        (id, address, session_id, expires_at, created_at)
                        VALUES (:id, :address, :session_id, :expires_at, :created_at)
                        ON DUPLICATE KEY UPDATE
                        address = VALUES(address),
                        session_id = VALUES(session_id),
                        expires_at = VALUES(expires_at),
                        created_at = VALUES(created_at)
                    """)
                    
                    conn.execute(query, {
                        'id': row['id'],
                        'address': row['address'],
                        'session_id': row['session_id'],
                        'expires_at': row['expires_at'],
                        'created_at': row['created_at']
                    })
                
                conn.commit()
            
            self.migration_stats['temporary_emails'] = len(data)
            logger.info(f"temporary_emails 表迁移完成: {len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"迁移 temporary_emails 表失败: {e}")
            return False
    
    def migrate_received_mails(self) -> bool:
        """迁移接收邮件表"""
        logger.info("开始迁移 received_mails 表...")
        
        try:
            data = self.get_sqlite_data('received_mails')
            if not data:
                logger.info("received_mails 表无数据需要迁移")
                return True
            
            engine = self.mysql_manager.get_engine()
            
            with engine.connect() as conn:
                for row in data:
                    query = text("""
                        INSERT INTO received_mails 
                        (id, email_address_id, sender, subject, body_text, body_html, received_at)
                        VALUES (:id, :email_address_id, :sender, :subject, :body_text, :body_html, :received_at)
                        ON DUPLICATE KEY UPDATE
                        email_address_id = VALUES(email_address_id),
                        sender = VALUES(sender),
                        subject = VALUES(subject),
                        body_text = VALUES(body_text),
                        body_html = VALUES(body_html),
                        received_at = VALUES(received_at)
                    """)
                    
                    conn.execute(query, {
                        'id': row['id'],
                        'email_address_id': row['email_address_id'],
                        'sender': row['sender'],
                        'subject': row['subject'],
                        'body_text': row['body_text'],
                        'body_html': row['body_html'],
                        'received_at': row['received_at']
                    })
                
                conn.commit()
            
            self.migration_stats['received_mails'] = len(data)
            logger.info(f"received_mails 表迁移完成: {len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"迁移 received_mails 表失败: {e}")
            return False
    
    def migrate_email_history(self) -> bool:
        """迁移邮箱历史表"""
        logger.info("开始迁移 email_history 表...")
        
        try:
            data = self.get_sqlite_data('email_history')
            if not data:
                logger.info("email_history 表无数据需要迁移")
                return True
            
            engine = self.mysql_manager.get_engine()
            
            with engine.connect() as conn:
                for row in data:
                    query = text("""
                        INSERT INTO email_history 
                        (id, session_id, email_address, created_at, expires_at, is_active)
                        VALUES (:id, :session_id, :email_address, :created_at, :expires_at, :is_active)
                        ON DUPLICATE KEY UPDATE
                        session_id = VALUES(session_id),
                        email_address = VALUES(email_address),
                        created_at = VALUES(created_at),
                        expires_at = VALUES(expires_at),
                        is_active = VALUES(is_active)
                    """)
                    
                    conn.execute(query, {
                        'id': row['id'],
                        'session_id': row['session_id'],
                        'email_address': row['email_address'],
                        'created_at': row['created_at'],
                        'expires_at': row['expires_at'],
                        'is_active': bool(row['is_active'])
                    })
                
                conn.commit()
            
            self.migration_stats['email_history'] = len(data)
            logger.info(f"email_history 表迁移完成: {len(data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"迁移 email_history 表失败: {e}")
            return False
    
    def verify_migration(self) -> bool:
        """验证迁移结果"""
        logger.info("验证迁移结果...")
        
        try:
            engine = self.mysql_manager.get_engine()
            
            with engine.connect() as conn:
                for table_name in self.migration_stats.keys():
                    result = conn.execute(text(f"SELECT COUNT(*) as count FROM {table_name}"))
                    mysql_count = result.fetchone()[0]
                    sqlite_count = self.migration_stats[table_name]
                    
                    if mysql_count == sqlite_count:
                        logger.info(f"✅ {table_name}: {mysql_count} 条记录 (匹配)")
                    else:
                        logger.warning(f"⚠️ {table_name}: MySQL({mysql_count}) != SQLite({sqlite_count})")
            
            logger.info("迁移验证完成")
            return True
            
        except Exception as e:
            logger.error(f"验证迁移失败: {e}")
            return False
    
    def run_migration(self) -> bool:
        """执行完整迁移"""
        logger.info("开始数据迁移...")
        
        # 验证SQLite数据库
        if not self.validate_sqlite_database():
            return False
        
        # 设置MySQL数据库
        if not self.setup_mysql_database():
            return False
        
        # 迁移数据
        migration_steps = [
            self.migrate_temporary_emails,
            self.migrate_received_mails,
            self.migrate_email_history
        ]
        
        for step in migration_steps:
            if not step():
                logger.error("迁移失败")
                return False
        
        # 验证迁移结果
        if not self.verify_migration():
            return False
        
        logger.info("🎉 数据迁移完成！")
        return True


@click.command()
@click.option('--sqlite-path', default='database/tempmail.db', 
              help='SQLite数据库文件路径')
@click.option('--dry-run', is_flag=True, 
              help='仅验证不执行实际迁移')
def migrate(sqlite_path, dry_run):
    """将SQLite数据迁移到MySQL"""
    
    if dry_run:
        click.echo("🔍 执行迁移预检查...")
        migrator = SQLiteToMySQLMigrator(sqlite_path)
        
        # 验证SQLite
        if not migrator.validate_sqlite_database():
            click.echo("❌ SQLite数据库验证失败")
            sys.exit(1)
        
        # 验证MySQL配置
        errors = migrator.mysql_config.validate()
        if errors:
            click.echo("❌ MySQL配置验证失败:")
            for error in errors:
                click.echo(f"  - {error}")
            sys.exit(1)
        
        click.echo("✅ 迁移预检查通过")
        return
    
    # 执行实际迁移
    click.echo("🚀 开始数据迁移...")
    migrator = SQLiteToMySQLMigrator(sqlite_path)
    
    if migrator.run_migration():
        click.echo("✅ 迁移成功完成")
    else:
        click.echo("❌ 迁移失败")
        sys.exit(1)


if __name__ == '__main__':
    migrate()
