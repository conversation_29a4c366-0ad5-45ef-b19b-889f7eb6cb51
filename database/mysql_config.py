"""
MySQL数据库配置和连接管理模块
支持从SQLite平滑迁移到MySQL
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError
from flask import current_app


class MySQLConfig:
    """MySQL数据库配置类"""
    
    def __init__(self):
        self.host = os.getenv('MYSQL_HOST', 'localhost')
        self.port = int(os.getenv('MYSQL_PORT', '3306'))
        self.database = os.getenv('MYSQL_DATABASE', 'tempmail')
        self.username = os.getenv('MYSQL_USERNAME', 'tempmail_user')
        self.password = os.getenv('MYSQL_PASSWORD', '')
        self.charset = os.getenv('MYSQL_CHARSET', 'utf8mb4')
        self.collation = os.getenv('MYSQL_COLLATION', 'utf8mb4_unicode_ci')
        
        # 连接池配置
        self.pool_size = int(os.getenv('MYSQL_POOL_SIZE', '10'))
        self.max_overflow = int(os.getenv('MYSQL_MAX_OVERFLOW', '20'))
        self.pool_timeout = int(os.getenv('MYSQL_POOL_TIMEOUT', '30'))
        self.pool_recycle = int(os.getenv('MYSQL_POOL_RECYCLE', '3600'))
        
        # SSL配置
        self.ssl_disabled = os.getenv('MYSQL_SSL_DISABLED', 'false').lower() == 'true'
        self.ssl_ca = os.getenv('MYSQL_SSL_CA', '')
        self.ssl_cert = os.getenv('MYSQL_SSL_CERT', '')
        self.ssl_key = os.getenv('MYSQL_SSL_KEY', '')
    
    def get_connection_url(self, include_database: bool = True) -> str:
        """获取MySQL连接URL"""
        base_url = f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}"
        if include_database:
            base_url += f"/{self.database}"
        
        # 添加连接参数
        params = [
            f"charset={self.charset}",
            f"autocommit=true"
        ]
        
        if self.ssl_disabled:
            params.append("ssl_disabled=true")
        elif self.ssl_ca:
            params.append(f"ssl_ca={self.ssl_ca}")
            if self.ssl_cert:
                params.append(f"ssl_cert={self.ssl_cert}")
            if self.ssl_key:
                params.append(f"ssl_key={self.ssl_key}")
        
        if params:
            base_url += "?" + "&".join(params)
        
        return base_url
    
    def get_engine_config(self) -> Dict[str, Any]:
        """获取SQLAlchemy引擎配置"""
        return {
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'pool_timeout': self.pool_timeout,
            'pool_recycle': self.pool_recycle,
            'pool_pre_ping': True,  # 连接前检查连接有效性
            'echo': os.getenv('MYSQL_ECHO_SQL', 'false').lower() == 'true'
        }
    
    def validate(self) -> list:
        """验证配置，返回错误列表"""
        errors = []
        
        if not self.host:
            errors.append("MySQL主机地址不能为空")
        
        if not self.database:
            errors.append("MySQL数据库名不能为空")
        
        if not self.username:
            errors.append("MySQL用户名不能为空")
        
        if not self.password:
            errors.append("MySQL密码不能为空")
        
        if self.port < 1 or self.port > 65535:
            errors.append("MySQL端口号必须在1-65535之间")
        
        return errors


class MySQLConnectionManager:
    """MySQL连接管理器"""
    
    def __init__(self, config: MySQLConfig):
        self.config = config
        self._engine: Optional[Engine] = None
        self.logger = logging.getLogger(__name__)
    
    def get_engine(self) -> Engine:
        """获取SQLAlchemy引擎"""
        if self._engine is None:
            try:
                connection_url = self.config.get_connection_url()
                engine_config = self.config.get_engine_config()
                
                self._engine = create_engine(connection_url, **engine_config)
                self.logger.info(f"MySQL引擎已创建: {self.config.host}:{self.config.port}/{self.config.database}")
                
            except Exception as e:
                self.logger.error(f"创建MySQL引擎失败: {e}")
                raise
        
        return self._engine
    
    def test_connection(self) -> tuple[bool, str]:
        """测试数据库连接"""
        try:
            engine = self.get_engine()
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                if row and row[0] == 1:
                    return True, "连接成功"
                else:
                    return False, "连接测试失败"
        
        except SQLAlchemyError as e:
            return False, f"数据库连接错误: {str(e)}"
        except Exception as e:
            return False, f"未知错误: {str(e)}"
    
    def create_database_if_not_exists(self) -> bool:
        """如果数据库不存在则创建"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            connection_url = self.config.get_connection_url(include_database=False)
            engine_config = self.config.get_engine_config()
            temp_engine = create_engine(connection_url, **engine_config)
            
            with temp_engine.connect() as conn:
                # 检查数据库是否存在
                result = conn.execute(text(
                    "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = :db_name"
                ), {"db_name": self.config.database})
                
                if not result.fetchone():
                    # 创建数据库
                    conn.execute(text(f"""
                        CREATE DATABASE `{self.config.database}` 
                        CHARACTER SET {self.config.charset} 
                        COLLATE {self.config.collation}
                    """))
                    conn.commit()
                    self.logger.info(f"数据库 {self.config.database} 创建成功")
                else:
                    self.logger.info(f"数据库 {self.config.database} 已存在")
            
            temp_engine.dispose()
            return True
            
        except Exception as e:
            self.logger.error(f"创建数据库失败: {e}")
            return False
    
    def close(self):
        """关闭连接"""
        if self._engine:
            self._engine.dispose()
            self._engine = None
            self.logger.info("MySQL连接已关闭")


def get_mysql_config() -> MySQLConfig:
    """获取MySQL配置实例"""
    return MySQLConfig()


def get_mysql_connection_manager() -> MySQLConnectionManager:
    """获取MySQL连接管理器实例"""
    config = get_mysql_config()
    return MySQLConnectionManager(config)
