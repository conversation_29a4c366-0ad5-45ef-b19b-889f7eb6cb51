#!/usr/bin/env python3
"""
MySQL数据库管理命令行工具
提供数据库初始化、测试、维护等功能
"""

import os
import sys
import click
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from database.mysql_config import get_mysql_config, MySQLConnectionManager
from database.mysql_schema import MySQLSchemaManager

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
def cli():
    """MySQL数据库管理工具"""
    pass


@cli.command()
def test_connection():
    """测试MySQL数据库连接"""
    click.echo("测试MySQL数据库连接...")
    
    try:
        config = get_mysql_config()
        
        # 验证配置
        errors = config.validate()
        if errors:
            click.echo("配置验证失败:")
            for error in errors:
                click.echo(f"  - {error}")
            return
        
        # 测试连接
        manager = MySQLConnectionManager(config)
        success, message = manager.test_connection()
        
        if success:
            click.echo(f"✅ 连接成功: {message}")
            click.echo(f"数据库: {config.host}:{config.port}/{config.database}")
        else:
            click.echo(f"❌ 连接失败: {message}")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 连接测试失败: {e}")
        sys.exit(1)


@cli.command()
def create_database():
    """创建MySQL数据库"""
    click.echo("创建MySQL数据库...")
    
    try:
        config = get_mysql_config()
        manager = MySQLConnectionManager(config)
        
        if manager.create_database_if_not_exists():
            click.echo(f"✅ 数据库 {config.database} 创建成功或已存在")
        else:
            click.echo(f"❌ 数据库 {config.database} 创建失败")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 创建数据库失败: {e}")
        sys.exit(1)


@cli.command()
def create_tables():
    """创建数据库表结构"""
    click.echo("创建数据库表结构...")
    
    try:
        config = get_mysql_config()
        manager = MySQLConnectionManager(config)
        
        # 确保数据库存在
        if not manager.create_database_if_not_exists():
            click.echo("❌ 数据库创建失败")
            sys.exit(1)
        
        # 创建表结构
        engine = manager.get_engine()
        schema_manager = MySQLSchemaManager(engine)
        
        if schema_manager.create_tables():
            click.echo("✅ 数据库表结构创建成功")
        else:
            click.echo("❌ 数据库表结构创建失败")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 创建表结构失败: {e}")
        sys.exit(1)


@cli.command()
@click.confirmation_option(prompt='确定要删除所有表吗？这将删除所有数据！')
def drop_tables():
    """删除所有数据库表（危险操作）"""
    click.echo("删除数据库表...")
    
    try:
        config = get_mysql_config()
        manager = MySQLConnectionManager(config)
        engine = manager.get_engine()
        schema_manager = MySQLSchemaManager(engine)
        
        if schema_manager.drop_tables():
            click.echo("✅ 数据库表删除成功")
        else:
            click.echo("❌ 数据库表删除失败")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 删除表失败: {e}")
        sys.exit(1)


@cli.command()
def check_tables():
    """检查数据库表状态"""
    click.echo("检查数据库表状态...")
    
    try:
        config = get_mysql_config()
        manager = MySQLConnectionManager(config)
        engine = manager.get_engine()
        schema_manager = MySQLSchemaManager(engine)
        
        # 检查表是否存在
        tables_status = schema_manager.check_tables_exist()
        
        click.echo("表存在状态:")
        for table, exists in tables_status.items():
            status = "✅ 存在" if exists else "❌ 不存在"
            click.echo(f"  {table}: {status}")
        
        # 获取表信息
        if any(tables_status.values()):
            click.echo("\n表详细信息:")
            tables_info = schema_manager.get_table_info()
            
            for table, info in tables_info.items():
                click.echo(f"  {table}:")
                click.echo(f"    行数: {info['rows']}")
                click.echo(f"    数据大小: {info['data_size']} bytes")
                click.echo(f"    索引大小: {info['index_size']} bytes")
                if info['comment']:
                    click.echo(f"    注释: {info['comment']}")
                    
    except Exception as e:
        click.echo(f"❌ 检查表状态失败: {e}")
        sys.exit(1)


@cli.command()
def optimize_tables():
    """优化数据库表"""
    click.echo("优化数据库表...")
    
    try:
        config = get_mysql_config()
        manager = MySQLConnectionManager(config)
        engine = manager.get_engine()
        schema_manager = MySQLSchemaManager(engine)
        
        if schema_manager.optimize_tables():
            click.echo("✅ 数据库表优化成功")
        else:
            click.echo("❌ 数据库表优化失败")
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"❌ 优化表失败: {e}")
        sys.exit(1)


@cli.command()
def show_config():
    """显示当前MySQL配置"""
    click.echo("当前MySQL配置:")
    
    try:
        config = get_mysql_config()
        
        click.echo(f"主机: {config.host}")
        click.echo(f"端口: {config.port}")
        click.echo(f"数据库: {config.database}")
        click.echo(f"用户名: {config.username}")
        click.echo(f"字符集: {config.charset}")
        click.echo(f"排序规则: {config.collation}")
        click.echo(f"连接池大小: {config.pool_size}")
        click.echo(f"最大溢出: {config.max_overflow}")
        click.echo(f"连接超时: {config.pool_timeout}秒")
        click.echo(f"连接回收: {config.pool_recycle}秒")
        click.echo(f"SSL禁用: {config.ssl_disabled}")
        
        # 验证配置
        errors = config.validate()
        if errors:
            click.echo("\n⚠️ 配置问题:")
            for error in errors:
                click.echo(f"  - {error}")
        else:
            click.echo("\n✅ 配置验证通过")
            
    except Exception as e:
        click.echo(f"❌ 获取配置失败: {e}")
        sys.exit(1)


@cli.command()
def init_all():
    """完整初始化：创建数据库和表结构"""
    click.echo("开始完整初始化...")
    
    try:
        # 测试连接
        click.echo("1. 测试数据库连接...")
        config = get_mysql_config()
        manager = MySQLConnectionManager(config)
        success, message = manager.test_connection()
        
        if not success:
            click.echo(f"❌ 连接失败: {message}")
            sys.exit(1)
        click.echo("✅ 连接测试通过")
        
        # 创建数据库
        click.echo("2. 创建数据库...")
        if not manager.create_database_if_not_exists():
            click.echo("❌ 数据库创建失败")
            sys.exit(1)
        click.echo("✅ 数据库创建成功")
        
        # 创建表结构
        click.echo("3. 创建表结构...")
        engine = manager.get_engine()
        schema_manager = MySQLSchemaManager(engine)
        
        if not schema_manager.create_tables():
            click.echo("❌ 表结构创建失败")
            sys.exit(1)
        click.echo("✅ 表结构创建成功")
        
        click.echo("\n🎉 MySQL数据库初始化完成！")
        
    except Exception as e:
        click.echo(f"❌ 初始化失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    cli()
