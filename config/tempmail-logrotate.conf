# 临时邮箱系统统一日志轮转配置
# 放置在 /etc/logrotate.d/tempmail
#
# 分层轮转策略：
# - 高频日志：每日轮转，保留14天，50MB触发
# - 中频日志：每日轮转，保留30天，100MB触发
# - 低频日志：每周轮转，保留52周，10MB触发
#
# 配置说明：
# - 使用 'su admin tempmail' 解决权限问题
# - 使用 'maxsize' 而不是 'size' 避免覆盖时间轮转策略
# - 'maxsize' 与 daily/weekly 配合：达到大小阈值时立即轮转，否则按时间轮转

# 高频日志：邮件处理日志（生成频率最高）
/var/www/tempmail/logs/mail_handler.log {
    daily
    rotate 14
    maxsize 50M
    compress
    delaycompress
    missingok
    notifempty
    create 664 admin tempmail
    su admin tempmail
    dateext
    dateformat -%Y%m%d-%s

    postrotate
        # 记录轮转事件
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 高频日志轮转: mail_handler.log" >> /var/www/tempmail/logs/logrotate.log

        # 重新加载服务
        if systemctl is-active --quiet tempmail; then
            systemctl reload tempmail
        fi
        if systemctl is-active --quiet tempmail-optimized; then
            systemctl reload tempmail-optimized
        fi
    endscript
}

# 中频日志：应用程序和Web服务器日志
/var/www/tempmail/logs/app.log /var/www/tempmail/logs/gunicorn_*.log {
    daily
    rotate 30
    maxsize 100M
    compress
    delaycompress
    missingok
    notifempty
    create 664 admin tempmail
    su admin tempmail
    dateext
    dateformat -%Y%m%d-%s

    postrotate
        # 记录轮转事件
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 中频日志轮转: app.log, gunicorn_*.log" >> /var/www/tempmail/logs/logrotate.log

        # 重新加载服务
        if systemctl is-active --quiet tempmail; then
            systemctl reload tempmail
        fi
        if systemctl is-active --quiet tempmail-optimized; then
            systemctl reload tempmail-optimized
        fi

        # 重新加载Nginx（如果使用）
        if systemctl is-active --quiet nginx; then
            systemctl reload nginx
        fi
    endscript
}

# 低频日志：监控和健康检查日志
/var/www/tempmail/logs/monitoring.log /var/www/tempmail/logs/health-check.log {
    weekly
    rotate 52
    maxsize 10M
    compress
    delaycompress
    missingok
    notifempty
    create 664 admin tempmail
    su admin tempmail
    dateext
    dateformat -%Y%m%d-%s

    postrotate
        # 记录轮转事件
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 低频日志轮转: monitoring.log, health-check.log" >> /var/www/tempmail/logs/logrotate.log
    endscript
}

# 临时日志：每日清理
/var/www/tempmail/temp_logs/*.log {
    daily
    rotate 3
    compress
    delaycompress
    missingok
    notifempty
    create 664 admin tempmail
    su admin tempmail
    dateext
    dateformat -%Y%m%d-%s

    postrotate
        # 记录轮转事件
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 临时日志轮转: temp_logs/*.log" >> /var/www/tempmail/logs/logrotate.log
    endscript
}
