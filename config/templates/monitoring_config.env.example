# 临时邮箱系统监控配置示例
# 复制此文件为 .env 或在现有 .env 文件中添加这些配置

# ==================== 基础监控配置 ====================

# 是否启用监控 (true/false)
MONITORING_ENABLED=true

# 监控级别 (basic/standard/detailed)
# basic: 基础系统指标，最低资源占用
# standard: 标准指标 + 详细应用指标
# detailed: 全面指标 + 网络IO + 磁盘IO
MONITORING_LEVEL=basic

# ==================== 采集间隔配置 ====================

# 指标采集间隔（秒）
MONITORING_METRICS_INTERVAL=60

# 健康检查间隔（秒）
MONITORING_HEALTH_INTERVAL=300

# ==================== 资源限制配置 ====================

# 监控系统最大内存使用（MB）
MONITORING_MAX_MEMORY_MB=20

# 监控日志文件最大大小（MB）
MONITORING_MAX_LOG_SIZE_MB=50

# ==================== 告警阈值配置 ====================

# CPU使用率告警阈值（百分比）
MONITORING_CPU_THRESHOLD=80.0

# 内存使用率告警阈值（百分比）
MONITORING_MEMORY_THRESHOLD=85.0

# 磁盘使用率告警阈值（百分比）
MONITORING_DISK_THRESHOLD=90.0

# 响应时间告警阈值（秒）
MONITORING_RESPONSE_TIME_THRESHOLD=5.0

# 错误率告警阈值（百分比）
MONITORING_ERROR_RATE_THRESHOLD=10.0

# ==================== 通知配置 ====================

# 是否启用邮件通知 (true/false)
MONITORING_EMAIL_ENABLED=true

# 是否启用Webhook通知 (true/false)
MONITORING_WEBHOOK_ENABLED=false

# ==================== 邮件通知配置 ====================

# 是否启用邮件通知 (true/false)
MONITORING_EMAIL_ENABLED=true

# 告警邮件收件人（必须配置）
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# ==================== 内部邮件系统配置 ====================

# 管理员邮箱前缀（将创建 <EMAIL>）
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin

# sendmail程序路径（通常无需修改，但在不同Linux发行版中可能有所不同）
# 生产环境中应使用sendmail的实际绝对路径
# 部署到新服务器时，请使用 'which sendmail' 命令验证实际安装位置
MONITORING_SENDMAIL_PATH=/usr/sbin/sendmail

# 邮件发送重试次数
MONITORING_MAIL_RETRY_ATTEMPTS=3

# ==================== 数据保留配置 ====================

# 指标数据保留时间（小时）
MONITORING_METRICS_RETENTION_HOURS=24

# 日志文件保留时间（天）
MONITORING_LOG_RETENTION_DAYS=7

# 临时邮箱系统监控配置
# ==================== 生产环境推荐配置 ====================
# 生产环境建议配置（使用内部邮件）：
# MONITORING_LEVEL=basic
# MONITORING_METRICS_INTERVAL=60
# MONITORING_HEALTH_INTERVAL=300
# MONITORING_MAX_MEMORY_MB=20
# MONITORING_CPU_THRESHOLD=80.0
# MONITORING_MEMORY_THRESHOLD=85.0
# MONITORING_EMAIL_ENABLED=true
# MONITORING_USE_INTERNAL_MAIL=true
# MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
# MONITORING_ALERT_EMAIL_TO=<EMAIL>

# ==================== 开发环境推荐配置 ====================
# 开发环境建议配置：
# MONITORING_LEVEL=detailed
# MONITORING_METRICS_INTERVAL=30
# MONITORING_HEALTH_INTERVAL=60
# MONITORING_MAX_MEMORY_MB=50
# MONITORING_CPU_THRESHOLD=90.0
# MONITORING_MEMORY_THRESHOLD=95.0
# MONITORING_EMAIL_ENABLED=false
