# 临时邮箱系统日志轮转Cron任务配置
# 安装方法: sudo crontab -e 然后添加以下内容

# 每日凌晨2点执行日志轮转
0 2 * * * /usr/sbin/logrotate /etc/logrotate.d/tempmail >/dev/null 2>&1

# 每小时检查日志状态（工作时间）
0 9-18 * * 1-5 /var/www/tempmail/scripts/maintenance/log_rotation_monitor.py --no-alerts >/dev/null 2>&1

# 每4小时进行完整监控检查（包括告警）
0 */4 * * * /var/www/tempmail/scripts/maintenance/log_rotation_monitor.py >/dev/null 2>&1

# 每周日凌晨3点清理超期日志
0 3 * * 0 /var/www/tempmail/scripts/maintenance/logrotate_manager.sh cleanup >/dev/null 2>&1

# 每月1号凌晨4点生成月度报告
0 4 1 * * /var/www/tempmail/scripts/maintenance/log_rotation_monitor.py --report-only > /var/www/tempmail/logs/monthly_log_report_$(date +\%Y\%m).json 2>&1
