[Unit]
Description=Temporary Email System - Minimal Gunicorn WSGI Server
After=network.target

[Service]
Type=simple
User=admin
Group=tempmail
WorkingDirectory=/var/www/tempmail
Environment=PATH=/var/www/tempmail/venv/bin:/usr/local/bin:/usr/bin:/bin

# 使用配置文件启动命令
ExecStart=/var/www/tempmail/venv/bin/gunicorn -c gunicorn.conf.py app:app

# 重启策略
Restart=always
RestartSec=3

# 日志
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
