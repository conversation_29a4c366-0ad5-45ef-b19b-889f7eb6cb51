{"thresholds": {"high_frequency_logs": {"size_warning": "40M", "size_critical": "80M", "files": ["mail_handler.log"]}, "medium_frequency_logs": {"size_warning": "80M", "size_critical": "150M", "files": ["app.log", "gunicorn_access.log", "gunicorn_error.log"]}, "low_frequency_logs": {"size_warning": "8M", "size_critical": "15M", "files": ["monitoring.log", "health-check.log"]}}, "rotation_check": {"max_days_without_rotation": 7, "check_compressed_files": true}, "disk_usage": {"warning_threshold": "80%", "critical_threshold": "90%"}, "alerts": {"email_enabled": true, "email_recipients": ["<EMAIL>"], "cooldown_minutes": 60}}