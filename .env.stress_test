# 压力测试环境配置文件
# 此配置专门用于压力测试，禁用所有速率限制和优化性能

# Flask App Configuration
FLASK_APP=app.py
FLASK_ENV=development
FLASK_DEBUG=0
SECRET_KEY=stress_test_secret_key_for_development_only_not_for_production

# 压力测试模式标志
STRESS_TESTING=true
DISABLE_RATE_LIMIT=true
TESTING=true

# Database Configuration - 使用内存数据库以获得最佳性能
DATABASE_PATH=:memory:

# Email Configuration
DOMAIN_NAME=stress-test.local
EMAIL_EXPIRATION_HOURS=24

# API Configuration - 移除所有限制
API_TIMEOUT=60000
API_RETRY_ATTEMPTS=10
API_RETRY_DELAY=100

# Logging Configuration - 减少日志输出以提高性能
LOG_LEVEL=ERROR
LOG_FILE_APP=
LOG_FILE_MAIL_HANDLER=
LOG_FILE_CLEANUP=

# 性能优化配置
# 数据库连接超时（毫秒）
DB_TIMEOUT=60000
# 数据库缓存大小（KB，负数表示页数）
DB_CACHE_SIZE=-8000
# 数据库忙等待超时（毫秒）
DB_BUSY_TIMEOUT=30000

# 压力测试专用配置
MAX_CONCURRENT_CONNECTIONS=1000
MAX_MAILBOXES_PER_SESSION=10000
MAX_EMAILS_PER_MAILBOX=1000

# 禁用不必要的功能以提高性能
ENABLE_EMAIL_HISTORY=false
ENABLE_CLEANUP_SCHEDULER=false
ENABLE_DETAILED_LOGGING=false
