# MySQL数据库配置示例文件
# 复制此文件为 .env 并根据实际环境修改配置

# ==================== Flask应用配置 ====================
FLASK_APP=app.py
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your_very_secure_secret_key_at_least_32_characters_long

# ==================== 数据库配置 ====================
# 数据库类型设置为MySQL
DATABASE_TYPE=mysql

# MySQL服务器配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=tempmail
MYSQL_USERNAME=tempmail_user
MYSQL_PASSWORD=your_secure_mysql_password_here

# MySQL字符集配置
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_unicode_ci

# ==================== MySQL连接池配置 ====================
# 连接池大小（建议根据服务器性能调整）
MYSQL_POOL_SIZE=10
MYSQL_MAX_OVERFLOW=20
MYSQL_POOL_TIMEOUT=30
MYSQL_POOL_RECYCLE=3600

# ==================== MySQL SSL配置 ====================
# 是否禁用SSL连接
MYSQL_SSL_DISABLED=false

# SSL证书路径（如果使用SSL连接）
MYSQL_SSL_CA=/path/to/ca-cert.pem
MYSQL_SSL_CERT=/path/to/client-cert.pem
MYSQL_SSL_KEY=/path/to/client-key.pem

# ==================== MySQL调试配置 ====================
# 是否在日志中显示SQL语句（开发环境可设为true）
MYSQL_ECHO_SQL=false

# ==================== 应用配置 ====================
DOMAIN_NAME=yourdomain.com
EMAIL_EXPIRATION_HOURS=24

# ==================== 日志配置 ====================
LOG_FILE_APP=/var/www/tempmail/logs/app.log
LOG_FILE_MAIL_HANDLER=/var/www/tempmail/logs/mail_handler.log
LOG_FILE_CLEANUP=/var/www/tempmail/logs/cleanup.log
FLASK_LOG_LEVEL=INFO

# ==================== API配置 ====================
API_BASE_URL=
API_TIMEOUT=10000
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000

# ==================== 自动刷新配置 ====================
AUTO_REFRESH_ENABLED=true
AUTO_REFRESH_INTERVAL=3000

# ==================== 监控配置 ====================
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
MONITORING_METRICS_INTERVAL=60
MONITORING_HEALTH_INTERVAL=300
MONITORING_MAX_MEMORY_MB=20
MONITORING_CPU_THRESHOLD=80.0
MONITORING_MEMORY_THRESHOLD=85.0
MONITORING_EMAIL_ENABLED=true
MONITORING_USE_INTERNAL_MAIL=true
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# ==================== 缓存配置 ====================
# 缓存类型: simple, redis, filesystem
FLASK_CACHE_TYPE=simple
FLASK_CACHE_DEFAULT_TIMEOUT=300
FLASK_CACHE_THRESHOLD=500

# Redis缓存配置（如果使用Redis）
CACHE_REDIS_URL=redis://localhost:6379/0

# 文件系统缓存配置（如果使用filesystem）
CACHE_DIR=/tmp/flask_cache

# ==================== 生产环境优化配置 ====================
# 禁用调试模式
FLASK_DEBUG=0
FLASK_ENV=production

# 安全配置
SECRET_KEY=your_production_secret_key_here_32_chars_minimum

# 性能配置
GUNICORN_WORKERS=4
GUNICORN_THREADS=2
GUNICORN_MAX_REQUESTS=1000
GUNICORN_MAX_REQUESTS_JITTER=100

# ==================== 开发环境配置示例 ====================
# 开发环境可以使用以下配置：
# FLASK_ENV=development
# FLASK_DEBUG=1
# MYSQL_ECHO_SQL=true
# MONITORING_LEVEL=detailed
# LOG_LEVEL=DEBUG

# ==================== 测试环境配置示例 ====================
# 测试环境建议使用独立的数据库：
# MYSQL_DATABASE=tempmail_test
# MYSQL_USERNAME=tempmail_test_user
# MYSQL_PASSWORD=test_password
# MONITORING_ENABLED=false
