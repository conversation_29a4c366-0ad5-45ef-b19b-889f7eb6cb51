#!/bin/bash

# systemd服务启动失败诊断脚本
# 无需sudo权限的基础诊断

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 systemd服务启动失败诊断${NC}"
echo "=================================================="

# 1. 检查服务文件是否存在
echo ""
echo -e "${BLUE}1. 检查服务文件${NC}"
SERVICE_FILE="/var/www/tempmail/tempmail-optimized.service"
if [ -f "$SERVICE_FILE" ]; then
    echo -e "   ✅ 本地服务文件存在: $SERVICE_FILE"
    echo -e "   文件大小: $(du -h $SERVICE_FILE | cut -f1)"
    echo -e "   文件权限: $(ls -l $SERVICE_FILE | awk '{print $1, $3, $4}')"
else
    echo -e "   ❌ 本地服务文件不存在: $SERVICE_FILE"
fi

# 检查系统服务文件
SYSTEM_SERVICE="/etc/systemd/system/tempmail-optimized.service"
echo -e "   系统服务文件: $SYSTEM_SERVICE"
if [ -f "$SYSTEM_SERVICE" ]; then
    echo -e "   ✅ 系统服务文件存在"
else
    echo -e "   ❌ 系统服务文件不存在（需要先安装）"
fi

# 2. 检查配置文件语法
echo ""
echo -e "${BLUE}2. 检查配置文件内容${NC}"
if [ -f "$SERVICE_FILE" ]; then
    echo "   服务文件内容预览:"
    echo "   ===================="
    head -20 "$SERVICE_FILE" | sed 's/^/   /'
    echo "   ===================="
    
    # 检查关键配置项
    echo ""
    echo "   关键配置检查:"
    
    # 检查ExecStart路径
    exec_start=$(grep "^ExecStart=" "$SERVICE_FILE" | head -1)
    if [ -n "$exec_start" ]; then
        echo -e "   ExecStart: ${exec_start#ExecStart=}"
        
        # 提取gunicorn路径
        gunicorn_path=$(echo "$exec_start" | grep -o '/[^[:space:]]*/gunicorn' | head -1)
        if [ -n "$gunicorn_path" ] && [ -f "$gunicorn_path" ]; then
            echo -e "   ✅ Gunicorn可执行文件存在: $gunicorn_path"
        elif [ -n "$gunicorn_path" ]; then
            echo -e "   ❌ Gunicorn可执行文件不存在: $gunicorn_path"
        fi
        
        # 提取配置文件路径
        config_path=$(echo "$exec_start" | grep -o '\--config [^[:space:]]*' | cut -d' ' -f2)
        if [ -n "$config_path" ] && [ -f "$config_path" ]; then
            echo -e "   ✅ Gunicorn配置文件存在: $config_path"
        elif [ -n "$config_path" ]; then
            echo -e "   ❌ Gunicorn配置文件不存在: $config_path"
        fi
    fi
    
    # 检查用户和组
    user=$(grep "^User=" "$SERVICE_FILE" | cut -d'=' -f2)
    group=$(grep "^Group=" "$SERVICE_FILE" | cut -d'=' -f2)
    echo -e "   配置用户: $user"
    echo -e "   配置组: $group"
    
    # 检查工作目录
    workdir=$(grep "^WorkingDirectory=" "$SERVICE_FILE" | cut -d'=' -f2)
    if [ -n "$workdir" ] && [ -d "$workdir" ]; then
        echo -e "   ✅ 工作目录存在: $workdir"
    elif [ -n "$workdir" ]; then
        echo -e "   ❌ 工作目录不存在: $workdir"
    fi
fi

# 3. 检查依赖文件
echo ""
echo -e "${BLUE}3. 检查依赖文件${NC}"

# 检查虚拟环境
VENV_PATH="/var/www/tempmail/venv"
if [ -d "$VENV_PATH" ]; then
    echo -e "   ✅ 虚拟环境目录存在: $VENV_PATH"
    
    # 检查gunicorn
    GUNICORN_BIN="$VENV_PATH/bin/gunicorn"
    if [ -f "$GUNICORN_BIN" ]; then
        echo -e "   ✅ Gunicorn可执行文件存在: $GUNICORN_BIN"
        echo -e "   版本: $($GUNICORN_BIN --version 2>/dev/null || echo '无法获取版本')"
    else
        echo -e "   ❌ Gunicorn可执行文件不存在: $GUNICORN_BIN"
    fi
    
    # 检查Python
    PYTHON_BIN="$VENV_PATH/bin/python"
    if [ -f "$PYTHON_BIN" ]; then
        echo -e "   ✅ Python可执行文件存在: $PYTHON_BIN"
        echo -e "   版本: $($PYTHON_BIN --version 2>/dev/null || echo '无法获取版本')"
    else
        echo -e "   ❌ Python可执行文件不存在: $PYTHON_BIN"
    fi
else
    echo -e "   ❌ 虚拟环境目录不存在: $VENV_PATH"
fi

# 检查应用文件
APP_FILE="/var/www/tempmail/app.py"
if [ -f "$APP_FILE" ]; then
    echo -e "   ✅ 应用文件存在: $APP_FILE"
else
    echo -e "   ❌ 应用文件不存在: $APP_FILE"
fi

# 检查Gunicorn配置文件
GUNICORN_CONF="/var/www/tempmail/gunicorn.conf.py"
if [ -f "$GUNICORN_CONF" ]; then
    echo -e "   ✅ Gunicorn配置文件存在: $GUNICORN_CONF"
else
    echo -e "   ❌ Gunicorn配置文件不存在: $GUNICORN_CONF"
fi

# 4. 检查权限
echo ""
echo -e "${BLUE}4. 检查文件权限${NC}"

# 检查关键目录权限
dirs=("/var/www/tempmail" "/var/www/tempmail/logs" "/var/www/tempmail/database")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        perms=$(ls -ld "$dir" | awk '{print $1, $3, $4}')
        echo -e "   目录 $dir: $perms"
        
        # 检查写权限
        if [ -w "$dir" ]; then
            echo -e "     ✅ 当前用户可写"
        else
            echo -e "     ❌ 当前用户不可写"
        fi
    else
        echo -e "   ❌ 目录不存在: $dir"
    fi
done

# 5. 检查端口占用
echo ""
echo -e "${BLUE}5. 检查端口占用${NC}"
if command -v netstat >/dev/null 2>&1; then
    port_8080=$(netstat -tlnp 2>/dev/null | grep ":8080")
    if [ -n "$port_8080" ]; then
        echo -e "   ⚠️ 端口8080已被占用:"
        echo "$port_8080" | sed 's/^/     /'
    else
        echo -e "   ✅ 端口8080未被占用"
    fi
else
    echo -e "   ⚠️ netstat命令不可用，无法检查端口"
fi

# 6. 检查环境变量文件
echo ""
echo -e "${BLUE}6. 检查环境变量文件${NC}"
ENV_FILE="/var/www/tempmail/.env"
if [ -f "$ENV_FILE" ]; then
    echo -e "   ✅ 环境变量文件存在: $ENV_FILE"
    echo -e "   文件权限: $(ls -l $ENV_FILE | awk '{print $1, $3, $4}')"
    
    # 检查关键配置
    if grep -q "FLASK_APP" "$ENV_FILE"; then
        echo -e "   ✅ 包含FLASK_APP配置"
    else
        echo -e "   ⚠️ 缺少FLASK_APP配置"
    fi
else
    echo -e "   ⚠️ 环境变量文件不存在: $ENV_FILE"
fi

# 7. 测试手动启动
echo ""
echo -e "${BLUE}7. 测试手动启动命令${NC}"
if [ -f "/var/www/tempmail/venv/bin/gunicorn" ] && [ -f "/var/www/tempmail/gunicorn.conf.py" ]; then
    echo "   测试命令构建:"
    echo "   cd /var/www/tempmail"
    echo "   source venv/bin/activate"
    echo "   gunicorn --config gunicorn.conf.py --bind 0.0.0.0:5001 app:app"
    echo ""
    echo -e "   ${YELLOW}建议手动测试上述命令以确认应用可以启动${NC}"
else
    echo -e "   ❌ 缺少必要文件，无法构建测试命令"
fi

# 8. 提供修复建议
echo ""
echo -e "${BLUE}8. 修复建议${NC}"
echo "   基于以上检查，可能的问题和解决方案:"
echo ""

# 检查是否安装了服务
if [ ! -f "/etc/systemd/system/tempmail-optimized.service" ]; then
    echo -e "   ${YELLOW}问题1: 服务未安装到系统${NC}"
    echo "   解决方案:"
    echo "     sudo cp /var/www/tempmail/tempmail-optimized.service /etc/systemd/system/"
    echo "     sudo systemctl daemon-reload"
    echo ""
fi

# 检查gunicorn是否存在
if [ ! -f "/var/www/tempmail/venv/bin/gunicorn" ]; then
    echo -e "   ${YELLOW}问题2: Gunicorn未安装${NC}"
    echo "   解决方案:"
    echo "     cd /var/www/tempmail"
    echo "     source venv/bin/activate"
    echo "     pip install gunicorn"
    echo ""
fi

# 检查配置文件
if [ ! -f "/var/www/tempmail/gunicorn.conf.py" ]; then
    echo -e "   ${YELLOW}问题3: Gunicorn配置文件缺失${NC}"
    echo "   解决方案: 确保gunicorn.conf.py文件存在"
    echo ""
fi

echo ""
echo -e "${BLUE}📋 下一步操作建议:${NC}"
echo "1. 手动测试应用启动"
echo "2. 修复发现的问题"
echo "3. 重新尝试启动服务"
echo "4. 查看systemd日志获取详细错误信息"

echo ""
echo "=================================================="
echo -e "${GREEN}诊断完成${NC}"
