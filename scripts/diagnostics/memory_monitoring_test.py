#!/usr/bin/env python3
"""
内存监控配置测试脚本
用于验证内存监控优化效果
"""

import os
import sys
import time
import psutil
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from monitoring.config import MonitoringConfig
from monitoring.metrics import MetricsCollector
from monitoring.alerts import AlertManager

class MemoryMonitoringTest:
    """内存监控测试类"""
    
    def __init__(self):
        self.config = MonitoringConfig.from_env()
        self.metrics_collector = MetricsCollector(self.config)
        
    def test_current_memory_usage(self):
        """测试当前内存使用情况"""
        print("=== 当前内存使用情况 ===")
        
        # 系统内存
        memory = psutil.virtual_memory()
        print(f"系统总内存: {memory.total / 1024 / 1024 / 1024:.1f}GB")
        print(f"系统已用内存: {memory.used / 1024 / 1024 / 1024:.1f}GB ({memory.percent:.1f}%)")
        print(f"系统可用内存: {memory.available / 1024 / 1024 / 1024:.1f}GB")
        
        # 当前进程内存
        current_process = psutil.Process()
        process_memory = current_process.memory_info()
        print(f"当前进程内存: {process_memory.rss / 1024 / 1024:.1f}MB")
        
        return {
            'system_memory_percent': memory.percent,
            'process_memory_mb': process_memory.rss / 1024 / 1024
        }
    
    def test_monitoring_config(self):
        """测试监控配置"""
        print("\n=== 监控配置检查 ===")
        
        print(f"监控启用状态: {self.config.enabled}")
        print(f"监控级别: {self.config.level}")
        print(f"内存基础限制: {self.config.max_memory_mb}MB")
        print(f"系统内存告警阈值: {self.config.memory_threshold}%")
        print(f"指标采集间隔: {self.config.metrics_interval}秒")
        print(f"邮件通知启用: {self.config.email_notifications}")
        
        # 计算实际告警阈值
        app_memory_threshold = max(self.config.max_memory_mb * 3, 100)
        print(f"应用内存告警阈值: {app_memory_threshold}MB")
        
        return {
            'max_memory_mb': self.config.max_memory_mb,
            'app_memory_threshold': app_memory_threshold,
            'system_memory_threshold': self.config.memory_threshold
        }
    
    def test_alert_logic(self, current_memory_mb):
        """测试告警逻辑"""
        print("\n=== 告警逻辑测试 ===")
        
        app_memory_threshold = max(self.config.max_memory_mb * 3, 100)
        
        print(f"当前应用内存使用: {current_memory_mb:.1f}MB")
        print(f"应用内存告警阈值: {app_memory_threshold}MB")
        
        if current_memory_mb > app_memory_threshold:
            print("❌ 当前内存使用超过告警阈值，会触发告警")
            alert_status = "ALERT"
        else:
            margin = app_memory_threshold - current_memory_mb
            print(f"✅ 当前内存使用正常，距离告警还有 {margin:.1f}MB")
            alert_status = "OK"
        
        return {
            'current_memory_mb': current_memory_mb,
            'threshold_mb': app_memory_threshold,
            'margin_mb': app_memory_threshold - current_memory_mb,
            'status': alert_status
        }
    
    def test_metrics_collection(self):
        """测试指标收集"""
        print("\n=== 指标收集测试 ===")
        
        try:
            metrics = self.metrics_collector.collect_all()
            
            if 'application' in metrics and 'process' in metrics['application']:
                app_memory = metrics['application']['process'].get('memory_mb', 0)
                print(f"✅ 成功收集应用内存指标: {app_memory:.1f}MB")
            else:
                print("❌ 无法收集应用内存指标")
                app_memory = 0
            
            if 'system' in metrics and 'memory' in metrics['system']:
                sys_memory = metrics['system']['memory'].get('percent', 0)
                print(f"✅ 成功收集系统内存指标: {sys_memory:.1f}%")
            else:
                print("❌ 无法收集系统内存指标")
                sys_memory = 0
            
            return {
                'app_memory_mb': app_memory,
                'sys_memory_percent': sys_memory,
                'collection_success': True
            }
            
        except Exception as e:
            print(f"❌ 指标收集失败: {e}")
            return {
                'app_memory_mb': 0,
                'sys_memory_percent': 0,
                'collection_success': False,
                'error': str(e)
            }
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*50)
        print("内存监控配置测试报告")
        print("="*50)
        
        # 收集所有测试数据
        memory_usage = self.test_current_memory_usage()
        config_info = self.test_monitoring_config()
        alert_test = self.test_alert_logic(memory_usage['process_memory_mb'])
        metrics_test = self.test_metrics_collection()
        
        # 生成综合评估
        print("\n=== 综合评估 ===")
        
        issues = []
        recommendations = []
        
        # 检查配置合理性
        if config_info['max_memory_mb'] < 30:
            issues.append("内存基础限制过低，可能导致频繁误报")
            recommendations.append(f"建议将 MONITORING_MAX_MEMORY_MB 调整为 50-80")
        
        if alert_test['status'] == "ALERT":
            issues.append("当前内存使用触发告警阈值")
            recommendations.append("检查应用是否存在内存泄漏或调整阈值")
        
        if not metrics_test['collection_success']:
            issues.append("指标收集失败")
            recommendations.append("检查监控服务配置和权限")
        
        # 输出评估结果
        if not issues:
            print("✅ 监控配置正常，无发现问题")
        else:
            print("⚠️  发现以下问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        
        if recommendations:
            print("\n💡 建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
        
        # 保存详细报告
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'memory_usage': memory_usage,
            'config_info': config_info,
            'alert_test': alert_test,
            'metrics_test': metrics_test,
            'issues': issues,
            'recommendations': recommendations
        }
        
        report_file = project_root / 'logs' / f'memory_monitoring_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return report_data

def main():
    """主函数"""
    print("内存监控配置测试工具")
    print("用于验证内存监控优化效果")
    print("-" * 50)
    
    try:
        tester = MemoryMonitoringTest()
        report = tester.generate_report()
        
        # 返回状态码
        if report['issues']:
            print(f"\n⚠️  测试完成，发现 {len(report['issues'])} 个问题")
            return 1
        else:
            print(f"\n✅ 测试完成，监控配置正常")
            return 0
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
