#!/bin/bash

# 权限诊断脚本
# 检查临时邮箱系统的文件和目录权限

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 项目路径
PROJECT_ROOT="/var/www/tempmail"

log_info "开始权限诊断..."
log_info "项目路径: $PROJECT_ROOT"
log_info "当前用户: $(whoami)"
log_info "当前用户组: $(groups)"

echo ""
log_info "=== 目录权限检查 ==="

# 检查主要目录
directories=(
    "$PROJECT_ROOT"
    "$PROJECT_ROOT/database"
    "$PROJECT_ROOT/logs"
    "$PROJECT_ROOT/static"
    "$PROJECT_ROOT/templates"
    "$PROJECT_ROOT/venv"
    "/tmp"
)

for dir in "${directories[@]}"; do
    if [ -d "$dir" ]; then
        perms=$(ls -ld "$dir" | awk '{print $1, $3, $4}')
        log_info "目录 $dir: $perms"
        
        # 检查写权限
        if [ -w "$dir" ]; then
            log_info "  ✓ 可写"
        else
            log_error "  ✗ 不可写"
        fi
    else
        log_warn "目录不存在: $dir"
    fi
done

echo ""
log_info "=== 文件权限检查 ==="

# 检查重要文件
files=(
    "$PROJECT_ROOT/app.py"
    "$PROJECT_ROOT/database/tempmail.db"
    "$PROJECT_ROOT/logs/app.log"
    "$PROJECT_ROOT/.env"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        perms=$(ls -l "$file" | awk '{print $1, $3, $4}')
        log_info "文件 $file: $perms"
        
        # 检查读写权限
        if [ -r "$file" ] && [ -w "$file" ]; then
            log_info "  ✓ 可读写"
        elif [ -r "$file" ]; then
            log_warn "  ⚠ 只读"
        else
            log_error "  ✗ 无法访问"
        fi
    else
        log_warn "文件不存在: $file"
    fi
done

echo ""
log_info "=== 磁盘空间检查 ==="
df -h "$PROJECT_ROOT"

echo ""
log_info "=== SELinux状态检查 ==="
if command -v getenforce >/dev/null 2>&1; then
    selinux_status=$(getenforce 2>/dev/null || echo "未安装")
    log_info "SELinux状态: $selinux_status"
    
    if [ "$selinux_status" = "Enforcing" ]; then
        log_warn "SELinux处于强制模式，可能影响文件访问"
        log_info "检查SELinux上下文:"
        ls -Z "$PROJECT_ROOT" 2>/dev/null || log_warn "无法获取SELinux上下文"
    fi
else
    log_info "SELinux: 未安装"
fi

echo ""
log_info "=== 进程权限检查 ==="
log_info "当前进程的有效用户ID: $(id -u)"
log_info "当前进程的有效组ID: $(id -g)"
log_info "当前进程的所有组: $(id -G)"

echo ""
log_info "=== 文件系统类型检查 ==="
filesystem_type=$(df -T "$PROJECT_ROOT" | tail -1 | awk '{print $2}')
log_info "文件系统类型: $filesystem_type"

echo ""
log_info "=== 权限诊断完成 ==="
