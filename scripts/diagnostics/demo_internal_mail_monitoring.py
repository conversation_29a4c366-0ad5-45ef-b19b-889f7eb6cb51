#!/usr/bin/env python3
"""
内部邮件监控系统演示脚本

展示如何使用临时邮箱系统的内部邮件功能发送监控告警
"""

import os
import time
import json
from pathlib import Path

# 设置演示环境变量
os.environ['MONITORING_ENABLED'] = 'true'
os.environ['MONITORING_LEVEL'] = 'basic'
os.environ['MONITORING_USE_INTERNAL_MAIL'] = 'true'
os.environ['MONITORING_EMAIL_ENABLED'] = 'true'
os.environ['MONITORING_WEBHOOK_ENABLED'] = 'false'
os.environ['MONITORING_ADMIN_EMAIL_PREFIX'] = 'demo-monitoring'
os.environ['DOMAIN_NAME'] = 'kuroneko.lol'
os.environ['MONITORING_ALERT_EMAIL_TO'] = '<EMAIL>'
os.environ['MONITORING_METRICS_INTERVAL'] = '15'  # 15秒间隔用于演示

from monitoring.config import MonitoringConfig
from monitoring.lightweight_monitor import LightweightMonitor


def demo_internal_mail_monitoring():
    """演示内部邮件监控系统"""
    print("🚀 内部邮件监控系统演示")
    print("=" * 60)
    
    # 1. 显示配置信息
    print("\n📋 1. 监控配置信息")
    config = MonitoringConfig.from_env()
    
    print(f"  监控级别: {config.level}")
    print(f"  使用内部邮件: {config.use_internal_mail}")
    print(f"  管理员邮箱前缀: {config.internal_admin_email_prefix}")
    print(f"  域名: {config.domain_name}")
    print(f"  收件人邮箱: {config.alert_email_to}")
    print(f"  指标采集间隔: {config.metrics_interval}秒")
    
    # 2. 初始化监控器
    print("\n⚙️ 2. 初始化监控器")
    monitor = LightweightMonitor(config)
    
    # 检查内部邮件发送器
    if monitor.alert_manager.internal_mail_sender:
        mail_sender = monitor.alert_manager.internal_mail_sender
        print(f"  ✅ 内部邮件发送器已初始化")
        print(f"    管理员邮箱: {mail_sender.admin_email}")
        print(f"    sendmail路径: {mail_sender.sendmail_path}")
        
        # 获取管理员邮箱信息
        admin_info = mail_sender.get_admin_email_info()
        if admin_info.get('exists'):
            print(f"    邮箱ID: {admin_info.get('id')}")
            print(f"    创建时间: {admin_info.get('created_at')}")
        else:
            print(f"    ❌ 管理员邮箱不存在")
    else:
        print(f"  ❌ 内部邮件发送器未初始化")
        return
    
    try:
        # 3. 启动监控
        print("\n▶️ 3. 启动监控系统")
        monitor.start()
        print("  监控器已启动，开始收集指标...")
        
        # 4. 发送测试告警
        print("\n🧪 4. 发送测试告警")
        test_alerts = [
            {
                'id': 'demo_test_warning',
                'severity': 'warning',
                'message': '这是一个警告级别的测试告警',
                'details': {
                    'test_type': '演示测试',
                    'alert_level': '警告',
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
            },
            {
                'id': 'demo_test_critical',
                'severity': 'critical',
                'message': '这是一个严重级别的测试告警',
                'details': {
                    'test_type': '演示测试',
                    'alert_level': '严重',
                    'system_status': '需要立即关注',
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
        ]
        
        for i, alert in enumerate(test_alerts, 1):
            print(f"  发送测试告警 {i}: {alert['severity']} - {alert['message']}")
            monitor.alert_manager.send_alert(
                alert['id'],
                alert['message'],
                alert['severity'],
                alert['details']
            )
            time.sleep(2)  # 间隔2秒
        
        # 5. 等待指标收集
        print("\n📊 5. 监控指标收集")
        print("  等待指标收集...")
        time.sleep(20)  # 等待至少一次指标收集
        
        # 获取当前指标
        metrics = monitor.get_current_metrics()
        if 'error' not in metrics:
            print("  ✅ 指标收集正常")
            if 'system' in metrics:
                cpu = metrics['system'].get('cpu_percent', 0)
                memory = metrics['system'].get('memory', {}).get('percent', 0)
                print(f"    当前CPU使用率: {cpu:.1f}%")
                print(f"    当前内存使用率: {memory:.1f}%")
        
        # 6. 模拟高CPU使用率告警
        print("\n🚨 6. 模拟系统告警")
        
        # 手动触发高CPU告警
        fake_high_cpu_metrics = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'system': {
                'cpu_percent': 95.5,  # 超过阈值的CPU使用率
                'memory': {'percent': 78.2},
                'disk': {'percent': 45.1}
            },
            'application': {
                'process': {
                    'memory_mb': 25.8,
                    'cpu_percent': 2.1
                }
            },
            'database': {
                'connection_test': True,
                'file_size_mb': 0.1
            }
        }
        
        print("  模拟高CPU使用率场景...")
        monitor.alert_manager.check_alerts(fake_high_cpu_metrics)
        
        # 等待告警处理
        time.sleep(3)
        
        # 7. 检查告警状态
        print("\n📋 7. 告警状态检查")
        active_alerts = monitor.alert_manager.get_active_alerts()
        print(f"  当前活跃告警数量: {len(active_alerts)}")
        
        for alert in active_alerts:
            print(f"    - {alert['id']}: {alert['severity']} - {alert['message']}")
        
        # 8. 邮件发送统计
        print("\n📧 8. 邮件发送统计")
        if mail_sender:
            admin_info = mail_sender.get_admin_email_info()
            print(f"  管理员邮箱: {mail_sender.admin_email}")
            print(f"  邮箱状态: {'正常' if admin_info.get('exists') else '异常'}")
            
            # 测试邮件发送功能
            print("  执行邮件发送测试...")
            success, message = mail_sender.test_mail_sending(config.alert_email_to)
            print(f"  测试结果: {'✅ 成功' if success else '❌ 失败'} - {message}")
        
        # 9. 监控性能统计
        print("\n📈 9. 监控性能统计")
        status = monitor.get_status()
        print(f"  运行时间: {status['uptime_seconds']:.1f}秒")
        print(f"  后台线程数: {status['threads_count']}")
        print(f"  最近指标收集耗时: {status['last_metrics_time']:.3f}秒")
        print(f"  最近健康检查耗时: {status['last_health_check_time']:.3f}秒")
        
        # 10. 保存演示结果
        print("\n💾 10. 保存演示结果")
        demo_results = {
            'config': {
                'use_internal_mail': config.use_internal_mail,
                'admin_email_prefix': config.internal_admin_email_prefix,
                'domain_name': config.domain_name,
                'alert_email_to': config.alert_email_to
            },
            'mail_sender': {
                'admin_email': mail_sender.admin_email if mail_sender else None,
                'admin_email_exists': admin_info.get('exists', False) if mail_sender else False
            },
            'monitoring': {
                'status': status,
                'active_alerts_count': len(active_alerts),
                'latest_metrics': metrics
            },
            'test_results': {
                'mail_test_success': success if mail_sender else False,
                'mail_test_message': message if mail_sender else 'N/A'
            }
        }
        
        # 保存到文件
        results_file = Path('internal_mail_demo_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(demo_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"  演示结果已保存到: {results_file}")
        
    finally:
        # 停止监控
        print("\n⏹️ 停止监控器")
        monitor.stop()
        print("  监控器已停止")
    
    print("\n🎉 内部邮件监控系统演示完成！")
    
    # 总结
    print("\n📝 演示总结:")
    print("  ✅ 内部邮件系统配置正确")
    print("  ✅ 管理员邮箱自动创建成功")
    print("  ✅ 监控告警功能正常")
    print("  ✅ 邮件发送功能可用")
    print("  ✅ 系统性能良好")
    
    print("\n🔍 检查要点:")
    print("  1. 查看监控日志: tail -f logs/monitoring.log")
    print("  2. 检查管理员邮箱: 在数据库中查看 temporary_emails 表")
    print("  3. 验证邮件发送: 检查收件箱是否收到告警邮件")
    print("  4. 监控API测试: curl http://localhost:5000/api/monitoring/status")
    
    print(f"\n💡 内部邮件系统优势:")
    print(f"  🔒 完全自给自足，无需外部SMTP服务")
    print(f"  🚀 利用现有Postfix基础设施")
    print(f"  🛡️ 更好的安全性和隐私保护")
    print(f"  ⚡ 更快的邮件发送速度")
    print(f"  💰 零额外成本")


if __name__ == '__main__':
    try:
        demo_internal_mail_monitoring()
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
