#!/bin/bash

# 日志轮转验证脚本
# 功能：验证日志轮转系统是否正常工作

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_DIR="$PROJECT_ROOT/logs"
TEMP_LOG_DIR="$PROJECT_ROOT/temp_logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 计数器
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试结果记录
TEST_RESULTS=()

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
    TEST_RESULTS+=("PASS: $1")
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
    TEST_RESULTS+=("FAIL: $1")
}

# 开始测试
start_test() {
    ((TESTS_TOTAL++))
    log_info "测试 $TESTS_TOTAL: $1"
}

# 检查文件存在性
test_file_exists() {
    start_test "检查配置文件存在性"
    
    local files=(
        "/etc/logrotate.d/tempmail"
        "$PROJECT_ROOT/config/tempmail-logrotate.conf"
        "$PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh"
        "$PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py"
    )
    
    local all_exist=true
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            echo "  ✓ $file"
        else
            echo "  ✗ $file (不存在)"
            all_exist=false
        fi
    done
    
    if $all_exist; then
        log_success "所有必需文件存在"
    else
        log_error "部分必需文件缺失"
    fi
}

# 检查目录权限
test_directory_permissions() {
    start_test "检查目录权限"
    
    local dirs=("$LOG_DIR" "$TEMP_LOG_DIR")
    local all_correct=true
    
    for dir in "${dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            local owner=$(stat -c "%U:%G" "$dir")
            local perms=$(stat -c "%a" "$dir")
            
            if [[ "$owner" == "admin:tempmail" ]] && [[ "$perms" == "755" ]]; then
                echo "  ✓ $dir ($owner, $perms)"
            else
                echo "  ✗ $dir ($owner, $perms) - 应该是 admin:tempmail, 755"
                all_correct=false
            fi
        else
            echo "  ✗ $dir (目录不存在)"
            all_correct=false
        fi
    done
    
    if $all_correct; then
        log_success "目录权限正确"
    else
        log_error "目录权限不正确"
    fi
}

# 检查logrotate配置语法
test_logrotate_syntax() {
    start_test "检查logrotate配置语法"
    
    if [[ ! -f "/etc/logrotate.d/tempmail" ]]; then
        log_error "logrotate配置文件不存在"
        return
    fi
    
    if logrotate -d /etc/logrotate.d/tempmail > /tmp/logrotate_syntax_test.log 2>&1; then
        log_success "logrotate配置语法正确"
    else
        log_error "logrotate配置语法错误"
        echo "错误详情："
        cat /tmp/logrotate_syntax_test.log | head -10
    fi
    
    rm -f /tmp/logrotate_syntax_test.log
}

# 检查cron任务
test_cron_jobs() {
    start_test "检查cron任务"
    
    local cron_content=$(crontab -l 2>/dev/null || echo "")
    local required_patterns=(
        "logrotate.*tempmail"
        "log_rotation_monitor"
        "logrotate_manager.*cleanup"
    )
    
    local all_found=true
    for pattern in "${required_patterns[@]}"; do
        if echo "$cron_content" | grep -q "$pattern"; then
            echo "  ✓ 找到模式: $pattern"
        else
            echo "  ✗ 未找到模式: $pattern"
            all_found=false
        fi
    done
    
    if $all_found; then
        log_success "所有必需的cron任务存在"
    else
        log_error "部分cron任务缺失"
    fi
}

# 检查脚本权限
test_script_permissions() {
    start_test "检查脚本执行权限"
    
    local scripts=(
        "$PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh"
        "$PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py"
        "$PROJECT_ROOT/scripts/deployment/setup_log_rotation.sh"
    )
    
    local all_executable=true
    for script in "${scripts[@]}"; do
        if [[ -f "$script" ]] && [[ -x "$script" ]]; then
            echo "  ✓ $script"
        else
            echo "  ✗ $script (不可执行或不存在)"
            all_executable=false
        fi
    done
    
    if $all_executable; then
        log_success "所有脚本具有执行权限"
    else
        log_error "部分脚本缺少执行权限"
    fi
}

# 测试监控脚本
test_monitoring_script() {
    start_test "测试监控脚本功能"
    
    local monitor_script="$PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py"
    
    if [[ ! -f "$monitor_script" ]]; then
        log_error "监控脚本不存在"
        return
    fi
    
    if python3 "$monitor_script" --no-alerts > /tmp/monitor_test.log 2>&1; then
        log_success "监控脚本运行正常"
    else
        log_error "监控脚本运行失败"
        echo "错误详情："
        cat /tmp/monitor_test.log | head -10
    fi
    
    rm -f /tmp/monitor_test.log
}

# 检查日志文件状态
test_log_files_status() {
    start_test "检查日志文件状态"
    
    local log_files=(
        "$LOG_DIR/app.log"
        "$LOG_DIR/mail_handler.log"
        "$LOG_DIR/monitoring.log"
        "$LOG_DIR/logrotate.log"
    )
    
    local files_ok=true
    for log_file in "${log_files[@]}"; do
        if [[ -f "$log_file" ]]; then
            local size=$(stat -c%s "$log_file")
            local owner=$(stat -c "%U:%G" "$log_file")
            local perms=$(stat -c "%a" "$log_file")
            
            if [[ "$owner" == "admin:tempmail" ]] && [[ "$perms" == "664" ]]; then
                echo "  ✓ $log_file (${size}字节, $owner, $perms)"
            else
                echo "  ✗ $log_file ($owner, $perms) - 权限不正确"
                files_ok=false
            fi
        else
            echo "  ! $log_file (不存在，这可能是正常的)"
        fi
    done
    
    if $files_ok; then
        log_success "日志文件状态正常"
    else
        log_error "部分日志文件权限不正确"
    fi
}

# 检查磁盘空间
test_disk_space() {
    start_test "检查磁盘空间"
    
    local usage=$(df "$LOG_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $usage -lt 80 ]]; then
        log_success "磁盘空间充足 (${usage}%)"
    elif [[ $usage -lt 90 ]]; then
        log_warning "磁盘空间使用率较高 (${usage}%)"
        ((TESTS_PASSED++))  # 警告不算失败
        TEST_RESULTS+=("WARN: 磁盘空间使用率较高 (${usage}%)")
    else
        log_error "磁盘空间不足 (${usage}%)"
    fi
}

# 模拟轮转测试
test_rotation_simulation() {
    start_test "模拟日志轮转测试"
    
    # 创建测试日志文件
    local test_log="$LOG_DIR/test_rotation.log"
    echo "Test log entry $(date)" > "$test_log"
    chown admin:tempmail "$test_log"
    chmod 664 "$test_log"
    
    # 创建临时logrotate配置
    local test_config="/tmp/test_logrotate.conf"
    cat > "$test_config" << EOF
$test_log {
    daily
    rotate 1
    missingok
    notifempty
    create 664 admin tempmail
}
EOF
    
    # 执行测试轮转
    if logrotate -f "$test_config" > /tmp/rotation_test.log 2>&1; then
        if [[ -f "${test_log}.1" ]]; then
            log_success "日志轮转模拟成功"
            # 清理测试文件
            rm -f "$test_log" "${test_log}.1"
        else
            log_error "日志轮转模拟失败 - 未生成轮转文件"
        fi
    else
        log_error "日志轮转模拟失败"
        cat /tmp/rotation_test.log
    fi
    
    # 清理
    rm -f "$test_config" /tmp/rotation_test.log
}

# 检查logrotate状态
test_logrotate_status() {
    start_test "检查logrotate状态"
    
    local status_file="/var/lib/logrotate/status"
    
    if [[ -f "$status_file" ]]; then
        if grep -q "tempmail" "$status_file"; then
            log_success "logrotate状态文件包含tempmail记录"
        else
            log_warning "logrotate状态文件中未找到tempmail记录（可能尚未执行过轮转）"
            ((TESTS_PASSED++))  # 这不算失败
            TEST_RESULTS+=("WARN: logrotate状态文件中未找到tempmail记录")
        fi
    else
        log_error "logrotate状态文件不存在"
    fi
}

# 生成测试报告
generate_report() {
    local report_file="$LOG_DIR/rotation_verification_$(date +%Y%m%d_%H%M%S).log"
    
    {
        echo "日志轮转系统验证报告"
        echo "====================="
        echo "验证时间: $(date)"
        echo "验证脚本: $0"
        echo
        echo "测试摘要:"
        echo "总测试数: $TESTS_TOTAL"
        echo "通过数: $TESTS_PASSED"
        echo "失败数: $TESTS_FAILED"
        echo "成功率: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
        echo
        echo "详细结果:"
        for result in "${TEST_RESULTS[@]}"; do
            echo "  $result"
        done
        echo
        echo "系统信息:"
        echo "操作系统: $(uname -a)"
        echo "logrotate版本: $(logrotate --version | head -1)"
        echo "Python版本: $(python3 --version)"
        echo "磁盘使用: $(df -h "$LOG_DIR" | tail -1)"
        echo
    } > "$report_file"
    
    echo "详细报告已保存到: $report_file"
}

# 显示摘要
show_summary() {
    echo
    echo "=========================================="
    echo "  验证摘要"
    echo "=========================================="
    echo "总测试数: $TESTS_TOTAL"
    echo "通过数: $TESTS_PASSED"
    echo "失败数: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}✓ 所有测试通过！日志轮转系统工作正常。${NC}"
        return 0
    else
        echo -e "${RED}✗ 有 $TESTS_FAILED 个测试失败，请检查上述错误信息。${NC}"
        return 1
    fi
}

# 主函数
main() {
    echo "日志轮转系统验证脚本"
    echo "===================="
    echo
    
    # 执行所有测试
    test_file_exists
    test_directory_permissions
    test_logrotate_syntax
    test_cron_jobs
    test_script_permissions
    test_monitoring_script
    test_log_files_status
    test_disk_space
    test_rotation_simulation
    test_logrotate_status
    
    # 生成报告和摘要
    generate_report
    show_summary
}

# 执行主函数
main "$@"
