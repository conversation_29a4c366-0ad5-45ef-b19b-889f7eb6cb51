#!/usr/bin/env python3
"""
监控系统演示脚本

展示轻量级监控系统的主要功能
"""

import os
import time
import json
from pathlib import Path

# 设置环境变量
os.environ['MONITORING_ENABLED'] = 'true'
os.environ['MONITORING_LEVEL'] = 'basic'
os.environ['MONITORING_EMAIL_ENABLED'] = 'false'
os.environ['MONITORING_WEBHOOK_ENABLED'] = 'false'

from monitoring.config import MonitoringConfig
from monitoring.lightweight_monitor import LightweightMonitor


def demo_basic_monitoring():
    """演示基础监控功能"""
    print("🚀 轻量级监控系统演示")
    print("=" * 50)
    
    # 1. 创建监控配置
    print("\n📋 1. 监控配置")
    config = MonitoringConfig(
        enabled=True,
        level='basic',
        metrics_interval=10,  # 10秒间隔用于演示
        health_check_interval=20,
        max_memory_mb=30,
        cpu_threshold=70.0,
        memory_threshold=80.0,
        email_notifications=False,  # 演示时禁用邮件
        webhook_notifications=False
    )
    
    print(f"  监控级别: {config.level}")
    print(f"  指标采集间隔: {config.metrics_interval}秒")
    print(f"  内存限制: {config.max_memory_mb}MB")
    print(f"  CPU告警阈值: {config.cpu_threshold}%")
    
    # 2. 初始化监控器
    print("\n⚙️ 2. 初始化监控器")
    monitor = LightweightMonitor(config)
    
    try:
        # 3. 启动监控
        print("\n▶️ 3. 启动监控")
        monitor.start()
        print("  监控器已启动，开始收集指标...")
        
        # 4. 等待一些指标收集
        print("\n📊 4. 收集指标数据")
        for i in range(3):
            time.sleep(12)  # 等待指标收集
            
            # 获取当前指标
            metrics = monitor.get_current_metrics()
            if 'error' not in metrics:
                print(f"  第{i+1}次采集:")
                if 'system' in metrics:
                    cpu = metrics['system'].get('cpu_percent', 0)
                    memory = metrics['system'].get('memory', {}).get('percent', 0)
                    print(f"    CPU: {cpu:.1f}%")
                    print(f"    内存: {memory:.1f}%")
                
                if 'application' in metrics:
                    app_memory = metrics['application'].get('process', {}).get('memory_mb', 0)
                    print(f"    应用内存: {app_memory:.1f}MB")
                
                if 'database' in metrics:
                    db_connected = metrics['database'].get('connection_test', False)
                    db_size = metrics['database'].get('file_size_mb', 0)
                    print(f"    数据库连接: {'✅' if db_connected else '❌'}")
                    print(f"    数据库大小: {db_size:.1f}MB")
            else:
                print(f"  ❌ 指标收集失败: {metrics['error']}")
        
        # 5. 获取监控状态
        print("\n📈 5. 监控状态")
        status = monitor.get_status()
        print(f"  运行状态: {'✅ 运行中' if status['running'] else '❌ 已停止'}")
        print(f"  运行时间: {status['uptime_seconds']:.1f}秒")
        print(f"  后台线程: {status['threads_count']}个")
        print(f"  最近指标收集耗时: {status['last_metrics_time']:.3f}秒")
        print(f"  最近健康检查耗时: {status['last_health_check_time']:.3f}秒")
        
        # 6. 获取指标摘要
        print("\n📋 6. 指标摘要")
        summary = monitor.metrics_collector.get_metrics_summary(2)
        if 'error' not in summary:
            print(f"  数据点数量: {summary['data_points']}")
            if 'cpu' in summary:
                print(f"  CPU使用率 - 平均: {summary['cpu']['avg']:.1f}%, 最大: {summary['cpu']['max']:.1f}%")
            if 'memory' in summary:
                print(f"  内存使用率 - 平均: {summary['memory']['avg']:.1f}%, 最大: {summary['memory']['max']:.1f}%")
        else:
            print(f"  ❌ 摘要获取失败: {summary['error']}")
        
        # 7. 健康检查
        print("\n🏥 7. 健康检查")
        health = monitor.force_health_check()
        print(f"  健康状态: {'✅ 正常' if health['healthy'] else '❌ 异常'}")
        print(f"  检查时间: {health['timestamp']}")
        if not health['healthy']:
            print(f"  异常原因: {health['message']}")
        
        # 8. 测试告警功能
        print("\n🚨 8. 测试告警功能")
        monitor.alert_manager.send_alert(
            'demo_test_alert',
            '这是一个演示告警',
            'warning',
            {'demo': True, 'timestamp': time.time()}
        )
        
        # 等待一下让告警处理
        time.sleep(1)
        
        active_alerts = monitor.alert_manager.get_active_alerts()
        print(f"  活跃告警数量: {len(active_alerts)}")
        
        if active_alerts:
            alert = active_alerts[0]
            print(f"  最新告警: {alert['id']} - {alert['message']}")
            print(f"  告警级别: {alert['severity']}")
        
        # 9. 资源使用情况
        print("\n💾 9. 资源使用情况")
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        print(f"  当前进程内存: {memory_mb:.1f}MB")
        print(f"  当前进程CPU: {cpu_percent:.1f}%")
        
        monitor_memory = monitor.metrics_collector.get_memory_usage()
        print(f"  监控器估算内存: {monitor_memory:.1f}MB")
        
        # 10. 保存演示结果
        print("\n💾 10. 保存演示结果")
        demo_results = {
            'config': {
                'level': config.level,
                'metrics_interval': config.metrics_interval,
                'max_memory_mb': config.max_memory_mb
            },
            'status': status,
            'latest_metrics': metrics,
            'summary': summary,
            'health': health,
            'active_alerts_count': len(active_alerts),
            'resource_usage': {
                'memory_mb': memory_mb,
                'cpu_percent': cpu_percent,
                'monitor_memory_mb': monitor_memory
            }
        }
        
        # 保存到文件
        results_file = Path('monitoring_demo_results.json')
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(demo_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"  演示结果已保存到: {results_file}")
        
    finally:
        # 停止监控
        print("\n⏹️ 停止监控器")
        monitor.stop()
        print("  监控器已停止")
    
    print("\n🎉 演示完成！")
    print("\n📝 总结:")
    print("  ✅ 监控系统启动正常")
    print("  ✅ 指标收集功能正常")
    print("  ✅ 健康检查功能正常")
    print("  ✅ 告警功能正常")
    print("  ✅ 资源使用在预期范围内")
    print(f"  📊 监控器内存占用: ~{memory_mb:.1f}MB")
    print(f"  ⚡ 监控器CPU占用: ~{cpu_percent:.1f}%")


def demo_api_endpoints():
    """演示API端点功能"""
    print("\n🌐 API端点演示")
    print("=" * 30)
    
    try:
        from app import app
        
        with app.test_client() as client:
            print("测试监控API端点:")
            
            endpoints = [
                ('/api/monitoring/status', '监控状态'),
                ('/api/monitoring/metrics', '当前指标'),
                ('/api/monitoring/health', '健康检查'),
                ('/api/monitoring/alerts', '告警信息'),
                ('/api/monitoring/metrics/summary?minutes=5', '指标摘要')
            ]
            
            for endpoint, description in endpoints:
                try:
                    response = client.get(endpoint)
                    status = "✅" if response.status_code == 200 else "❌"
                    print(f"  {status} {description}: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if endpoint == '/api/monitoring/status':
                            print(f"    运行状态: {data.get('running', False)}")
                        elif endpoint == '/api/monitoring/health':
                            print(f"    健康状态: {data.get('healthy', False)}")
                        elif endpoint == '/api/monitoring/alerts':
                            active_count = len(data.get('active_alerts', []))
                            print(f"    活跃告警: {active_count}个")
                
                except Exception as e:
                    print(f"  ❌ {description}: 异常 - {e}")
            
            print("  ✅ API端点测试完成")
    
    except ImportError:
        print("  ❌ 无法导入Flask应用，跳过API测试")


if __name__ == '__main__':
    try:
        demo_basic_monitoring()
        demo_api_endpoints()
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
