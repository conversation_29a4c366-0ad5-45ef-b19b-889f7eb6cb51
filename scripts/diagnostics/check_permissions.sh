#!/bin/bash

# 权限检查脚本（无需sudo）
# 检查当前权限状态并提供修复建议

echo "🔍 检查当前权限状态..."
echo ""

PROJECT_ROOT="/var/www/tempmail"
CURRENT_USER="admin"

# 检查当前用户和组
echo "👤 当前用户信息:"
echo "用户: $(whoami)"
echo "用户ID: $(id -u)"
echo "组: $(groups)"
echo ""

# 检查目录权限
echo "📁 目录权限检查:"
echo "项目根目录:"
ls -ld "$PROJECT_ROOT" 2>/dev/null || echo "❌ 无法访问项目根目录"

echo "数据库目录:"
ls -ld "$PROJECT_ROOT/database" 2>/dev/null || echo "❌ 无法访问数据库目录"

echo "日志目录:"
ls -ld "$PROJECT_ROOT/logs" 2>/dev/null || echo "❌ 无法访问日志目录"

echo ""

# 检查文件权限
echo "📄 文件权限检查:"
echo "数据库文件:"
ls -l "$PROJECT_ROOT/database/"*.db* 2>/dev/null || echo "ℹ️ 数据库文件不存在（正常，将在init-db时创建）"

echo "日志文件:"
ls -l "$PROJECT_ROOT/logs/"*.log 2>/dev/null | head -3 || echo "ℹ️ 日志文件不存在或无权限访问"

echo ""

# 测试写权限
echo "✏️ 写权限测试:"

# 测试数据库目录写权限
if [ -w "$PROJECT_ROOT/database" ]; then
    echo "✅ 数据库目录可写"
else
    echo "❌ 数据库目录不可写"
fi

# 测试日志目录写权限
if [ -w "$PROJECT_ROOT/logs" ]; then
    echo "✅ 日志目录可写"
else
    echo "❌ 日志目录不可写"
fi

# 测试项目根目录写权限
if [ -w "$PROJECT_ROOT" ]; then
    echo "✅ 项目根目录可写"
else
    echo "❌ 项目根目录不可写"
fi

echo ""

# 检查虚拟环境
echo "🐍 虚拟环境检查:"
if [ -d "$PROJECT_ROOT/venv" ]; then
    if [ -r "$PROJECT_ROOT/venv/bin/activate" ]; then
        echo "✅ 虚拟环境可访问"
    else
        echo "❌ 虚拟环境不可访问"
    fi
else
    echo "❌ 虚拟环境不存在"
fi

echo ""

# 提供修复建议
echo "🔧 修复建议:"
echo ""

# 检查是否需要修复权限
needs_fix=false

if [ ! -w "$PROJECT_ROOT/database" ] || [ ! -w "$PROJECT_ROOT/logs" ]; then
    needs_fix=true
fi

if [ "$needs_fix" = true ]; then
    echo "需要修复权限，请执行以下命令："
    echo ""
    echo "# 1. 修复数据库目录权限"
    echo "sudo chown -R admin:tempmail $PROJECT_ROOT/database"
    echo "sudo chmod 775 $PROJECT_ROOT/database"
    echo ""
    echo "# 2. 修复日志目录权限"
    echo "sudo chown -R admin:tempmail $PROJECT_ROOT/logs"
    echo "sudo chmod 775 $PROJECT_ROOT/logs"
    echo ""
    echo "# 3. 确保用户在tempmail组中"
    echo "sudo usermod -a -G tempmail admin"
    echo ""
    echo "# 4. 重新加载组权限"
    echo "newgrp tempmail"
    echo ""
    echo "# 5. 然后运行"
    echo "source venv/bin/activate"
    echo "flask init-db"
else
    echo "✅ 权限看起来正常，可以尝试运行："
    echo "source venv/bin/activate"
    echo "flask init-db"
fi

echo ""
echo "📋 如果问题持续存在，请检查："
echo "1. SELinux状态: getenforce"
echo "2. 磁盘空间: df -h $PROJECT_ROOT"
echo "3. 文件系统类型: df -T $PROJECT_ROOT"
