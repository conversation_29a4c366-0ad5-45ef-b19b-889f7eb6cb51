#!/bin/bash

# 简化的日志轮转检查脚本

echo "=== 日志轮转系统检查 ==="
echo

# 检查配置文件
echo "1. 检查配置文件："
if [ -f "/etc/logrotate.d/tempmail" ]; then
    echo "  ✓ logrotate配置文件存在"
else
    echo "  ✗ logrotate配置文件不存在"
fi

if [ -f "/var/www/tempmail/config/tempmail-logrotate.conf" ]; then
    echo "  ✓ 项目配置文件存在"
else
    echo "  ✗ 项目配置文件不存在"
fi

# 检查脚本
echo
echo "2. 检查管理脚本："
if [ -x "/var/www/tempmail/scripts/maintenance/logrotate_manager.sh" ]; then
    echo "  ✓ 管理脚本存在且可执行"
else
    echo "  ✗ 管理脚本不存在或不可执行"
fi

if [ -x "/var/www/tempmail/scripts/maintenance/log_rotation_monitor.py" ]; then
    echo "  ✓ 监控脚本存在且可执行"
else
    echo "  ✗ 监控脚本不存在或不可执行"
fi

# 检查日志目录
echo
echo "3. 检查日志目录："
if [ -d "/var/www/tempmail/logs" ]; then
    echo "  ✓ 日志目录存在"
    echo "  目录大小: $(du -sh /var/www/tempmail/logs | cut -f1)"
    echo "  文件数量: $(find /var/www/tempmail/logs -name "*.log" | wc -l)"
else
    echo "  ✗ 日志目录不存在"
fi

# 检查最大的日志文件
echo
echo "4. 最大的日志文件："
find /var/www/tempmail/logs -name "*.log" -type f -exec ls -lh {} \; | sort -k5 -hr | head -3

# 检查logrotate语法
echo
echo "5. 检查logrotate配置语法："
if sudo logrotate -d /etc/logrotate.d/tempmail >/dev/null 2>&1; then
    echo "  ✓ 配置语法正确"
else
    echo "  ✗ 配置语法错误"
fi

echo
echo "=== 检查完成 ==="
