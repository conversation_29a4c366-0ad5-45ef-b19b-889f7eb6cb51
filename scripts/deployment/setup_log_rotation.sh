#!/bin/bash

# 临时邮箱系统日志轮转快速部署脚本
# 功能：一键安装和配置完整的日志轮转系统

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_DIR="$PROJECT_ROOT/logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查logrotate
    if ! command -v logrotate &> /dev/null; then
        log_error "logrotate未安装"
        echo "请安装logrotate: apt-get install logrotate"
        exit 1
    fi
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        echo "请安装Python3: apt-get install python3"
        exit 1
    fi
    
    # 检查cron
    if ! command -v crontab &> /dev/null; then
        log_error "cron未安装"
        echo "请安装cron: apt-get install cron"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建用户和组
setup_users() {
    log_info "设置用户和组..."
    
    # 创建tempmail组（如果不存在）
    if ! getent group tempmail > /dev/null 2>&1; then
        groupadd tempmail
        log_info "创建tempmail组"
    fi
    
    # 确保admin用户在tempmail组中
    if id "admin" &>/dev/null; then
        usermod -a -G tempmail admin
        log_info "将admin用户添加到tempmail组"
    else
        log_warning "admin用户不存在，请手动创建或调整用户配置"
    fi
    
    log_success "用户和组设置完成"
}

# 设置目录和权限
setup_directories() {
    log_info "设置目录和权限..."
    
    # 创建必要目录
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/temp_logs"
    mkdir -p "$PROJECT_ROOT/config"
    
    # 设置权限
    chown -R admin:tempmail "$LOG_DIR"
    chown -R admin:tempmail "$PROJECT_ROOT/temp_logs"
    chmod 755 "$LOG_DIR"
    chmod 755 "$PROJECT_ROOT/temp_logs"
    
    # 创建日志文件（如果不存在）
    touch "$LOG_DIR/logrotate.log"
    touch "$LOG_DIR/log_monitor.log"
    chown admin:tempmail "$LOG_DIR/logrotate.log"
    chown admin:tempmail "$LOG_DIR/log_monitor.log"
    chmod 664 "$LOG_DIR/logrotate.log"
    chmod 664 "$LOG_DIR/log_monitor.log"
    
    log_success "目录和权限设置完成"
}

# 安装logrotate配置
install_logrotate() {
    log_info "安装logrotate配置..."
    
    # 使用管理脚本安装
    if [[ -f "$PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh" ]]; then
        "$PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh" setup
        "$PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh" install
        log_success "logrotate配置安装完成"
    else
        log_error "logrotate管理脚本不存在"
        exit 1
    fi
}

# 设置cron任务
setup_cron() {
    log_info "设置cron任务..."
    
    # 备份现有crontab
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    # 创建新的crontab条目
    cat > /tmp/tempmail_cron << EOF
# 临时邮箱系统日志轮转任务
# 每日凌晨2点执行日志轮转
0 2 * * * /usr/sbin/logrotate /etc/logrotate.d/tempmail >/dev/null 2>&1

# 每4小时进行完整监控检查
0 */4 * * * $PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py >/dev/null 2>&1

# 每周日凌晨3点清理超期日志
0 3 * * 0 $PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh cleanup >/dev/null 2>&1

# 每月1号凌晨4点生成月度报告
0 4 1 * * $PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py --report-only > $LOG_DIR/monthly_log_report_\$(date +%Y%m).json 2>&1
EOF
    
    # 合并现有crontab和新任务
    (crontab -l 2>/dev/null || true; echo ""; cat /tmp/tempmail_cron) | crontab -
    
    # 清理临时文件
    rm -f /tmp/tempmail_cron
    
    log_success "cron任务设置完成"
}

# 创建监控配置
setup_monitoring() {
    log_info "设置监控配置..."
    
    local config_file="$PROJECT_ROOT/config/log_monitor_config.json"
    
    if [[ ! -f "$config_file" ]]; then
        cat > "$config_file" << EOF
{
  "thresholds": {
    "high_frequency_logs": {
      "size_warning": "40M",
      "size_critical": "80M",
      "files": ["mail_handler.log"]
    },
    "medium_frequency_logs": {
      "size_warning": "80M",
      "size_critical": "150M",
      "files": ["app.log", "gunicorn_access.log", "gunicorn_error.log"]
    },
    "low_frequency_logs": {
      "size_warning": "8M",
      "size_critical": "15M",
      "files": ["monitoring.log", "health-check.log"]
    }
  },
  "rotation_check": {
    "max_days_without_rotation": 7,
    "check_compressed_files": true
  },
  "disk_usage": {
    "warning_threshold": "80%",
    "critical_threshold": "90%"
  },
  "alerts": {
    "email_enabled": true,
    "email_recipients": ["<EMAIL>"],
    "cooldown_minutes": 60
  }
}
EOF
        chown admin:tempmail "$config_file"
        chmod 644 "$config_file"
        log_success "监控配置文件创建完成"
    else
        log_info "监控配置文件已存在，跳过创建"
    fi
}

# 测试配置
test_setup() {
    log_info "测试配置..."
    
    # 测试logrotate配置
    if logrotate -d /etc/logrotate.d/tempmail > /tmp/logrotate_test.log 2>&1; then
        log_success "logrotate配置测试通过"
    else
        log_error "logrotate配置测试失败"
        cat /tmp/logrotate_test.log
        exit 1
    fi
    
    # 测试监控脚本
    if python3 "$PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py" --no-alerts > /tmp/monitor_test.log 2>&1; then
        log_success "监控脚本测试通过"
    else
        log_warning "监控脚本测试失败，请检查Python依赖"
        cat /tmp/monitor_test.log
    fi
    
    # 测试cron任务
    if crontab -l | grep -q "tempmail"; then
        log_success "cron任务设置验证通过"
    else
        log_warning "cron任务可能未正确设置"
    fi
    
    # 清理测试文件
    rm -f /tmp/logrotate_test.log /tmp/monitor_test.log
}

# 显示部署摘要
show_summary() {
    echo
    echo "=========================================="
    echo "  日志轮转系统部署完成"
    echo "=========================================="
    echo
    echo "已安装组件："
    echo "✓ logrotate配置文件: /etc/logrotate.d/tempmail"
    echo "✓ 管理脚本: $PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh"
    echo "✓ 监控脚本: $PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py"
    echo "✓ 监控配置: $PROJECT_ROOT/config/log_monitor_config.json"
    echo "✓ cron任务: 已添加到root用户crontab"
    echo
    echo "轮转策略："
    echo "• 高频日志 (mail_handler.log): 每日轮转，保留14天，50MB触发"
    echo "• 中频日志 (app.log, gunicorn_*.log): 每日轮转，保留30天，100MB触发"
    echo "• 低频日志 (monitoring.log, health-check.log): 每周轮转，保留52周，10MB触发"
    echo "• 临时日志 (temp_logs/*.log): 每日轮转，保留3天"
    echo
    echo "常用命令："
    echo "• 查看状态: $PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh status"
    echo "• 监控检查: $PROJECT_ROOT/scripts/maintenance/log_rotation_monitor.py"
    echo "• 手动轮转: sudo $PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh force"
    echo "• 清理日志: sudo $PROJECT_ROOT/scripts/maintenance/logrotate_manager.sh cleanup"
    echo
    echo "文档位置："
    echo "• 详细指南: $PROJECT_ROOT/docs/LOG_MANAGEMENT_GUIDE.md"
    echo "• 配置模板: $PROJECT_ROOT/config/templates/"
    echo
    echo "下一步："
    echo "1. 查看文档了解详细使用方法"
    echo "2. 根据需要调整监控配置"
    echo "3. 测试告警邮件功能"
    echo "4. 定期检查日志轮转状态"
    echo
}

# 主函数
main() {
    echo "临时邮箱系统日志轮转部署脚本"
    echo "=================================="
    echo
    
    check_permissions
    check_requirements
    setup_users
    setup_directories
    install_logrotate
    setup_cron
    setup_monitoring
    test_setup
    show_summary
    
    log_success "部署完成！"
}

# 执行主函数
main "$@"
