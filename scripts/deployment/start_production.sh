#!/bin/bash

# 临时邮箱系统生产环境启动脚本
# 使用 Gunicorn 作为 WSGI 服务器

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查必要的文件和目录
check_prerequisites() {
    log_info "检查运行环境..."
    
    # 检查虚拟环境
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        log_warn "未检测到虚拟环境，建议在虚拟环境中运行"
    else
        log_info "虚拟环境: $VIRTUAL_ENV"
    fi
    
    # 检查必要文件
    if [[ ! -f "app.py" ]]; then
        log_error "app.py 文件不存在"
        exit 1
    fi
    
    if [[ ! -f "gunicorn.conf.py" ]]; then
        log_error "gunicorn.conf.py 配置文件不存在"
        exit 1
    fi
    
    # 检查并创建必要目录
    mkdir -p logs
    mkdir -p database
    mkdir -p run
    
    log_info "环境检查完成"
}

# 检查端口是否被占用
check_port() {
    local port=${1:-8080}
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_error "端口 $port 已被占用"
        log_info "使用以下命令查看占用进程: lsof -i :$port"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    python -c "
from app import app
with app.app_context():
    from app import init_db_schema
    init_db_schema()
    print('数据库初始化完成')
"
}

# 启动 Gunicorn 服务器
start_gunicorn() {
    local port=${1:-8080}
    local workers=${2:-2}
    
    log_info "启动 Gunicorn 服务器..."
    log_info "端口: $port"
    log_info "工作进程数: $workers"
    
    # 更新配置文件中的端口
    sed -i "s/bind = \"0.0.0.0:[0-9]*\"/bind = \"0.0.0.0:$port\"/" gunicorn.conf.py
    sed -i "s/workers = [0-9]*/workers = $workers/" gunicorn.conf.py
    
    # 启动服务器
    exec gunicorn -c gunicorn.conf.py app:app
}

# 显示帮助信息
show_help() {
    echo "临时邮箱系统生产环境启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT       指定端口号 (默认: 8080)"
    echo "  -w, --workers NUM     指定工作进程数 (默认: 2)"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 使用默认配置启动"
    echo "  $0 -p 8080           # 在端口 8080 启动"
    echo "  $0 -p 8080 -w 4      # 在端口 8080 启动，使用 4 个工作进程"
}

# 主函数
main() {
    local port=8080
    local workers=2
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--port)
                port="$2"
                shift 2
                ;;
            -w|--workers)
                workers="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证参数
    if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
        log_error "无效的端口号: $port"
        exit 1
    fi
    
    if ! [[ "$workers" =~ ^[0-9]+$ ]] || [ "$workers" -lt 1 ]; then
        log_error "无效的工作进程数: $workers"
        exit 1
    fi
    
    # 执行启动流程
    check_prerequisites
    check_port $port
    init_database
    start_gunicorn $port $workers
}

# 信号处理
trap 'log_info "正在停止服务器..."; exit 0' SIGINT SIGTERM

# 运行主函数
main "$@"
