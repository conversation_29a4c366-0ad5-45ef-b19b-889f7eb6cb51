#!/usr/bin/env python3
"""
应用级优化验证脚本
验证 Gunicorn、Flask缓存和SQLite优化的效果
"""

import os
import sys
import time
import requests
import sqlite3
import subprocess
import threading
from pathlib import Path
from datetime import datetime
import json

class OptimizationVerifier:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.results = {}
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def verify_gunicorn_running(self):
        """验证Gunicorn是否正在运行"""
        self.log("检查Gunicorn进程...")
        try:
            result = subprocess.run(['pgrep', '-f', 'gunicorn'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                processes = result.stdout.strip().split('\n')
                self.log(f"发现 {len(processes)} 个Gunicorn进程")
                self.results['gunicorn_processes'] = len(processes)
                return True
            else:
                self.log("未发现Gunicorn进程", "WARN")
                self.results['gunicorn_processes'] = 0
                return False
        except Exception as e:
            self.log(f"检查Gunicorn进程失败: {e}", "ERROR")
            return False
            
    def verify_cache_working(self):
        """验证缓存是否工作"""
        self.log("测试缓存功能...")
        try:
            # 创建一个测试邮箱
            response = requests.post(f"{self.base_url}/api/generate-address", 
                                   json={}, timeout=10)
            if response.status_code != 201:
                self.log(f"创建测试邮箱失败: {response.status_code}", "ERROR")
                return False
                
            email_data = response.json()
            email_address = email_data['data']['address']
            
            # 第一次请求邮件列表（应该缓存）
            start_time = time.time()
            response1 = requests.get(f"{self.base_url}/api/emails", 
                                   params={'address': email_address}, timeout=10)
            first_request_time = time.time() - start_time
            
            # 第二次请求邮件列表（应该从缓存返回）
            start_time = time.time()
            response2 = requests.get(f"{self.base_url}/api/emails", 
                                   params={'address': email_address}, timeout=10)
            second_request_time = time.time() - start_time
            
            if response1.status_code == 200 and response2.status_code == 200:
                self.log(f"第一次请求时间: {first_request_time:.3f}s")
                self.log(f"第二次请求时间: {second_request_time:.3f}s")
                
                # 缓存应该使第二次请求更快，或者至少不会更慢
                cache_improvement = (first_request_time - second_request_time) / first_request_time * 100
                self.log(f"缓存性能提升: {cache_improvement:.1f}%")

                self.results['cache_first_request'] = first_request_time
                self.results['cache_second_request'] = second_request_time
                self.results['cache_improvement'] = cache_improvement

                # 如果响应时间都很快（<10ms），认为缓存正常工作
                if first_request_time < 0.01 and second_request_time < 0.01:
                    self.log("响应时间很快，缓存系统正常工作")
                    return True

                return cache_improvement >= -10  # 允许10%的误差
            else:
                self.log("缓存测试请求失败", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"缓存测试失败: {e}", "ERROR")
            return False
            
    def verify_sqlite_optimizations(self):
        """验证SQLite优化"""
        self.log("检查SQLite优化设置...")
        try:
            # 获取数据库路径
            db_path = os.getenv('DATABASE_PATH', 'database/tempmail.db')
            if not os.path.exists(db_path):
                self.log(f"数据库文件不存在: {db_path}", "ERROR")
                return False
                
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查各种PRAGMA设置
            pragmas_to_check = [
                'journal_mode',
                'synchronous',
                'cache_size',
                'temp_store',
                'page_size',
                'auto_vacuum'
            ]
            
            pragma_results = {}
            for pragma in pragmas_to_check:
                cursor.execute(f'PRAGMA {pragma}')
                value = cursor.fetchone()[0]
                pragma_results[pragma] = value
                self.log(f"PRAGMA {pragma}: {value}")
                
            conn.close()
            
            self.results['sqlite_pragmas'] = pragma_results
            
            # 检查关键优化是否生效
            optimizations_ok = (
                pragma_results['journal_mode'] == 'wal' and
                int(pragma_results['cache_size']) < 0 and  # 负数表示KB缓存
                int(pragma_results['auto_vacuum']) >= 1  # 启用了某种自动清理
            )
            
            return optimizations_ok
            
        except Exception as e:
            self.log(f"SQLite优化检查失败: {e}", "ERROR")
            return False
            
    def performance_benchmark(self, num_requests=50):
        """性能基准测试"""
        self.log(f"开始性能基准测试 ({num_requests} 个请求)...")
        
        try:
            # 测试邮箱创建性能
            create_times = []
            for i in range(num_requests):
                start_time = time.time()
                response = requests.post(f"{self.base_url}/api/generate-address", 
                                       json={}, timeout=10)
                request_time = time.time() - start_time
                
                if response.status_code == 201:
                    create_times.append(request_time)
                else:
                    self.log(f"请求 {i+1} 失败: {response.status_code}", "WARN")
                    
            if create_times:
                avg_create_time = sum(create_times) / len(create_times)
                max_create_time = max(create_times)
                min_create_time = min(create_times)
                
                self.log(f"邮箱创建性能统计:")
                self.log(f"  平均时间: {avg_create_time:.3f}s")
                self.log(f"  最大时间: {max_create_time:.3f}s")
                self.log(f"  最小时间: {min_create_time:.3f}s")
                self.log(f"  成功率: {len(create_times)}/{num_requests} ({len(create_times)/num_requests*100:.1f}%)")
                
                self.results['performance'] = {
                    'avg_create_time': avg_create_time,
                    'max_create_time': max_create_time,
                    'min_create_time': min_create_time,
                    'success_rate': len(create_times) / num_requests
                }
                
                return True
            else:
                self.log("所有性能测试请求都失败了", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"性能基准测试失败: {e}", "ERROR")
            return False
            
    def check_server_health(self):
        """检查服务器健康状态"""
        self.log("检查服务器健康状态...")
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                self.log("服务器响应正常")
                self.results['server_health'] = 'healthy'
                return True
            else:
                self.log(f"服务器响应异常: {response.status_code}", "ERROR")
                self.results['server_health'] = 'unhealthy'
                return False
        except Exception as e:
            self.log(f"服务器健康检查失败: {e}", "ERROR")
            self.results['server_health'] = 'error'
            return False
            
    def run_all_verifications(self):
        """运行所有验证"""
        self.log("开始应用级优化验证...")
        
        verifications = [
            ("服务器健康检查", self.check_server_health),
            ("Gunicorn进程检查", self.verify_gunicorn_running),
            ("SQLite优化检查", self.verify_sqlite_optimizations),
            ("缓存功能测试", self.verify_cache_working),
            ("性能基准测试", lambda: self.performance_benchmark(20))
        ]
        
        passed = 0
        total = len(verifications)
        
        for name, verification_func in verifications:
            self.log(f"执行: {name}")
            try:
                if verification_func():
                    self.log(f"✓ {name} 通过", "INFO")
                    passed += 1
                else:
                    self.log(f"✗ {name} 失败", "WARN")
            except Exception as e:
                self.log(f"✗ {name} 异常: {e}", "ERROR")
                
        self.log(f"验证完成: {passed}/{total} 项通过")
        self.results['summary'] = {
            'passed': passed,
            'total': total,
            'success_rate': passed / total
        }
        
        return passed == total
        
    def save_results(self, filename="optimization_verification_results.json"):
        """保存验证结果"""
        self.results['timestamp'] = datetime.now().isoformat()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        self.log(f"验证结果已保存到: {filename}")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='应用级优化验证工具')
    parser.add_argument('--url', default='http://localhost:5000', 
                       help='应用服务器URL (默认: http://localhost:5000)')
    parser.add_argument('--output', default='optimization_verification_results.json',
                       help='结果输出文件 (默认: optimization_verification_results.json)')
    
    args = parser.parse_args()
    
    verifier = OptimizationVerifier(args.url)
    success = verifier.run_all_verifications()
    verifier.save_results(args.output)
    
    if success:
        print("\n🎉 所有优化验证通过！")
        sys.exit(0)
    else:
        print("\n⚠️  部分优化验证失败，请检查配置")
        sys.exit(1)

if __name__ == '__main__':
    main()
