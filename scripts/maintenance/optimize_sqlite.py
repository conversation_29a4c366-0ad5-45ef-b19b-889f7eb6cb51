#!/usr/bin/env python3
"""
SQLite数据库优化脚本
重新配置数据库以应用所有优化设置
"""

import os
import sqlite3
import shutil
from pathlib import Path
from datetime import datetime

def log(message, level="INFO"):
    """日志输出"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def backup_database(db_path):
    """备份数据库"""
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    log(f"备份数据库到: {backup_path}")
    shutil.copy2(db_path, backup_path)
    return backup_path

def get_current_pragmas(db_path):
    """获取当前PRAGMA设置"""
    log("检查当前PRAGMA设置...")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    pragmas = {}
    pragma_list = [
        'journal_mode',
        'synchronous', 
        'cache_size',
        'temp_store',
        'page_size',
        'auto_vacuum',
        'mmap_size'
    ]
    
    for pragma in pragma_list:
        cursor.execute(f'PRAGMA {pragma}')
        value = cursor.fetchone()[0]
        pragmas[pragma] = value
        log(f"  {pragma}: {value}")
    
    conn.close()
    return pragmas

def optimize_database(db_path):
    """优化数据库设置"""
    log("开始优化数据库...")
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 应用优化设置
        optimizations = [
            ('journal_mode', 'WAL'),
            ('synchronous', 'NORMAL'),
            ('temp_store', 'MEMORY'),
            ('cache_size', '-8000'),  # 8MB缓存
            ('mmap_size', '268435456'),  # 256MB内存映射
            ('auto_vacuum', 'INCREMENTAL'),
        ]
        
        for pragma, value in optimizations:
            try:
                log(f"设置 PRAGMA {pragma}={value}")
                cursor.execute(f'PRAGMA {pragma}={value}')
                
                # 验证设置
                cursor.execute(f'PRAGMA {pragma}')
                actual_value = cursor.fetchone()[0]
                
                if pragma == 'synchronous':
                    # synchronous返回数字值
                    expected_map = {'NORMAL': 1, 'FULL': 2, 'OFF': 0}
                    if str(actual_value) == str(expected_map.get(value, value)):
                        log(f"  ✓ {pragma} 设置成功: {actual_value}")
                    else:
                        log(f"  ⚠ {pragma} 设置可能未生效: 期望 {value}, 实际 {actual_value}")
                elif pragma == 'temp_store':
                    # temp_store返回数字值
                    expected_map = {'MEMORY': 2, 'FILE': 1, 'DEFAULT': 0}
                    if str(actual_value) == str(expected_map.get(value, value)):
                        log(f"  ✓ {pragma} 设置成功: {actual_value}")
                    else:
                        log(f"  ⚠ {pragma} 设置可能未生效: 期望 {value}, 实际 {actual_value}")
                elif pragma == 'auto_vacuum':
                    # auto_vacuum返回数字值
                    expected_map = {'INCREMENTAL': 2, 'FULL': 1, 'NONE': 0}
                    if str(actual_value) == str(expected_map.get(value, value)):
                        log(f"  ✓ {pragma} 设置成功: {actual_value}")
                    else:
                        log(f"  ⚠ {pragma} 设置可能未生效: 期望 {value}, 实际 {actual_value}")
                else:
                    if str(actual_value).lower() == str(value).lower():
                        log(f"  ✓ {pragma} 设置成功: {actual_value}")
                    else:
                        log(f"  ⚠ {pragma} 设置可能未生效: 期望 {value}, 实际 {actual_value}")
                        
            except sqlite3.OperationalError as e:
                log(f"  ✗ {pragma} 设置失败: {e}", "WARN")
        
        # 提交更改
        conn.commit()
        log("数据库优化设置已提交")
        
        # 执行VACUUM以应用某些设置
        log("执行VACUUM优化...")
        cursor.execute('VACUUM')
        log("VACUUM完成")
        
    except Exception as e:
        log(f"优化过程中出错: {e}", "ERROR")
        conn.rollback()
        raise
    finally:
        conn.close()

def verify_optimizations(db_path):
    """验证优化效果"""
    log("验证优化效果...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查关键设置
    checks = [
        ('journal_mode', 'wal'),
        ('synchronous', '1'),  # NORMAL = 1
        ('temp_store', '2'),   # MEMORY = 2
        ('auto_vacuum', '2'),  # INCREMENTAL = 2
    ]
    
    all_good = True
    for pragma, expected in checks:
        cursor.execute(f'PRAGMA {pragma}')
        actual = str(cursor.fetchone()[0]).lower()
        
        if actual == expected:
            log(f"  ✓ {pragma}: {actual}")
        else:
            log(f"  ⚠ {pragma}: 期望 {expected}, 实际 {actual}", "WARN")
            all_good = False
    
    # 检查缓存大小
    cursor.execute('PRAGMA cache_size')
    cache_size = cursor.fetchone()[0]
    if int(cache_size) < -1000:  # 至少1MB缓存
        log(f"  ✓ cache_size: {cache_size}")
    else:
        log(f"  ⚠ cache_size 可能过小: {cache_size}", "WARN")
        all_good = False
    
    conn.close()
    
    if all_good:
        log("✅ 所有优化设置验证通过")
    else:
        log("⚠️ 部分优化设置可能未生效", "WARN")
    
    return all_good

def main():
    # 数据库路径
    db_path = "/var/www/tempmail/database/tempmail.db"
    
    if not os.path.exists(db_path):
        log(f"数据库文件不存在: {db_path}", "ERROR")
        return False
    
    log("开始SQLite数据库优化...")
    log(f"数据库路径: {db_path}")
    
    # 获取当前设置
    current_pragmas = get_current_pragmas(db_path)
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        # 优化数据库
        optimize_database(db_path)
        
        # 验证优化
        success = verify_optimizations(db_path)
        
        if success:
            log("🎉 SQLite数据库优化完成！")
            log(f"备份文件: {backup_path}")
            return True
        else:
            log("⚠️ 优化完成，但部分设置可能需要重启应用才能生效", "WARN")
            return True
            
    except Exception as e:
        log(f"优化失败: {e}", "ERROR")
        log(f"可以从备份恢复: {backup_path}")
        return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
