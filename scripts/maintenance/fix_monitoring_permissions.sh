#!/bin/bash

# 监控系统权限修复脚本
# 解决临时邮箱系统中监控相关的文件权限问题

echo "=========================================="
echo "    监控系统权限修复脚本"
echo "=========================================="
echo

# 检查当前用户和组
echo "1. 当前用户信息:"
echo "   用户: $(whoami)"
echo "   用户ID: $(id -u)"
echo "   主组: $(id -gn)"
echo "   所属组: $(groups)"
echo

# 检查关键目录权限
echo "2. 当前权限状态:"
echo "   项目根目录:"
ls -ld . 2>/dev/null || echo "   无法访问项目根目录"

echo "   日志目录:"
ls -ld logs/ 2>/dev/null || echo "   日志目录不存在"

echo "   监控目录:"
ls -ld monitoring/ 2>/dev/null || echo "   监控目录不存在"

echo "   数据库目录:"
ls -ld database/ 2>/dev/null || echo "   数据库目录不存在"
echo

# 检查关键文件权限
echo "3. 关键文件权限:"
echo "   日志文件:"
ls -l logs/*.log 2>/dev/null | head -5 || echo "   无日志文件或无法访问"

echo "   监控文件:"
ls -l monitoring/*.py 2>/dev/null | head -3 || echo "   无监控文件或无法访问"

echo "   数据库文件:"
ls -l database/*.db 2>/dev/null || echo "   无数据库文件或无法访问"
echo

# 权限问题诊断
echo "4. 权限问题诊断:"

# 检查日志写入权限
if [ -w logs/ ]; then
    echo "   ✅ 日志目录可写"
else
    echo "   ❌ 日志目录不可写"
fi

# 检查监控目录读取权限
if [ -r monitoring/ ]; then
    echo "   ✅ 监控目录可读"
else
    echo "   ❌ 监控目录不可读"
fi

# 检查数据库权限
if [ -r database/ ] && [ -w database/ ]; then
    echo "   ✅ 数据库目录可读写"
elif [ -r database/ ]; then
    echo "   ⚠️  数据库目录只读"
else
    echo "   ❌ 数据库目录不可访问"
fi

echo

# 提供修复方案
echo "5. 权限修复方案:"
echo

if [ "$(whoami)" = "root" ]; then
    echo "   检测到root用户，执行完整权限修复..."
    
    # 修复日志目录权限
    echo "   修复日志目录权限..."
    chgrp -R tempmail logs/ 2>/dev/null
    chmod 775 logs/ 2>/dev/null
    chmod -R 664 logs/*.log 2>/dev/null
    chmod g+s logs/ 2>/dev/null  # 设置SGID位
    
    # 修复监控目录权限
    echo "   修复监控目录权限..."
    chgrp -R tempmail monitoring/ 2>/dev/null
    chmod 755 monitoring/ 2>/dev/null
    chmod -R 644 monitoring/*.py 2>/dev/null
    
    # 修复应用文件权限
    echo "   修复应用文件权限..."
    chgrp tempmail app.py mail_handler.py cleanup_script.py 2>/dev/null
    chmod 664 app.py mail_handler.py cleanup_script.py 2>/dev/null
    
    echo "   ✅ 权限修复完成"
    
elif groups | grep -q tempmail; then
    echo "   检测到用户属于tempmail组，执行部分权限修复..."
    
    # 只能修改tempmail组的文件
    echo "   修复tempmail组文件权限..."
    find . -group tempmail -type f -exec chmod g+w {} \; 2>/dev/null
    find . -group tempmail -type d -exec chmod g+w {} \; 2>/dev/null
    
    echo "   ⚠️  部分权限修复完成，某些文件可能仍需要管理员权限"
    
else
    echo "   ❌ 当前用户权限不足，需要管理员权限修复"
    echo
    echo "   请联系管理员执行以下命令："
    echo "   sudo bash fix_monitoring_permissions.sh"
    echo
    echo "   或者手动执行以下命令："
    echo "   sudo chgrp -R tempmail logs/ monitoring/"
    echo "   sudo chmod 775 logs/"
    echo "   sudo chmod -R 664 logs/*.log"
    echo "   sudo chmod g+s logs/"
    echo "   sudo chmod 755 monitoring/"
    echo "   sudo chmod -R 644 monitoring/*.py"
fi

echo
echo "6. 验证修复结果:"

# 测试权限修复结果
python3 -c "
import os
from pathlib import Path

print('   测试日志写入权限:')
try:
    test_log = Path('logs/permission_test.log')
    with open(test_log, 'w') as f:
        f.write('权限测试成功\n')
    test_log.unlink()
    print('   ✅ 日志写入权限正常')
except Exception as e:
    print(f'   ❌ 日志写入失败: {e}')

print('   测试监控模块导入:')
try:
    from monitoring.config import MonitoringConfig
    config = MonitoringConfig.from_env()
    print('   ✅ 监控模块导入成功')
except Exception as e:
    print(f'   ❌ 监控模块导入失败: {e}')

print('   测试数据库访问:')
try:
    import sqlite3
    db_path = os.getenv('DATABASE_PATH', 'database/tempmail.db')
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT 1')
    print('   ✅ 数据库访问正常')
except Exception as e:
    print(f'   ❌ 数据库访问失败: {e}')
"

echo
echo "=========================================="
echo "    权限修复脚本执行完成"
echo "=========================================="
