#!/usr/bin/env python3
"""
临时邮箱系统日志轮转监控脚本
功能：监控日志文件大小、轮转状态，并发送告警
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
script_dir = Path(__file__).parent
project_root = script_dir.parent.parent
sys.path.insert(0, str(project_root))

try:
    from monitoring.internal_mail_sender import InternalMailSender
except ImportError:
    InternalMailSender = None

class LogRotationMonitor:
    """日志轮转监控器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.project_root = project_root
        self.log_dir = self.project_root / "logs"
        self.temp_log_dir = self.project_root / "temp_logs"
        self.config_file = config_file or self.project_root / "config" / "log_monitor_config.json"
        
        # 加载配置
        self.config = self._load_config()
        
        # 设置日志
        self._setup_logging()
        
        # 初始化邮件发送器
        self.mail_sender = None
        if InternalMailSender:
            try:
                self.mail_sender = InternalMailSender()
                self.logger.info("邮件发送器初始化成功")
            except Exception as e:
                self.logger.warning(f"邮件发送器初始化失败: {e}")
    
    def _load_config(self) -> Dict:
        """加载监控配置"""
        default_config = {
            "thresholds": {
                "high_frequency_logs": {
                    "size_warning": "40M",
                    "size_critical": "80M",
                    "files": ["mail_handler.log"]
                },
                "medium_frequency_logs": {
                    "size_warning": "80M",
                    "size_critical": "150M", 
                    "files": ["app.log", "gunicorn_access.log", "gunicorn_error.log"]
                },
                "low_frequency_logs": {
                    "size_warning": "8M",
                    "size_critical": "15M",
                    "files": ["monitoring.log", "health-check.log"]
                }
            },
            "rotation_check": {
                "max_days_without_rotation": 7,
                "check_compressed_files": True
            },
            "disk_usage": {
                "warning_threshold": "80%",
                "critical_threshold": "90%"
            },
            "alerts": {
                "email_enabled": True,
                "email_recipients": ["<EMAIL>"],
                "cooldown_minutes": 60
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
        
        # 保存默认配置
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        return default_config
    
    def _setup_logging(self):
        """设置日志"""
        log_file = self.log_dir / "log_monitor.log"
        self.log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LogRotationMonitor')
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串为字节数"""
        size_str = size_str.upper().strip()
        if size_str.endswith('K'):
            return int(size_str[:-1]) * 1024
        elif size_str.endswith('M'):
            return int(size_str[:-1]) * 1024 * 1024
        elif size_str.endswith('G'):
            return int(size_str[:-1]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f}{unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f}TB"
    
    def check_file_sizes(self) -> List[Dict]:
        """检查文件大小"""
        alerts = []
        
        for category, config in self.config["thresholds"].items():
            warning_size = self._parse_size(config["size_warning"])
            critical_size = self._parse_size(config["size_critical"])
            
            for filename in config["files"]:
                file_path = self.log_dir / filename
                if not file_path.exists():
                    continue
                
                file_size = file_path.stat().st_size
                
                if file_size >= critical_size:
                    alerts.append({
                        "type": "file_size",
                        "level": "critical",
                        "file": filename,
                        "size": self._format_size(file_size),
                        "threshold": config["size_critical"],
                        "category": category,
                        "message": f"日志文件 {filename} 大小 {self._format_size(file_size)} 超过临界阈值 {config['size_critical']}"
                    })
                elif file_size >= warning_size:
                    alerts.append({
                        "type": "file_size", 
                        "level": "warning",
                        "file": filename,
                        "size": self._format_size(file_size),
                        "threshold": config["size_warning"],
                        "category": category,
                        "message": f"日志文件 {filename} 大小 {self._format_size(file_size)} 超过警告阈值 {config['size_warning']}"
                    })
        
        return alerts
    
    def check_rotation_status(self) -> List[Dict]:
        """检查轮转状态"""
        alerts = []
        max_days = self.config["rotation_check"]["max_days_without_rotation"]
        cutoff_time = datetime.now() - timedelta(days=max_days)
        
        # 检查logrotate状态文件
        status_file = Path("/var/lib/logrotate/status")
        if status_file.exists():
            try:
                with open(status_file, 'r') as f:
                    content = f.read()
                    if "tempmail" not in content:
                        alerts.append({
                            "type": "rotation_status",
                            "level": "warning", 
                            "message": "logrotate状态文件中未找到tempmail配置记录"
                        })
            except Exception as e:
                alerts.append({
                    "type": "rotation_status",
                    "level": "error",
                    "message": f"无法读取logrotate状态文件: {e}"
                })
        
        # 检查是否有轮转文件
        for log_file in self.log_dir.glob("*.log"):
            rotated_files = list(self.log_dir.glob(f"{log_file.name}.*"))
            if not rotated_files:
                # 检查文件修改时间
                mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                if mtime < cutoff_time:
                    alerts.append({
                        "type": "rotation_status",
                        "level": "warning",
                        "file": log_file.name,
                        "message": f"日志文件 {log_file.name} 可能未正确轮转，最后修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}"
                    })
        
        return alerts
    
    def check_disk_usage(self) -> List[Dict]:
        """检查磁盘使用情况"""
        alerts = []
        
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.log_dir)
            usage_percent = (used / total) * 100
            
            warning_threshold = float(self.config["disk_usage"]["warning_threshold"].rstrip('%'))
            critical_threshold = float(self.config["disk_usage"]["critical_threshold"].rstrip('%'))
            
            if usage_percent >= critical_threshold:
                alerts.append({
                    "type": "disk_usage",
                    "level": "critical",
                    "usage": f"{usage_percent:.1f}%",
                    "message": f"日志目录磁盘使用率 {usage_percent:.1f}% 超过临界阈值 {critical_threshold}%"
                })
            elif usage_percent >= warning_threshold:
                alerts.append({
                    "type": "disk_usage",
                    "level": "warning", 
                    "usage": f"{usage_percent:.1f}%",
                    "message": f"日志目录磁盘使用率 {usage_percent:.1f}% 超过警告阈值 {warning_threshold}%"
                })
                
        except Exception as e:
            alerts.append({
                "type": "disk_usage",
                "level": "error",
                "message": f"无法检查磁盘使用情况: {e}"
            })
        
        return alerts
    
    def send_alert(self, alerts: List[Dict]):
        """发送告警"""
        if not alerts or not self.config["alerts"]["email_enabled"]:
            return
        
        # 检查冷却时间
        cooldown_file = self.log_dir / ".alert_cooldown"
        if cooldown_file.exists():
            try:
                last_alert_time = datetime.fromtimestamp(cooldown_file.stat().st_mtime)
                cooldown_minutes = self.config["alerts"]["cooldown_minutes"]
                if datetime.now() - last_alert_time < timedelta(minutes=cooldown_minutes):
                    self.logger.info(f"告警冷却中，跳过发送 ({cooldown_minutes}分钟冷却期)")
                    return
            except Exception:
                pass
        
        # 构建告警邮件
        critical_alerts = [a for a in alerts if a["level"] == "critical"]
        warning_alerts = [a for a in alerts if a["level"] == "warning"]
        
        subject = f"日志轮转监控告警 - {len(critical_alerts)}个严重, {len(warning_alerts)}个警告"
        
        body_parts = [
            "临时邮箱系统日志轮转监控告警",
            f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]
        
        if critical_alerts:
            body_parts.extend([
                "🔴 严重告警:",
                *[f"  - {alert['message']}" for alert in critical_alerts],
                ""
            ])
        
        if warning_alerts:
            body_parts.extend([
                "🟡 警告告警:",
                *[f"  - {alert['message']}" for alert in warning_alerts],
                ""
            ])
        
        body_parts.extend([
            "建议操作:",
            "1. 检查日志轮转配置是否正常",
            "2. 手动执行日志轮转: sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh force",
            "3. 清理超期日志: sudo /var/www/tempmail/scripts/maintenance/logrotate_manager.sh cleanup",
            "4. 检查磁盘空间并清理不必要的文件"
        ])
        
        body = "\n".join(body_parts)
        
        # 发送邮件
        if self.mail_sender:
            try:
                for recipient in self.config["alerts"]["email_recipients"]:
                    self.mail_sender.send_alert_email(
                        recipient=recipient,
                        subject=subject,
                        body=body,
                        alert_type="log_rotation_alert"
                    )
                self.logger.info(f"告警邮件已发送给 {len(self.config['alerts']['email_recipients'])} 个收件人")
                
                # 更新冷却时间
                cooldown_file.touch()
                
            except Exception as e:
                self.logger.error(f"发送告警邮件失败: {e}")
        else:
            self.logger.warning("邮件发送器不可用，无法发送告警邮件")
    
    def generate_report(self) -> Dict:
        """生成监控报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "log_files": {},
            "disk_usage": {},
            "alerts": []
        }
        
        # 收集日志文件信息
        for log_file in self.log_dir.glob("*.log"):
            stat = log_file.stat()
            report["log_files"][log_file.name] = {
                "size": stat.st_size,
                "size_formatted": self._format_size(stat.st_size),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
        
        # 收集磁盘使用信息
        try:
            import shutil
            total, used, free = shutil.disk_usage(self.log_dir)
            report["disk_usage"] = {
                "total": total,
                "used": used,
                "free": free,
                "usage_percent": (used / total) * 100
            }
        except Exception as e:
            report["disk_usage"]["error"] = str(e)
        
        # 收集告警
        all_alerts = []
        all_alerts.extend(self.check_file_sizes())
        all_alerts.extend(self.check_rotation_status())
        all_alerts.extend(self.check_disk_usage())
        
        report["alerts"] = all_alerts
        
        return report
    
    def run_check(self, send_alerts: bool = True) -> Dict:
        """运行完整检查"""
        self.logger.info("开始日志轮转监控检查")
        
        report = self.generate_report()
        
        if send_alerts and report["alerts"]:
            self.send_alert(report["alerts"])
        
        # 保存报告
        report_file = self.log_dir / f"monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"监控检查完成，发现 {len(report['alerts'])} 个告警")
        return report

def main():
    parser = argparse.ArgumentParser(description="日志轮转监控脚本")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--no-alerts", action="store_true", help="不发送告警邮件")
    parser.add_argument("--report-only", action="store_true", help="仅生成报告")
    
    args = parser.parse_args()
    
    monitor = LogRotationMonitor(args.config)
    
    if args.report_only:
        report = monitor.generate_report()
        print(json.dumps(report, indent=2, ensure_ascii=False))
    else:
        report = monitor.run_check(send_alerts=not args.no_alerts)
        
        # 显示摘要
        print(f"\n监控摘要:")
        print(f"检查时间: {report['timestamp']}")
        print(f"日志文件数: {len(report['log_files'])}")
        print(f"告警数量: {len(report['alerts'])}")
        
        if report['alerts']:
            print("\n告警详情:")
            for alert in report['alerts']:
                level_icon = "🔴" if alert['level'] == 'critical' else "🟡" if alert['level'] == 'warning' else "ℹ️"
                print(f"  {level_icon} {alert['message']}")

if __name__ == "__main__":
    main()
