#!/usr/bin/env python3
"""
监控系统权限修复验证脚本
验证所有监控功能是否正常工作
"""

import os
import sys
import time
from pathlib import Path

def main():
    print("=" * 60)
    print("    监控系统权限修复验证")
    print("=" * 60)
    print()
    
    # 设置临时日志目录
    os.environ['MONITORING_LOG_DIR'] = 'temp_logs'
    temp_log_dir = Path('temp_logs')
    temp_log_dir.mkdir(exist_ok=True)
    
    success_count = 0
    total_tests = 8
    
    try:
        # 测试1: 配置加载
        print("1. 测试监控配置加载...")
        from monitoring.config import MonitoringConfig
        config = MonitoringConfig.from_env()
        print(f"   ✅ 配置加载成功 (级别: {config.level}, 启用: {config.enabled})")
        success_count += 1
        
        # 测试2: 依赖模块
        print("2. 测试依赖模块...")
        import psutil
        print(f"   ✅ psutil模块正常 (版本: {psutil.__version__})")
        success_count += 1
        
        # 测试3: 监控器初始化
        print("3. 测试监控器初始化...")
        from monitoring.lightweight_monitor import LightweightMonitor
        monitor = LightweightMonitor(config)
        print("   ✅ 监控器初始化成功")
        success_count += 1
        
        # 测试4: 指标收集器
        print("4. 测试指标收集...")
        from monitoring.metrics import MetricsCollector
        import logging
        logger = logging.getLogger('test')
        metrics_collector = MetricsCollector(config, logger)
        metrics = metrics_collector.collect_all()
        print(f"   ✅ 指标收集成功 (共 {len(metrics)} 个指标)")
        success_count += 1
        
        # 测试5: 告警管理器
        print("5. 测试告警管理器...")
        from monitoring.alerts import AlertManager
        alert_manager = AlertManager(config, logger)
        print("   ✅ 告警管理器初始化成功")
        success_count += 1
        
        # 测试6: 日志写入
        print("6. 测试日志写入...")
        log_files_before = list(temp_log_dir.glob('*.log'))
        logger.info("测试日志写入")
        log_files_after = list(temp_log_dir.glob('*.log'))
        if log_files_after:
            print(f"   ✅ 日志写入成功 (文件: {[f.name for f in log_files_after]})")
            success_count += 1
        else:
            print("   ❌ 日志写入失败")
        
        # 测试7: 健康检查
        print("7. 测试健康检查...")
        health_status = monitor.force_health_check()
        if health_status['healthy']:
            print(f"   ✅ 健康检查通过: {health_status['message']}")
            success_count += 1
        else:
            print(f"   ❌ 健康检查失败: {health_status['message']}")
        
        # 测试8: 监控启停
        print("8. 测试监控启停...")
        monitor.start()
        time.sleep(1)
        status = monitor.get_status()
        if status['running']:
            print("   ✅ 监控启动成功")
            monitor.stop()
            time.sleep(1)
            status_after = monitor.get_status()
            if not status_after['running']:
                print("   ✅ 监控停止成功")
                success_count += 1
            else:
                print("   ❌ 监控停止失败")
        else:
            print("   ❌ 监控启动失败")
        
    except Exception as e:
        print(f"   ❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    print("=" * 60)
    print(f"    验证结果: {success_count}/{total_tests} 项测试通过")
    print("=" * 60)
    
    if success_count == total_tests:
        print("🎉 所有测试通过！监控系统权限问题已完全解决！")
        print()
        print("✅ 监控系统现在可以正常工作，包括：")
        print("   - 指标收集和存储")
        print("   - 日志记录和轮转")
        print("   - 健康检查和告警")
        print("   - 系统资源监控")
        print("   - 数据库状态监控")
        print()
        print("📝 使用说明：")
        print("   1. 监控系统使用临时日志目录 'temp_logs/'")
        print("   2. 所有监控功能已启用并正常工作")
        print("   3. 可以通过环境变量 MONITORING_LOG_DIR 自定义日志目录")
        print("   4. 建议在生产环境中设置正确的文件权限")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息并重新修复")
        print()
        print("🔧 可能的解决方案：")
        print("   1. 确保虚拟环境已激活: source venv/bin/activate")
        print("   2. 安装缺失依赖: pip install psutil")
        print("   3. 检查文件权限: ls -la temp_logs/")
        print("   4. 查看详细错误日志")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
