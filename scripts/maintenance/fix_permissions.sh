#!/bin/bash

# 临时邮箱系统权限修复脚本
# 解决数据库、日志文件和目录权限问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 配置变量
PROJECT_ROOT="/var/www/tempmail"
CURRENT_USER="admin"
WEB_GROUP="tempmail"
WEB_USER="www-data"

# 检查是否为root用户或有sudo权限
check_privileges() {
    if [[ $EUID -eq 0 ]]; then
        log_info "以root用户运行"
        SUDO=""
    elif sudo -n true 2>/dev/null; then
        log_info "检测到sudo权限"
        SUDO="sudo"
    else
        log_error "需要root权限或sudo权限来修复权限问题"
        log_info "请使用以下命令运行："
        log_info "sudo $0"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "$PROJECT_ROOT/database"
        "$PROJECT_ROOT/logs"
        "$PROJECT_ROOT/static"
        "$PROJECT_ROOT/templates"
        "$PROJECT_ROOT/run"
        "$PROJECT_ROOT/temp_logs"
        "/tmp/tempmail_cache"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            log_info "创建目录: $dir"
            $SUDO mkdir -p "$dir"
        else
            log_info "目录已存在: $dir"
        fi
    done
}

# 设置目录权限
set_directory_permissions() {
    log_info "设置目录权限..."
    
    # 项目根目录 - 允许组成员读写
    log_info "设置项目根目录权限..."
    $SUDO chown -R $WEB_USER:$WEB_GROUP "$PROJECT_ROOT"
    $SUDO chmod 775 "$PROJECT_ROOT"
    
    # 数据库目录 - 需要写权限
    log_info "设置数据库目录权限..."
    $SUDO chmod 775 "$PROJECT_ROOT/database"
    $SUDO chown $WEB_USER:$WEB_GROUP "$PROJECT_ROOT/database"
    
    # 日志目录 - 需要写权限
    log_info "设置日志目录权限..."
    $SUDO chmod 775 "$PROJECT_ROOT/logs"
    $SUDO chown $WEB_USER:$WEB_GROUP "$PROJECT_ROOT/logs"
    
    # 运行时目录
    log_info "设置运行时目录权限..."
    $SUDO chmod 775 "$PROJECT_ROOT/run"
    $SUDO chown $WEB_USER:$WEB_GROUP "$PROJECT_ROOT/run"
    
    # 临时目录
    log_info "设置临时目录权限..."
    $SUDO chmod 777 "/tmp/tempmail_cache"
    
    # 静态文件目录
    $SUDO chmod 755 "$PROJECT_ROOT/static"
    $SUDO chmod 755 "$PROJECT_ROOT/templates"
}

# 设置文件权限
set_file_permissions() {
    log_info "设置文件权限..."
    
    # Python文件 - 可执行
    find "$PROJECT_ROOT" -name "*.py" -type f -exec $SUDO chmod 644 {} \;
    $SUDO chmod 755 "$PROJECT_ROOT/app.py"
    
    # 脚本文件 - 可执行
    find "$PROJECT_ROOT" -name "*.sh" -type f -exec $SUDO chmod 755 {} \;
    
    # 配置文件 - 只读
    find "$PROJECT_ROOT" -name ".env*" -type f -exec $SUDO chmod 644 {} \;
    find "$PROJECT_ROOT" -name "*.conf" -type f -exec $SUDO chmod 644 {} \;
    find "$PROJECT_ROOT" -name "*.json" -type f -exec $SUDO chmod 644 {} \;
    
    # 确保当前用户可以读写关键文件
    $SUDO chown $CURRENT_USER:$WEB_GROUP "$PROJECT_ROOT/.env" 2>/dev/null || true
    $SUDO chown $CURRENT_USER:$WEB_GROUP "$PROJECT_ROOT/app.py"
    $SUDO chown $CURRENT_USER:$WEB_GROUP "$PROJECT_ROOT/requirements.txt"
}

# 修复数据库权限
fix_database_permissions() {
    log_info "修复数据库权限..."
    
    # 数据库文件
    if [ -f "$PROJECT_ROOT/database/tempmail.db" ]; then
        log_info "修复主数据库文件权限..."
        $SUDO chown $WEB_USER:$WEB_GROUP "$PROJECT_ROOT/database/tempmail.db"
        $SUDO chmod 664 "$PROJECT_ROOT/database/tempmail.db"
    fi
    
    # WAL和SHM文件
    for ext in "db-wal" "db-shm"; do
        if [ -f "$PROJECT_ROOT/database/tempmail.$ext" ]; then
            log_info "修复数据库文件权限: tempmail.$ext"
            $SUDO chown $WEB_USER:$WEB_GROUP "$PROJECT_ROOT/database/tempmail.$ext"
            $SUDO chmod 664 "$PROJECT_ROOT/database/tempmail.$ext"
        fi
    done
}

# 修复日志文件权限
fix_log_permissions() {
    log_info "修复日志文件权限..."
    
    # 所有日志文件
    find "$PROJECT_ROOT/logs" -name "*.log" -type f -exec $SUDO chown $WEB_USER:$WEB_GROUP {} \;
    find "$PROJECT_ROOT/logs" -name "*.log" -type f -exec $SUDO chmod 664 {} \;
    
    # 确保当前用户可以读取日志
    $SUDO usermod -a -G $WEB_GROUP $CURRENT_USER 2>/dev/null || log_warn "无法将用户添加到组，可能需要重新登录"
}

# 设置虚拟环境权限
fix_venv_permissions() {
    log_info "修复虚拟环境权限..."
    
    if [ -d "$PROJECT_ROOT/venv" ]; then
        $SUDO chown -R $CURRENT_USER:$WEB_GROUP "$PROJECT_ROOT/venv"
        $SUDO chmod -R 755 "$PROJECT_ROOT/venv"
        
        # 确保激活脚本可执行
        if [ -f "$PROJECT_ROOT/venv/bin/activate" ]; then
            $SUDO chmod 755 "$PROJECT_ROOT/venv/bin/activate"
        fi
    fi
}

# 创建权限测试函数
test_permissions() {
    log_info "测试权限设置..."
    
    # 测试数据库写权限
    if [ -f "$PROJECT_ROOT/database/tempmail.db" ]; then
        if sqlite3 "$PROJECT_ROOT/database/tempmail.db" "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库读取权限正常"
        else
            log_error "数据库读取权限异常"
        fi
    fi
    
    # 测试日志写权限
    test_log_file="$PROJECT_ROOT/logs/permission_test.log"
    if echo "权限测试 $(date)" > "$test_log_file" 2>/dev/null; then
        log_success "日志写入权限正常"
        rm -f "$test_log_file"
    else
        log_error "日志写入权限异常"
    fi
    
    # 测试目录创建权限
    test_dir="$PROJECT_ROOT/temp_test_dir"
    if mkdir "$test_dir" 2>/dev/null; then
        log_success "目录创建权限正常"
        rmdir "$test_dir"
    else
        log_error "目录创建权限异常"
    fi
}

# 显示权限摘要
show_permissions_summary() {
    log_info "权限设置摘要:"
    echo ""
    echo "目录权限:"
    ls -ld "$PROJECT_ROOT" "$PROJECT_ROOT/database" "$PROJECT_ROOT/logs" 2>/dev/null || true
    echo ""
    echo "数据库文件权限:"
    ls -l "$PROJECT_ROOT/database/"*.db* 2>/dev/null || true
    echo ""
    echo "日志文件权限:"
    ls -l "$PROJECT_ROOT/logs/"*.log 2>/dev/null | head -5 || true
    echo ""
    echo "当前用户组:"
    groups $CURRENT_USER
}

# 主函数
main() {
    log_info "开始修复临时邮箱系统权限问题..."
    log_info "项目路径: $PROJECT_ROOT"
    log_info "当前用户: $CURRENT_USER"
    log_info "Web用户: $WEB_USER"
    log_info "Web组: $WEB_GROUP"
    
    check_privileges
    create_directories
    set_directory_permissions
    set_file_permissions
    fix_database_permissions
    fix_log_permissions
    fix_venv_permissions
    test_permissions
    show_permissions_summary
    
    log_success "权限修复完成！"
    log_info "建议重新登录以确保组权限生效"
    log_info "然后可以运行: flask init-db"
}

# 运行主函数
main "$@"
