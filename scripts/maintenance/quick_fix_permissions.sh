#!/bin/bash

# 快速权限修复脚本 - 针对当前具体问题
# 解决 flask init-db 权限问题

set -e

echo "🔧 开始快速修复权限问题..."

# 当前用户和路径
CURRENT_USER="admin"
PROJECT_ROOT="/var/www/tempmail"

echo "📍 当前用户: $CURRENT_USER"
echo "📍 项目路径: $PROJECT_ROOT"

# 1. 修复数据库目录和文件权限
echo ""
echo "🗄️ 修复数据库权限..."
sudo chown -R $CURRENT_USER:tempmail "$PROJECT_ROOT/database"
sudo chmod 775 "$PROJECT_ROOT/database"
sudo chmod 664 "$PROJECT_ROOT/database"/*.db* 2>/dev/null || true

# 2. 修复日志目录权限
echo ""
echo "📝 修复日志目录权限..."
sudo chown -R $CURRENT_USER:tempmail "$PROJECT_ROOT/logs"
sudo chmod 775 "$PROJECT_ROOT/logs"
sudo chmod 664 "$PROJECT_ROOT/logs"/*.log 2>/dev/null || true

# 3. 确保当前用户在tempmail组中
echo ""
echo "👥 确保用户组权限..."
sudo usermod -a -G tempmail $CURRENT_USER

# 4. 修复关键文件权限
echo ""
echo "📄 修复关键文件权限..."
sudo chown $CURRENT_USER:tempmail "$PROJECT_ROOT/app.py"
sudo chown $CURRENT_USER:tempmail "$PROJECT_ROOT/.env" 2>/dev/null || true
sudo chmod 644 "$PROJECT_ROOT/.env" 2>/dev/null || true

# 5. 创建必要的目录
echo ""
echo "📁 创建必要目录..."
sudo mkdir -p "$PROJECT_ROOT/run"
sudo mkdir -p "/tmp/tempmail_cache"
sudo chown $CURRENT_USER:tempmail "$PROJECT_ROOT/run"
sudo chmod 775 "$PROJECT_ROOT/run"
sudo chmod 777 "/tmp/tempmail_cache"

# 6. 验证权限
echo ""
echo "✅ 验证权限设置..."
echo "数据库目录权限:"
ls -ld "$PROJECT_ROOT/database"
echo "日志目录权限:"
ls -ld "$PROJECT_ROOT/logs"
echo "数据库文件权限:"
ls -l "$PROJECT_ROOT/database/"*.db* 2>/dev/null || echo "数据库文件不存在（正常，将在init-db时创建）"

echo ""
echo "🎉 权限修复完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 重新登录或运行: newgrp tempmail"
echo "2. 激活虚拟环境: source venv/bin/activate"
echo "3. 运行数据库初始化: flask init-db"
echo ""
echo "如果仍有问题，请运行: ./diagnose_permissions.sh"
