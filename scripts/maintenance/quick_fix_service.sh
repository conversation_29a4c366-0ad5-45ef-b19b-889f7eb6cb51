#!/bin/bash

# 快速修复服务配置
# 移除有问题的--capture-output参数

echo "🔧 快速修复服务配置..."

# 检查sudo权限
if ! sudo -n true 2>/dev/null; then
    echo "需要sudo权限，请输入密码："
    sudo true || exit 1
fi

# 停止服务
echo "停止服务..."
sudo systemctl stop tempmail-optimized 2>/dev/null || true

# 更新服务配置
echo "更新服务配置..."
sudo cp tempmail-optimized-simple.service /etc/systemd/system/tempmail-optimized.service

# 重新加载配置
echo "重新加载systemd配置..."
sudo systemctl daemon-reload

# 启动服务
echo "启动服务..."
if sudo systemctl start tempmail-optimized; then
    echo "✅ 服务启动成功"
    
    # 等待启动
    sleep 3
    
    # 检查状态
    if systemctl is-active --quiet tempmail-optimized; then
        echo "✅ 服务运行正常"
        
        # 测试连接
        if curl -f http://localhost:8080/ > /dev/null 2>&1; then
            echo "✅ HTTP响应正常"
            echo "🎉 修复成功！服务现在运行在 http://localhost:8080"
        else
            echo "⚠️ 服务启动但HTTP响应异常"
        fi
    else
        echo "❌ 服务启动后异常"
        sudo systemctl status tempmail-optimized --no-pager -l
    fi
else
    echo "❌ 服务启动失败"
    sudo systemctl status tempmail-optimized --no-pager -l
fi
