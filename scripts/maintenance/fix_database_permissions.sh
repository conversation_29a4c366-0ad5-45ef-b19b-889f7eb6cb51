#!/bin/bash

# 修复数据库权限问题
# 确保应用能够正常访问数据库

set -e

echo "🔧 修复数据库权限问题..."

# 检查数据库目录
DB_DIR="/var/www/tempmail/database"
DB_FILE="$DB_DIR/tempmail.db"

echo "检查数据库目录: $DB_DIR"
if [ ! -d "$DB_DIR" ]; then
    echo "创建数据库目录..."
    sudo mkdir -p "$DB_DIR"
fi

# 设置正确的所有者和权限
echo "设置数据库目录权限..."
sudo chown -R admin:tempmail "$DB_DIR"
sudo chmod -R 775 "$DB_DIR"

# 如果数据库文件存在，设置权限
if [ -f "$DB_FILE" ]; then
    echo "设置数据库文件权限..."
    sudo chown admin:tempmail "$DB_FILE"
    sudo chmod 664 "$DB_FILE"
    
    # 设置WAL和SHM文件权限（如果存在）
    for ext in "-wal" "-shm"; do
        if [ -f "${DB_FILE}${ext}" ]; then
            sudo chown admin:tempmail "${DB_FILE}${ext}"
            sudo chmod 664 "${DB_FILE}${ext}"
        fi
    done
else
    echo "数据库文件不存在，将在应用启动时创建"
fi

echo "✅ 数据库权限修复完成"

# 测试数据库连接（如果文件存在）
if [ -f "$DB_FILE" ]; then
    echo "测试数据库连接..."
    if sqlite3 "$DB_FILE" "SELECT 1;" > /dev/null 2>&1; then
        echo "✅ 数据库连接正常"
    else
        echo "❌ 数据库连接失败"
        exit 1
    fi
fi

echo "🎉 数据库权限修复完成！"
