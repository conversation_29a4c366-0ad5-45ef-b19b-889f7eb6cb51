#!/bin/bash

# systemd服务问题修复脚本
# 修复tempmail-optimized服务启动失败的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 修复systemd服务启动问题${NC}"
echo "=================================================="

# 检查是否需要sudo
check_sudo() {
    if ! sudo -n true 2>/dev/null; then
        echo -e "${YELLOW}此脚本需要sudo权限来修复服务配置${NC}"
        echo "请输入sudo密码以继续..."
        sudo true || exit 1
    fi
}

# 1. 停止可能运行的服务
echo ""
echo -e "${BLUE}1. 停止现有服务${NC}"
sudo systemctl stop tempmail-optimized 2>/dev/null || echo "   服务未运行或不存在"

# 停止手动启动的进程
pkill -f "gunicorn.*5001" 2>/dev/null || echo "   无手动启动的进程"

# 2. 备份现有配置
echo ""
echo -e "${BLUE}2. 备份现有配置${NC}"
if [ -f "/etc/systemd/system/tempmail-optimized.service" ]; then
    sudo cp /etc/systemd/system/tempmail-optimized.service /etc/systemd/system/tempmail-optimized.service.backup.$(date +%Y%m%d_%H%M%S)
    echo "   ✅ 已备份现有服务文件"
else
    echo "   ℹ️ 无现有服务文件需要备份"
fi

# 3. 选择服务配置版本
echo ""
echo -e "${BLUE}3. 选择服务配置版本${NC}"
echo "   可用的服务配置:"
echo "   1) tempmail-optimized-simple.service (推荐 - 简化版)"
echo "   2) tempmail-optimized.service (完整版)"
echo ""

# 自动选择简化版本
SERVICE_FILE="tempmail-optimized-simple.service"
echo -e "   选择: ${GREEN}简化版本 ($SERVICE_FILE)${NC}"

# 4. 安装新的服务配置
echo ""
echo -e "${BLUE}4. 安装新的服务配置${NC}"
if [ -f "/var/www/tempmail/$SERVICE_FILE" ]; then
    sudo cp "/var/www/tempmail/$SERVICE_FILE" "/etc/systemd/system/tempmail-optimized.service"
    echo "   ✅ 已安装新的服务配置"
else
    echo -e "   ${RED}❌ 服务配置文件不存在: $SERVICE_FILE${NC}"
    exit 1
fi

# 5. 重新加载systemd
echo ""
echo -e "${BLUE}5. 重新加载systemd配置${NC}"
sudo systemctl daemon-reload
echo "   ✅ systemd配置已重新加载"

# 6. 修复权限问题
echo ""
echo -e "${BLUE}6. 修复权限问题${NC}"

# 确保admin用户在tempmail组中
sudo usermod -a -G tempmail admin 2>/dev/null || echo "   用户已在组中"

# 修复关键目录权限
sudo chown -R admin:tempmail /var/www/tempmail/logs
sudo chown -R admin:tempmail /var/www/tempmail/database
sudo chmod 775 /var/www/tempmail/logs
sudo chmod 775 /var/www/tempmail/database

# 确保运行时目录存在
sudo mkdir -p /run/tempmail-optimized
sudo chown admin:tempmail /run/tempmail-optimized
sudo chmod 755 /run/tempmail-optimized

echo "   ✅ 权限问题已修复"

# 7. 验证配置
echo ""
echo -e "${BLUE}7. 验证服务配置${NC}"

# 检查服务文件语法
if sudo systemctl cat tempmail-optimized >/dev/null 2>&1; then
    echo "   ✅ 服务配置语法正确"
else
    echo -e "   ${RED}❌ 服务配置语法错误${NC}"
    exit 1
fi

# 8. 启用服务
echo ""
echo -e "${BLUE}8. 启用服务${NC}"
sudo systemctl enable tempmail-optimized
echo "   ✅ 服务已启用（开机自启动）"

# 9. 尝试启动服务
echo ""
echo -e "${BLUE}9. 启动服务${NC}"
echo "   正在启动服务..."

if sudo systemctl start tempmail-optimized; then
    echo -e "   ${GREEN}✅ 服务启动成功${NC}"
    
    # 等待服务完全启动
    sleep 5
    
    # 检查服务状态
    if systemctl is-active --quiet tempmail-optimized; then
        echo -e "   ${GREEN}✅ 服务运行正常${NC}"
        
        # 测试HTTP响应
        if curl -f http://localhost:5001/ > /dev/null 2>&1; then
            echo -e "   ${GREEN}✅ HTTP响应正常${NC}"
        else
            echo -e "   ${YELLOW}⚠️ HTTP响应异常，但服务已启动${NC}"
        fi
    else
        echo -e "   ${RED}❌ 服务启动后异常${NC}"
    fi
else
    echo -e "   ${RED}❌ 服务启动失败${NC}"
    echo ""
    echo -e "${YELLOW}查看错误信息:${NC}"
    sudo systemctl status tempmail-optimized --no-pager -l
    echo ""
    echo -e "${YELLOW}查看日志:${NC}"
    sudo journalctl -u tempmail-optimized -n 20 --no-pager
    exit 1
fi

# 10. 显示服务状态
echo ""
echo -e "${BLUE}10. 服务状态总结${NC}"
echo ""

# 显示服务状态
echo -e "${BLUE}服务状态:${NC}"
sudo systemctl status tempmail-optimized --no-pager -l | head -15

echo ""
echo -e "${BLUE}端口监听:${NC}"
netstat -tlnp 2>/dev/null | grep ":5001" || echo "端口5001未监听"

echo ""
echo -e "${BLUE}进程信息:${NC}"
ps aux | grep -E "gunicorn.*app:app" | grep -v grep || echo "无相关进程"

# 11. 提供管理命令
echo ""
echo -e "${BLUE}11. 服务管理命令${NC}"
echo ""
echo "常用管理命令:"
echo "  启动服务: sudo systemctl start tempmail-optimized"
echo "  停止服务: sudo systemctl stop tempmail-optimized"
echo "  重启服务: sudo systemctl restart tempmail-optimized"
echo "  查看状态: sudo systemctl status tempmail-optimized"
echo "  查看日志: sudo journalctl -u tempmail-optimized -f"
echo "  禁用服务: sudo systemctl disable tempmail-optimized"
echo ""
echo "测试命令:"
echo "  HTTP测试: curl -I http://localhost:5001/"
echo "  健康检查: ./manage_tempmail_service.sh health"
echo "  完整验证: python verify_optimizations.py --url http://localhost:5001"

echo ""
echo "=================================================="
echo -e "${GREEN}🎉 服务修复完成！${NC}"

# 最终验证
if systemctl is-active --quiet tempmail-optimized; then
    echo -e "${GREEN}✅ tempmail-optimized服务正在运行${NC}"
    echo -e "${GREEN}✅ 可以通过 http://localhost:5001 访问${NC}"
else
    echo -e "${RED}❌ 服务仍有问题，请检查日志${NC}"
    exit 1
fi
