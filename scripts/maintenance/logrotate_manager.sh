#!/bin/bash

# 临时邮箱系统日志轮转管理脚本
# 功能：安装、测试、监控和维护日志轮转配置

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOGROTATE_CONFIG="$PROJECT_ROOT/config/tempmail-logrotate.conf"
SYSTEM_LOGROTATE_DIR="/etc/logrotate.d"
SYSTEM_CONFIG_FILE="$SYSTEM_LOGROTATE_DIR/tempmail"
LOG_DIR="$PROJECT_ROOT/logs"
LOGROTATE_LOG="$LOG_DIR/logrotate.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - INFO: $1" >> "$LOGROTATE_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - SUCCESS: $1" >> "$LOGROTATE_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - WARNING: $1" >> "$LOGROTATE_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - ERROR: $1" >> "$LOGROTATE_LOG"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0 $*"
        exit 1
    fi
}

# 创建必要的目录和文件
setup_directories() {
    log_info "创建必要的目录和文件..."
    
    # 确保日志目录存在
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/temp_logs"
    
    # 确保日志文件存在
    touch "$LOGROTATE_LOG"
    
    # 设置正确的权限
    chown -R admin:tempmail "$LOG_DIR"
    chown -R admin:tempmail "$PROJECT_ROOT/temp_logs"
    chmod 664 "$LOGROTATE_LOG"
    
    log_success "目录和文件设置完成"
}

# 安装logrotate配置
install_config() {
    log_info "安装logrotate配置..."
    
    if [[ ! -f "$LOGROTATE_CONFIG" ]]; then
        log_error "配置文件不存在: $LOGROTATE_CONFIG"
        exit 1
    fi
    
    # 备份现有配置（如果存在）
    if [[ -f "$SYSTEM_CONFIG_FILE" ]]; then
        cp "$SYSTEM_CONFIG_FILE" "$SYSTEM_CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "已备份现有配置"
    fi
    
    # 复制新配置
    cp "$LOGROTATE_CONFIG" "$SYSTEM_CONFIG_FILE"
    chmod 644 "$SYSTEM_CONFIG_FILE"
    
    log_success "logrotate配置已安装到 $SYSTEM_CONFIG_FILE"
}

# 测试配置
test_config() {
    log_info "测试logrotate配置..."
    
    # 语法检查
    if logrotate -d "$SYSTEM_CONFIG_FILE" > /tmp/logrotate_test.log 2>&1; then
        log_success "配置语法检查通过"
    else
        log_error "配置语法检查失败"
        cat /tmp/logrotate_test.log
        exit 1
    fi
    
    # 详细测试输出
    log_info "详细测试输出："
    logrotate -d "$SYSTEM_CONFIG_FILE" | head -20
}

# 强制执行轮转（测试用）
force_rotate() {
    log_info "强制执行日志轮转（测试模式）..."
    
    if logrotate -f "$SYSTEM_CONFIG_FILE" > /tmp/logrotate_force.log 2>&1; then
        log_success "强制轮转执行成功"
        cat /tmp/logrotate_force.log
    else
        log_error "强制轮转执行失败"
        cat /tmp/logrotate_force.log
        exit 1
    fi
}

# 检查日志文件状态
check_log_status() {
    log_info "检查日志文件状态..."
    
    echo -e "\n${BLUE}=== 日志文件大小统计 ===${NC}"
    find "$LOG_DIR" -name "*.log" -type f -exec ls -lh {} \; | sort -k5 -hr
    
    echo -e "\n${BLUE}=== 磁盘使用情况 ===${NC}"
    du -sh "$LOG_DIR"
    du -sh "$PROJECT_ROOT/temp_logs" 2>/dev/null || echo "temp_logs目录不存在或为空"
    
    echo -e "\n${BLUE}=== 最近的轮转记录 ===${NC}"
    if [[ -f "$LOGROTATE_LOG" ]]; then
        tail -10 "$LOGROTATE_LOG"
    else
        echo "暂无轮转记录"
    fi
}

# 清理旧日志
cleanup_old_logs() {
    log_info "清理超期的日志文件..."
    
    local cleaned=0
    
    # 清理超过60天的压缩日志
    find "$LOG_DIR" -name "*.log.*.gz" -mtime +60 -type f | while read -r file; do
        log_info "删除超期日志: $file"
        rm -f "$file"
        ((cleaned++))
    done
    
    # 清理超过7天的临时日志
    if [[ -d "$PROJECT_ROOT/temp_logs" ]]; then
        find "$PROJECT_ROOT/temp_logs" -name "*.log*" -mtime +7 -type f | while read -r file; do
            log_info "删除超期临时日志: $file"
            rm -f "$file"
            ((cleaned++))
        done
    fi
    
    log_success "清理完成，共清理 $cleaned 个文件"
}

# 监控日志轮转状态
monitor_rotation() {
    log_info "监控日志轮转状态..."
    
    # 检查logrotate状态文件
    local status_file="/var/lib/logrotate/status"
    if [[ -f "$status_file" ]]; then
        echo -e "\n${BLUE}=== Logrotate状态 ===${NC}"
        grep "tempmail" "$status_file" || echo "未找到tempmail相关记录"
    fi
    
    # 检查大文件
    echo -e "\n${BLUE}=== 需要注意的大文件 ===${NC}"
    find "$LOG_DIR" -name "*.log" -size +50M -type f -exec ls -lh {} \; || echo "无大文件"
    
    # 检查错误
    echo -e "\n${BLUE}=== 最近的错误 ===${NC}"
    if [[ -f "$LOGROTATE_LOG" ]]; then
        grep -i "error\|failed" "$LOGROTATE_LOG" | tail -5 || echo "无错误记录"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
临时邮箱系统日志轮转管理脚本

用法: $0 [选项]

选项:
    install     安装logrotate配置到系统
    test        测试配置文件语法
    force       强制执行日志轮转（测试用）
    status      检查日志文件状态
    monitor     监控轮转状态
    cleanup     清理超期日志文件
    setup       创建必要的目录和文件
    help        显示此帮助信息

示例:
    $0 setup      # 首次设置
    $0 install    # 安装配置
    $0 test       # 测试配置
    $0 status     # 查看状态
    $0 monitor    # 监控轮转

注意: 大部分操作需要root权限
EOF
}

# 主函数
main() {
    case "${1:-help}" in
        setup)
            setup_directories
            ;;
        install)
            check_permissions
            setup_directories
            install_config
            test_config
            ;;
        test)
            check_permissions
            test_config
            ;;
        force)
            check_permissions
            force_rotate
            ;;
        status)
            check_log_status
            ;;
        monitor)
            monitor_rotation
            ;;
        cleanup)
            check_permissions
            cleanup_old_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
