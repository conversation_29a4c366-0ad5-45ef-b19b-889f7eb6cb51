# 临时邮箱 MVP 项目

这是一个基于 Python Flask（后端）、Vanilla JavaScript（前端）、SQLite（数据库）和 Postfix（邮件接收）的轻量级临时邮箱服务 MVP 实现，适合个人或小型团队自部署。

[![测试覆盖率](https://img.shields.io/badge/测试覆盖率-70%25-yellow)](./htmlcov/index.html)
[![代码质量](https://img.shields.io/badge/代码质量-生产级-green)](#项目亮点)
[![Python版本](https://img.shields.io/badge/Python-3.11+-blue)](./requirements.txt)
[![Flask版本](https://img.shields.io/badge/Flask-3.1.0-blue)](./requirements.txt)

## 目录

- [功能特性](#功能特性)
- [技术栈](#技术栈)
- [项目亮点](#项目亮点)
- [快速开始](#快速开始)
- [运行应用](#运行应用)
- [前端开发与构建](#前端开发与构建)
- [测试](#测试)
  - [测试文件结构](#测试文件结构)
  - [测试模块介绍](#测试模块介绍)
  - [测试最佳实践](#测试最佳实践)
- [环境变量配置](#环境变量配置)
- [API文档](#api文档)
- [项目文档](#项目文档)
- [常见问题](#常见问题)
- [贡献与反馈](#贡献与反馈)
- [License](#license)

---

## 功能特性

### 核心功能
- 🎲 **随机生成临时邮箱地址** - 支持自动生成安全的随机邮箱前缀
- ✨ **自定义邮箱前缀** - 用户可指定个性化的邮箱前缀（1-20字符，支持字母、数字、连字符）
- 📧 **实时邮件接收** - 自动轮询显示收到的邮件列表
- 👀 **邮件内容查看** - 支持查看邮件的纯文本和HTML内容
- 🔄 **智能刷新机制** - 页面可见时正常频率轮询，不可见时降低频率节省资源
- 📋 **一键复制** - 快速复制当前邮箱地址到剪贴板
- ⏰ **自动过期清理** - 邮箱地址和邮件内容在设定时间后自动过期并清理
- 🗑️ **删除重置功能** - 支持手动删除当前邮箱并生成新邮箱
- 🔒 **防冲突机制** - 自定义前缀冲突时自动添加随机后缀

### 用户体验
- 📱 **响应式设计** - 支持桌面和移动设备
- 🎨 **现代化UI** - 使用Tailwind CSS构建的美观界面
- 🔔 **智能通知** - 详细的错误提示与操作反馈
- 💾 **状态持久化** - 邮箱地址本地存储，刷新页面不丢失
- 🎯 **双栏布局** - 点击邮件时自动切换到双栏模式查看详情
- 🎨 **紫色主题按钮** - 自定义前缀功能采用独特的紫色渐变设计

## 技术栈

### 后端技术
- **框架**：Python 3.11+ + Flask 3.1.0
- **数据库**：SQLite 3（支持WAL模式，优化并发性能）
- **邮件处理**：Postfix + 自定义邮件处理脚本（mail_handler.py）
- **环境管理**：python-dotenv
- **WSGI服务器**：Gunicorn 23.0.0（生产环境推荐）
- **日志系统**：Python logging模块，支持文件和控制台输出
- **数据验证**：自定义验证函数，支持RFC 5321邮箱标准

### 前端技术
- **核心**：Vanilla JavaScript（无框架依赖）
- **样式**：Tailwind CSS + 自定义CSS
- **模板引擎**：Jinja2
- **API通信**：统一的APIConfig类，支持重试机制和错误处理
- **状态管理**：LocalStorage + 内存缓存
- **安全防护**：XSS防护，HTML内容转义

### 开发与测试
- **后端测试**：Pytest 8.3.5 + Coverage 7.8.0
- **前端测试**：Jest 29.7.0 + JSDOM
- **测试覆盖率**：70%（持续改进中）
- **代码质量**：符合PEP 8规范，完整的错误处理
- **API测试**：支持生产环境和开发环境的灵活测试
- **类型检查**：MyPy 1.15.0

### 部署与运维
- **容器化**：支持Docker部署
- **进程管理**：支持systemd服务
- **定时任务**：Cron定时清理过期数据（cleanup_script.py）
- **监控**：详细的日志记录和错误追踪
- **配置管理**：环境变量配置，支持多环境部署

---

## 项目亮点

### 🚀 技术特色
- **零框架前端**：使用原生JavaScript，无复杂依赖，加载速度快
- **模块化API设计**：统一的APIConfig类，支持重试机制和错误处理
- **智能轮询优化**：根据页面可见性自动调整轮询频率，节省资源
- **生产级代码质量**：完整的错误处理、输入验证和安全防护
- **函数重构优化**：复杂函数拆分为多个职责单一的函数，提高可维护性

### 🛡️ 安全特性
- **输入验证**：前后端双重验证，防止恶意输入
- **XSS防护**：HTML内容转义，防止跨站脚本攻击
- **速率限制**：API请求频率限制，防止滥用
- **安全配置**：强制HTTPS、安全的密钥管理
- **RFC 5321标准**：严格按照邮箱地址标准进行验证

### 📊 性能优化
- **数据库优化**：WAL模式、索引优化、连接池管理
- **前端缓存**：邮件列表缓存、状态持久化
- **资源管理**：智能轮询、按需加载
- **响应式设计**：移动端优化、快速响应
- **API重试机制**：网络不稳定时自动重试，提高成功率

### 🔧 开发体验
- **完整的测试覆盖**：单元测试、集成测试、端到端测试
- **详细的文档**：API文档、部署指南、故障排除
- **灵活的配置**：环境变量管理、多环境支持
- **易于扩展**：模块化设计、清晰的代码结构
- **代码质量工具**：MyPy类型检查、Coverage测试覆盖率

### ✨ 最新功能
- **自定义邮箱前缀**：支持用户指定个性化前缀，冲突时自动添加后缀
- **统一错误处理**：6个专门的错误处理器，提供一致的错误响应
- **增强的验证系统**：5个专门的验证函数，覆盖所有输入场景
- **前端安全优化**：移除未使用代码，添加XSS防护机制

---

## 快速开始

### 1. 克隆项目
```bash
git clone <your-repository-url>
cd temp-email-mvp
```

### 2. 创建并激活 Python 虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 安装前端测试依赖（可选）
npm install
```

### 4. 配置环境变量
复制环境变量示例文件并根据需要修改：
```bash
cp .env.example .env
```

**必需配置项：**
- `SECRET_KEY`：Flask 密钥（生产环境必须设置强密钥）
- `DOMAIN_NAME`：临时邮箱域名（如 `temp.example.com`）
- `DATABASE_PATH`：数据库文件路径（建议使用绝对路径）

**可选配置项：**
- `EMAIL_EXPIRATION_HOURS`：邮箱有效期（默认24小时）
- `AUTO_REFRESH_INTERVAL`：前端轮询间隔（默认3秒）
- `API_TIMEOUT`：API请求超时时间（默认10秒）
- `API_RETRY_ATTEMPTS`：API重试次数（默认3次）
- `API_RETRY_DELAY`：API重试延迟（默认1秒）
- 日志文件路径等

### 5. 初始化数据库
```bash
flask init-db
```
> 💡 应用首次启动时也会自动创建数据库表结构

### 6. 启动应用（开发模式）
```bash
# 方式1：使用 Flask 开发服务器
flask run

# 方式2：直接运行 Python 脚本
python app.py

# 方式3：指定端口
python app.py --port 8080
```

应用将在 `http://localhost:5000` 启动（或指定的端口）。

### 7. 验证安装
打开浏览器访问 `http://localhost:5000`，您应该看到：
- ✅ 临时邮箱界面正常显示
- ✅ 点击"New Email"按钮可以生成新邮箱
- ✅ 点击"Custom"按钮可以创建自定义前缀邮箱（紫色按钮）
- ✅ 邮箱地址可以正常复制
- ✅ 自定义前缀验证功能正常工作

### 8. 配置 Postfix（或其他 MTA）
- 参考开发文档中“Postfix 配置”章节。
- 确保 Postfix 能将邮件通过管道传递给 `mail_handler.py`。
- **注意**：`mail_handler.py` 的执行用户需有数据库和日志目录的读写权限，并能执行虚拟环境中的 Python。

### 9. 配置定时任务（邮件清理）
- 参考开发文档中“Cron Job 配置”章节。
- 设置定时任务定期执行 `cleanup_script.py`，以删除过期邮箱和邮件。
- **注意**：Cron 用户需有数据库和日志目录权限，并能执行虚拟环境中的 Python。

---

## 运行应用

### 开发模式（Flask 内置服务器）
```bash
flask run  # 或 python app.py
```

### 生产模式（推荐 Gunicorn）
```bash
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

---

## 前端开发与构建

### 文件结构
- **静态资源**：位于 `static/` 目录，无需构建工具，直接修改即可生效
- **主页面模板**：`templates/index.html`
- **主要JavaScript文件**：
  - `static/js/main.js` - 主要业务逻辑
  - `static/js/api-config.js` - API配置和请求管理
- **样式文件**：`static/css/style.css`
- **测试页面**：`templates/test_custom.html` - 自定义前缀功能测试页面

### API配置特性
- **统一管理**：所有API端点通过APIConfig类统一管理
- **自动重试**：网络错误时自动重试，支持递增延迟
- **超时控制**：可配置的请求超时时间
- **错误处理**：智能错误处理和用户友好的错误提示

---

## 测试

### 后端测试

#### 测试文件结构

本项目包含以下主要测试文件：

| 测试文件 | 描述 | 用途 | 覆盖率 |
|---------|------|------|-------|
| `test_polling.py` | 基本邮件轮询测试 | 验证邮件发送和接收的基本功能 | 61% |
| `test_polling_production.py` | 生产环境邮件测试 | 更健壮的API和数据库测试，适用于生产环境 | 15% |
| `test_mail_handler.py` | 邮件处理器测试 | 测试邮件接收和解析功能 | 94% |
| `test_api_consolidated.py` | API集成测试 | 测试RESTful API端点功能 | 97% |
| `test_app_consolidated.py` | 应用集成测试 | 测试Flask应用核心功能 | 95% |
| `test_custom_prefix_feature.py` | 自定义前缀功能测试 | 测试自定义邮箱前缀功能 | - |
| `test_cleanup_script.py` | 清理脚本测试 | 测试过期数据清理功能 | 99% |

#### 基本测试（Pytest）
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_polling.py -v

# 使用指定标记运行测试
pytest -m mail_handler_tests
```

#### 生产环境测试
生产环境测试通过 `test_polling_production.py` 提供，支持更灵活的配置选项：

```bash
# 基本用法
python tests/test_polling_production.py

# 指定API主机和端口
python tests/test_polling_production.py --api-host http://your-api.com --api-port 8080

# 指定数据库路径
python tests/test_polling_production.py --db /path/to/your/tempmail.db

# 跳过API检查（只测试数据库功能）
python tests/test_polling_production.py --skip-api

# 设置日志级别
python tests/test_polling_production.py --log-level DEBUG
```

#### 测试模块介绍

1. **基本邮件轮询测试 (`test_polling.py`)**:
   用于开发环境测试邮件系统的基础功能，包括：
   - 获取或生成临时邮箱
   - 插入测试邮件到数据库
   - 通过API验证邮件接收
   - 支持跳过API测试（适用于API不可用的场景）

2. **生产环境测试 (`test_polling_production.py`)**:
   针对生产环境优化的测试模块，特点包括：
   - 自动检测数据库位置
   - 支持通过环境变量和命令行参数配置
   - 更完善的错误处理和日志记录
   - 在API服务不可用时可退化为仅数据库测试
   - 验证邮件流程完整性

3. **邮件处理器测试 (`test_mail_handler.py`)**:
   针对邮件接收和处理的单元测试，测试点包括：
   - 邮件解析能力
   - 邮件保存到数据库
   - 错误处理和边缘情况
   - 使用模拟数据测试不同格式的邮件

4. **API和应用测试**:
   测试HTTP端点和应用逻辑，包括：
   - REST API功能
   - 页面路由和响应
   - 应用初始化和配置加载

生产环境测试的优势：
- 自动检测数据库位置和API配置
- 具备错误处理机制和更全面的日志输出
- 支持在API不可用时仍能验证数据库功能
- 通过环境变量或命令行参数灵活配置

### 前端测试（Jest）
```bash
npm install
npm test  # 或 yarn test

# 监视模式
npm run test:watch
```

### 测试覆盖率报告
```bash
# 生成覆盖率报告
pytest --cov=. --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html  # macOS
# 或在浏览器中打开 htmlcov/index.html
```

当前测试覆盖率：**70%**（目标：提升至95%+）

---

## 环境变量配置

本项目使用 `.env` 文件管理环境变量，支持在不同环境（开发、测试、生产）中灵活配置。

### 测试最佳实践

在使用测试模块时，以下是一些建议的最佳实践：

1. **开发环境**:
   - 使用 `pytest tests/test_polling.py -v` 进行快速验证
   - 确保API服务和数据库都在本地运行

2. **生产环境**:
   - 使用 `python tests/test_polling_production.py --skip-api` 仅测试数据库功能
   - 设置 `LOG_LEVEL=DEBUG` 获取更详细的调试信息
   - 使用绝对路径指定数据库: `--db /absolute/path/to/database.db`

3. **持续集成环境**:
   - 在CI流程中配置必要的环境变量
   - 使用 `pytest -m "not slow"` 排除耗时测试
   - 设置超时和重试机制避免网络波动导致的失败

4. **调试测试失败**:
   - 使用 `--log-level DEBUG` 查看详细日志
   - 检查数据库连接和API可用性
   - 验证环境变量是否正确加载

### 环境变量文件位置

系统按以下优先级查找并加载环境变量：
1. 测试目录下的 `.env` 文件（`tests/.env`）
2. 项目根目录下的 `.env` 文件（`.env`）
3. 自动查找（使用 `python-dotenv` 的 `find_dotenv()` 功能）

### 环境变量示例

下面是一个完整的 `.env` 文件示例：

```properties
# Flask App Configuration
FLASK_APP=app.py
FLASK_ENV=development  # 或 production
FLASK_DEBUG=1  # 生产环境设为0
SECRET_KEY=a32fc828aa39f3569186964acf5fd48dd278fd36ec008f57

# Database Configuration
DATABASE_PATH=./database/tempmail.db

# Email Configuration
DOMAIN_NAME=develop.local
EMAIL_EXPIRATION_HOURS=24

# Logging Configuration
LOG_FILE_MAIL_HANDLER=./logs/mail_handler.log
LOG_FILE_CLEANUP=./logs/cleanup.log
LOG_FILE_APP=./logs/app.log
LOG_LEVEL=INFO

# API Configuration
API_HOST=http://127.0.0.1
API_PORT=5000

# Testing Configuration
SKIP_API_CHECK=false

# Frontend Configuration (Optional)
AUTO_REFRESH_ENABLED=true
AUTO_REFRESH_INTERVAL=3000
```

### 核心环境变量

| 环境变量 | 说明 | 默认值 | 示例 |
|---------|------|-------|------|
| `FLASK_APP` | Flask应用入口 | `app.py` | `app.py` |
| `FLASK_ENV` | Flask环境模式 | `development` | `production` |
| `SECRET_KEY` | Flask密钥 | - | `a32fc828aa39f3569186964acf5fd48d` |
| `DATABASE_PATH` | 数据库完整路径 | `./database/tempmail.db` | `/var/www/tempmail/database/tempmail.db` |
| `DOMAIN_NAME` | 临时邮箱域名 | `develop.local` | `temp.example.com` |
| `EMAIL_EXPIRATION_HOURS` | 邮箱有效期（小时） | `24` | `48` |

### API配置环境变量

| 环境变量 | 说明 | 默认值 | 示例 |
|---------|------|-------|------|
| `API_BASE_URL` | API基础URL | `` | `http://api.example.com` |
| `API_TIMEOUT` | API请求超时（毫秒） | `10000` | `15000` |
| `API_RETRY_ATTEMPTS` | API重试次数 | `3` | `5` |
| `API_RETRY_DELAY` | API重试延迟（毫秒） | `1000` | `2000` |

### 测试相关环境变量

| 环境变量 | 说明 | 默认值 | 示例 |
|---------|------|-------|------|
| `API_HOST` | API 主机地址 | `http://127.0.0.1` | `http://example.com` |
| `API_PORT` | API 端口 | `5000` | `8080` |
| `SKIP_API_CHECK` | 是否跳过 API 检查 | `false` | `true` |
| `LOG_LEVEL` | 日志级别 | `INFO` | `DEBUG` |

### API 地址格式

API地址配置支持以下格式：
- 带协议的完整地址：`http://example.com:8080`
- 仅域名或IP（自动添加http://）：`example.com:8080`
- 域名与端口分离：`API_HOST=http://example.com` + `API_PORT=8080`

### 配置优先级

1. 命令行参数（测试脚本适用）
2. 环境变量（`.env` 文件或系统环境变量）
3. 默认值

---

## API文档

### 主要API端点

#### 1. 生成邮箱地址
```http
POST /api/generate-address
Content-Type: application/json

{
  "custom_prefix": "myprefix"  // 可选，1-20字符，字母数字连字符
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "address": "<EMAIL>",
    "expires_at": "2024-01-02T12:00:00Z"
  }
}
```

#### 2. 获取邮件列表
```http
GET /api/emails?address=<EMAIL>&last_received=2024-01-01T10:00:00Z
```

#### 3. 获取邮件详情
```http
GET /api/email/123?address=<EMAIL>
```

#### 4. 删除邮箱
```http
DELETE /api/delete-email
Content-Type: application/json

{
  "address": "<EMAIL>"
}
```

### API特性
- **自动重试**：网络错误时自动重试，最多3次
- **超时控制**：默认10秒超时
- **错误处理**：统一的错误响应格式
- **速率限制**：防止API滥用

---

## 项目文档

### 📚 完整文档列表

| 文档文件 | 描述 | 内容 |
|---------|------|------|
| [`docs/CUSTOM_PREFIX_FEATURE.md`](./docs/CUSTOM_PREFIX_FEATURE.md) | 自定义前缀功能文档 | 功能详细说明、API使用、测试方法 |
| [`docs/API_CONFIG_GUIDE.md`](./docs/API_CONFIG_GUIDE.md) | API配置指南 | APIConfig类使用、配置选项、最佳实践 |
| [`docs/API_IMPROVEMENT_SUMMARY.md`](./docs/API_IMPROVEMENT_SUMMARY.md) | API改进总结 | 重构历程、功能增强、技术亮点 |
| [`docs/REFACTORING_SUMMARY.md`](./docs/REFACTORING_SUMMARY.md) | 代码重构总结 | 重构目标、改进内容、质量提升 |
| [`docs/TASK_COMPLETION_SUMMARY.md`](./docs/TASK_COMPLETION_SUMMARY.md) | 任务完成总结 | 功能实现、测试验证、部署状态 |

### 🔧 技术文档亮点

#### 代码重构成果
- **复杂函数拆分**：将复杂的`generate_address`函数拆分为3个职责明确的函数
- **输入验证增强**：添加了5个专门的验证函数，覆盖所有输入场景
- **错误处理统一**：实现了6个统一的错误处理器
- **前端安全优化**：移除未使用代码，添加XSS防护

#### API配置系统
- **统一管理**：APIConfig类管理所有API端点
- **智能重试**：递增延迟重试机制
- **配置热加载**：支持运行时配置更新
- **环境适配**：支持开发、测试、生产环境

#### 自定义前缀功能
- **用户友好**：紫色主题按钮，直观的用户界面
- **冲突处理**：自动添加随机后缀解决前缀冲突
- **完整测试**：包含单元测试、集成测试、手动测试
- **向后兼容**：不影响现有功能

---

## 常见问题

### 🔧 安装和配置问题

**Q: 数据库/日志权限问题**
- **解决方案**：确保所有相关脚本的执行用户有数据库和日志目录的读写权限
- **检查命令**：`ls -la database/ logs/`

**Q: 环境变量未生效**
- **解决方案**：确认 `.env` 路径正确，且已激活虚拟环境
- **检查命令**：`python -c "import os; print(os.getenv('DOMAIN_NAME'))"`

**Q: 端口冲突**
- **解决方案**：如 5000/8000 被占用，可修改启动命令的端口参数
- **示例**：`python app.py --port 8080`
- **生产环境**：修改 `gunicorn.conf.py` 中的 `bind` 参数

### 📧 邮件相关问题

**Q: 邮件收不到**
- **解决方案**：检查 Postfix 配置和 `mail_handler.py` 的可执行权限
- **调试步骤**：
  1. 检查 `mail_handler.py` 权限：`chmod +x mail_handler.py`
  2. 查看邮件处理日志：`tail -f logs/mail_handler.log`
  3. 测试邮件处理：`echo "test" | python mail_handler.py <EMAIL>`

**Q: 自定义前缀不工作**
- **解决方案**：确保前缀符合规则（1-20字符，字母数字连字符）
- **测试方法**：访问 `/test-custom` 页面进行功能测试

### 🧪 测试相关问题

**Q: 测试失败**
- **解决方案**：
  1. 确保测试数据库权限正确
  2. 检查环境变量配置
  3. 使用 `pytest -v` 查看详细错误信息

**Q: API测试超时**
- **解决方案**：
  1. 增加 `API_TIMEOUT` 环境变量值
  2. 检查网络连接
  3. 使用 `--skip-api` 参数跳过API测试

### 🚀 性能问题

**Q: 页面加载慢**
- **解决方案**：
  1. 检查轮询间隔设置（`AUTO_REFRESH_INTERVAL`）
  2. 确保数据库使用WAL模式
  3. 清理过期数据：`python cleanup_script.py`

**Q: API请求失败**
- **解决方案**：
  1. 检查API重试配置（`API_RETRY_ATTEMPTS`）
  2. 查看浏览器控制台错误信息
  3. 检查服务器日志：`tail -f logs/app.log`

---

## 贡献与反馈

### 🤝 如何贡献

我们欢迎各种形式的贡献！

**代码贡献：**
1. Fork 本项目
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 提交 Pull Request

**问题报告：**
- 使用 GitHub Issues 报告 bug
- 提供详细的错误信息和复现步骤
- 包含环境信息（Python版本、操作系统等）

**功能建议：**
- 通过 Issues 提出新功能建议
- 详细描述功能需求和使用场景
- 欢迎提供设计思路和实现方案

### 📊 项目状态

- **开发状态**：活跃开发中
- **测试覆盖率**：70%（目标95%+）
- **代码质量**：生产级
- **文档完整性**：完善

### 🎯 后续计划

**高优先级：**
- [ ] 提升测试覆盖率至95%+
- [ ] 添加WebSocket支持替代轮询
- [ ] 实现Redis缓存支持
- [ ] 添加Docker容器化部署

**中优先级：**
- [ ] 添加邮件搜索功能
- [ ] 实现邮件标签分类
- [ ] 添加邮件导出功能
- [ ] 支持多语言界面

**低优先级：**
- [ ] 添加邮件附件支持
- [ ] 实现邮件转发功能
- [ ] 添加API访问统计
- [ ] 支持自定义域名

### 📞 联系方式

- **GitHub Issues**：[项目Issues页面](https://github.com/skywalk128/tempmail/issues)
- **项目主页**：[GitHub Repository](https://github.com/skywalk128/tempmail)

---

## License

MIT License - 详见 [LICENSE](./LICENSE) 文件

---

## 致谢

感谢所有为这个项目做出贡献的开发者和用户！

**特别感谢：**
- Flask 框架团队
- Tailwind CSS 团队
- 所有测试用户和反馈者

**技术栈致谢：**
- Python & Flask - 强大的后端框架
- SQLite - 轻量级数据库解决方案
- Vanilla JavaScript - 简洁高效的前端实现
- Pytest - 优秀的测试框架