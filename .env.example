# Flask App Configuration
FLASK_APP=app.py
# or production， development
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=a32fc828aa39f3569186964acf5fd48dd278fd36ec008f57

# Database Configuration
# 数据库类型: sqlite 或 mysql
DATABASE_TYPE=mysql

# SQLite配置 (当DATABASE_TYPE=sqlite时使用)
# 使用相对路径时，它是相对于项目根目录的 (如果从项目根目录运行Flask应用)
# 对于 mail_handler.py 和 cleanup_script.py，它们会尝试从脚本所在目录加载 .env，
# 所以DATABASE_PATH最好是绝对路径，或者脚本能正确找到项目根目录下的相对路径。
# 为简单起见，这里用相对路径，假设脚本和应用都从项目根目录执行或能正确解析。
# 生产环境强烈建议使用绝对路径。
DATABASE_PATH=/var/www/tempmail/database/tempmail.db

# MySQL配置 (当DATABASE_TYPE=mysql时使用)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=tempmail
MYSQL_USERNAME=tempmail_user
MYSQL_PASSWORD=your_secure_password_here
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_unicode_ci

# MySQL连接池配置
MYSQL_POOL_SIZE=10
MYSQL_MAX_OVERFLOW=20
MYSQL_POOL_TIMEOUT=30
MYSQL_POOL_RECYCLE=3600

# MySQL SSL配置 (可选)
MYSQL_SSL_DISABLED=false
MYSQL_SSL_CA=
MYSQL_SSL_CERT=
MYSQL_SSL_KEY=

# MySQL调试配置
MYSQL_ECHO_SQL=false

# Email Configuration
DOMAIN_NAME=kuroneko.lol
# 邮箱地址默认有效期（小时）
EMAIL_EXPIRATION_HOURS=24

# Logging (Optional, 脚本会自动创建logs目录)
LOG_FILE_MAIL_HANDLER=/var/www/tempmail/logs/mail_handler.log
LOG_FILE_CLEANUP=/var/www/tempmail/logs/cleanup.log
LOG_FILE_APP=/var/www/tempmail/logs/app.log

# 自动刷新配置（3秒轮询）
AUTO_REFRESH_ENABLED=true
AUTO_REFRESH_INTERVAL=3000

# API 配置
API_HOST=https://api.kuroneko.lol
API_PORT=443

# 前端API配置
API_BASE_URL=
API_TIMEOUT=10000
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000

# 测试配置
SKIP_API_CHECK=true

# 日志配置
LOG_LEVEL=INFO

# 临时邮箱系统监控配置
# ==================== 生产环境推荐配置 ====================
# 生产环境建议配置（使用内部邮件）：
MONITORING_LEVEL=basic
MONITORING_METRICS_INTERVAL=60
MONITORING_HEALTH_INTERVAL=300
MONITORING_MAX_MEMORY_MB=20
MONITORING_CPU_THRESHOLD=80.0
MONITORING_MEMORY_THRESHOLD=85.0
MONITORING_EMAIL_ENABLED=true
MONITORING_USE_INTERNAL_MAIL=true
MONITORING_ADMIN_EMAIL_PREFIX=monitoring-admin
MONITORING_ALERT_EMAIL_TO=<EMAIL>