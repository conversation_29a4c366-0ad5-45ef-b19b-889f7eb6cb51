[pytest]
# 忽略backup相关的文件和目录
norecursedirs = tests_backup backup .git .tox dist build *.egg
testpaths = tests .
# 忽略backup相关的文件模式 (使用--ignore-glob选项)
addopts =
    --ignore-glob=tests_backup/*
    --ignore-glob=*backup*
    --ignore-glob=*.bak
    --ignore-glob=*_backup.py
    --ignore-glob=*_backup.js
    --ignore-glob=backup/*

markers =
    mail_handler_tests: mark tests for mail_handler.py
    mail_handler_additional: mark additional tests for mail_handler.py
    mail_handler_edge_cases: mark edge case tests for mail_handler.py
    mail_handler_coverage: mark coverage tests for mail_handler.py
    mail_handler_final: mark final coverage tests for mail_handler.py
    mail_handler_remaining: mark remaining coverage tests for mail_handler.py
