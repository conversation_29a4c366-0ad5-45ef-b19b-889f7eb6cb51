# 生产环境配置文件
# 应用级优化配置

# ==================== Flask 基础配置 ====================
FLASK_APP=app.py
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your_production_secret_key_here_32_chars_minimum

# ==================== 数据库配置 ====================
DATABASE_PATH=/var/www/tempmail/database/tempmail.db

# SQLite 性能优化参数
SQLITE_TIMEOUT=60
SQLITE_CACHE_SIZE=8000
SQLITE_PAGE_SIZE=4096
SQLITE_AUTO_VACUUM=INCREMENTAL

# ==================== 缓存配置 ====================
# 缓存类型: simple, filesystem, redis, memcached
FLASK_CACHE_TYPE=simple
FLASK_CACHE_DEFAULT_TIMEOUT=300
FLASK_CACHE_THRESHOLD=500

# Redis 缓存配置（如果使用 redis）
# CACHE_REDIS_URL=redis://localhost:6379/0

# 文件系统缓存配置（如果使用 filesystem）
# CACHE_DIR=/tmp/flask_cache

# ==================== 应用配置 ====================
DOMAIN_NAME=yourdomain.com
EMAIL_EXPIRATION_HOURS=24

# API 配置
API_TIMEOUT=10000
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000

# ==================== 日志配置 ====================
LOG_FILE_APP=/var/www/tempmail/logs/app.log
LOG_FILE_MAIL_HANDLER=/var/www/tempmail/logs/mail_handler.log
LOG_FILE_CLEANUP=/var/www/tempmail/logs/cleanup.log
FLASK_LOG_LEVEL=INFO

# ==================== 监控配置 ====================
MONITORING_ENABLED=true
MONITORING_LEVEL=basic
MONITORING_METRICS_INTERVAL=60
MONITORING_HEALTH_INTERVAL=300
MONITORING_MAX_MEMORY_MB=20
MONITORING_CPU_THRESHOLD=80.0
MONITORING_MEMORY_THRESHOLD=85.0
MONITORING_EMAIL_ENABLED=true
MONITORING_USE_INTERNAL_MAIL=true
MONITORING_ALERT_EMAIL_TO=<EMAIL>

# ==================== 性能优化配置 ====================
# 禁用开发模式功能
AUTO_REFRESH_ENABLED=false
AUTO_REFRESH_INTERVAL=30000

# 生产环境安全设置
DISABLE_RATE_LIMIT=false
