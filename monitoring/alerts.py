"""
轻量级告警管理器

提供基础的告警检查和通知功能
"""

import time
import smtplib
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Set, Optional, List
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from collections import defaultdict


class AlertManager:
    """轻量级告警管理器"""

    def __init__(self, config, logger, app=None):
        self.config = config
        self.logger = logger
        self.app = app
        
        # 告警状态跟踪
        self.active_alerts: Set[str] = set()
        self.alert_history = []
        self.last_notification_time = defaultdict(float)
        
        # 告警抑制时间（秒）
        self.suppression_time = {
            'warning': 300,   # 5分钟
            'critical': 600,  # 10分钟
            'urgent': 1800    # 30分钟
        }

        # 初始化内部邮件发送器（如果启用）
        self.internal_mail_sender = None
        if config.email_notifications and config.use_internal_mail:
            try:
                from .internal_mail_sender import InternalMailSender
                self.internal_mail_sender = InternalMailSender(config, logger, app)
                self.logger.info("内部邮件发送器已初始化")
            except Exception as e:
                self.logger.error(f"初始化内部邮件发送器失败: {e}")

        self.logger.info("告警管理器初始化完成")
    
    def check_alerts(self, metrics: Dict[str, Any]):
        """检查指标并触发告警"""
        try:
            current_time = time.time()
            
            # 检查系统指标告警
            self._check_system_alerts(metrics, current_time)
            
            # 检查应用指标告警
            self._check_application_alerts(metrics, current_time)
            
            # 检查数据库指标告警
            self._check_database_alerts(metrics, current_time)
            
        except Exception as e:
            self.logger.error(f"告警检查异常: {e}", exc_info=True)
    
    def _check_system_alerts(self, metrics: Dict[str, Any], current_time: float):
        """检查系统指标告警"""
        if 'system' not in metrics:
            return
        
        system_metrics = metrics['system']
        
        # CPU使用率告警
        if 'cpu_percent' in system_metrics:
            cpu_percent = system_metrics['cpu_percent']
            if cpu_percent > self.config.cpu_threshold:
                self._trigger_alert(
                    'high_cpu_usage',
                    f'CPU使用率过高: {cpu_percent:.1f}%',
                    'warning' if cpu_percent < 90 else 'critical',
                    {'cpu_percent': cpu_percent, 'threshold': self.config.cpu_threshold},
                    current_time
                )
            else:
                self._resolve_alert('high_cpu_usage', current_time)
        
        # 内存使用率告警
        if 'memory' in system_metrics and 'percent' in system_metrics['memory']:
            memory_percent = system_metrics['memory']['percent']
            if memory_percent > self.config.memory_threshold:
                self._trigger_alert(
                    'high_memory_usage',
                    f'内存使用率过高: {memory_percent:.1f}%',
                    'warning' if memory_percent < 95 else 'critical',
                    {'memory_percent': memory_percent, 'threshold': self.config.memory_threshold},
                    current_time
                )
            else:
                self._resolve_alert('high_memory_usage', current_time)
        
        # 磁盘使用率告警
        if 'disk' in system_metrics and 'percent' in system_metrics['disk']:
            disk_percent = system_metrics['disk']['percent']
            if disk_percent > self.config.disk_threshold:
                self._trigger_alert(
                    'high_disk_usage',
                    f'磁盘使用率过高: {disk_percent:.1f}%',
                    'warning' if disk_percent < 95 else 'critical',
                    {'disk_percent': disk_percent, 'threshold': self.config.disk_threshold},
                    current_time
                )
            else:
                self._resolve_alert('high_disk_usage', current_time)
    
    def _check_application_alerts(self, metrics: Dict[str, Any], current_time: float):
        """检查应用指标告警"""
        if 'application' not in metrics:
            return
        
        app_metrics = metrics['application']
        
        # 应用内存使用告警
        if 'process' in app_metrics and 'memory_mb' in app_metrics['process']:
            memory_mb = app_metrics['process']['memory_mb']
            # 使用更合理的阈值：配置值的3倍，最小100MB
            memory_threshold = max(self.config.max_memory_mb * 3, 100)
            if memory_mb > memory_threshold:
                self._trigger_alert(
                    'high_app_memory',
                    f'应用内存使用过高: {memory_mb:.1f}MB (阈值: {memory_threshold}MB)',
                    'warning',
                    {'memory_mb': memory_mb, 'threshold_mb': memory_threshold, 'config_limit_mb': self.config.max_memory_mb},
                    current_time
                )
            else:
                self._resolve_alert('high_app_memory', current_time)
        
        # 文件描述符告警
        if 'process' in app_metrics and 'num_fds' in app_metrics['process']:
            num_fds = app_metrics['process']['num_fds']
            if num_fds > 1000:  # 文件描述符过多
                self._trigger_alert(
                    'high_file_descriptors',
                    f'文件描述符数量过多: {num_fds}',
                    'warning',
                    {'num_fds': num_fds},
                    current_time
                )
            else:
                self._resolve_alert('high_file_descriptors', current_time)
    
    def _check_database_alerts(self, metrics: Dict[str, Any], current_time: float):
        """检查数据库指标告警"""
        if 'database' not in metrics:
            return
        
        db_metrics = metrics['database']
        
        # 数据库连接告警
        if not db_metrics.get('connection_test', False):
            self._trigger_alert(
                'database_connection_failed',
                '数据库连接失败',
                'critical',
                db_metrics,
                current_time
            )
        else:
            self._resolve_alert('database_connection_failed', current_time)
        
        # 数据库文件大小告警
        if 'file_size_mb' in db_metrics:
            file_size_mb = db_metrics['file_size_mb']
            if file_size_mb > 1000:  # 数据库文件超过1GB
                self._trigger_alert(
                    'large_database_file',
                    f'数据库文件过大: {file_size_mb:.1f}MB',
                    'warning',
                    {'file_size_mb': file_size_mb},
                    current_time
                )
            else:
                self._resolve_alert('large_database_file', current_time)
    
    def _trigger_alert(self, alert_id: str, message: str, severity: str, 
                      details: Dict[str, Any], current_time: float):
        """触发告警"""
        # 检查是否在抑制期内
        last_notification = self.last_notification_time.get(alert_id, 0)
        suppression_period = self.suppression_time.get(severity, 300)
        
        if current_time - last_notification < suppression_period:
            return  # 在抑制期内，不发送通知
        
        # 记录告警
        alert_data = {
            'id': alert_id,
            'message': message,
            'severity': severity,
            'details': details,
            'timestamp': datetime.now().isoformat(),
            'status': 'active'
        }
        
        self.active_alerts.add(alert_id)
        self.alert_history.append(alert_data)
        
        # 限制历史记录大小
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-500:]
        
        # 发送通知
        if self.config.is_notification_enabled():
            self._send_notification(alert_data)
            self.last_notification_time[alert_id] = current_time
        
        self.logger.warning(f"告警触发: {alert_id} - {message}")
    
    def _resolve_alert(self, alert_id: str, current_time: float):
        """解决告警"""
        if alert_id in self.active_alerts:
            self.active_alerts.remove(alert_id)
            
            # 记录解决
            resolve_data = {
                'id': alert_id,
                'message': f'告警已解决: {alert_id}',
                'severity': 'info',
                'timestamp': datetime.now().isoformat(),
                'status': 'resolved'
            }
            
            self.alert_history.append(resolve_data)
            self.logger.info(f"告警解决: {alert_id}")
    
    def _send_notification(self, alert_data: Dict[str, Any]):
        """发送通知"""
        try:
            # 发送邮件通知
            if self.config.email_notifications:
                self._send_email_notification(alert_data)
            
            # 发送Webhook通知
            if self.config.webhook_notifications:
                self._send_webhook_notification(alert_data)
                
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}", exc_info=True)
    
    def _send_email_notification(self, alert_data: Dict[str, Any]):
        """发送邮件通知"""
        if not self.config.alert_email_to:
            self.logger.warning("未配置收件人邮箱，跳过邮件通知")
            return

        # 使用内部邮件系统
        if self.internal_mail_sender:
            self._send_internal_email(alert_data)
        else:
            self.logger.error("内部邮件发送器未初始化，无法发送邮件通知")

    def _send_internal_email(self, alert_data: Dict[str, Any]):
        """通过内部邮件系统发送告警"""
        try:
            success = self.internal_mail_sender.send_alert_email(
                alert_data,
                self.config.alert_email_to
            )

            if success:
                self.logger.info(f"内部邮件告警已发送: {alert_data['id']}")
            else:
                self.logger.error(f"内部邮件告警发送失败: {alert_data['id']}")

        except Exception as e:
            self.logger.error(f"发送内部邮件告警时发生异常: {e}", exc_info=True)

    def _send_external_email(self, alert_data: Dict[str, Any]):
        """通过外部SMTP发送告警"""
        if not all([self.config.smtp_host, self.config.alert_email_from]):
            self.logger.warning("外部SMTP配置不完整，跳过邮件通知")
            return

        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.config.alert_email_from
            msg['To'] = self.config.alert_email_to
            msg['Subject'] = f"[临时邮箱监控] {alert_data['severity'].upper()}: {alert_data['id']}"
            
            # 邮件正文
            body = f"""
监控告警通知

告警ID: {alert_data['id']}
严重程度: {alert_data['severity']}
时间: {alert_data['timestamp']}
消息: {alert_data['message']}

详细信息:
{self._format_details(alert_data['details'])}

---
临时邮箱系统监控
            """.strip()
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.config.smtp_host, self.config.smtp_port) as server:
                if self.config.smtp_username and self.config.smtp_password:
                    server.starttls()
                    server.login(self.config.smtp_username, self.config.smtp_password)
                
                server.send_message(msg)
            
            self.logger.info(f"邮件通知已发送: {alert_data['id']}")
            
        except Exception as e:
            self.logger.error(f"发送邮件通知失败: {e}")
    
    def _send_webhook_notification(self, alert_data: Dict[str, Any]):
        """发送Webhook通知"""
        if not self.config.webhook_url:
            return
        
        try:
            payload = {
                'alert_id': alert_data['id'],
                'severity': alert_data['severity'],
                'message': alert_data['message'],
                'timestamp': alert_data['timestamp'],
                'details': alert_data['details'],
                'source': 'temp-email-monitoring'
            }
            
            response = requests.post(
                self.config.webhook_url,
                json=payload,
                timeout=self.config.webhook_timeout
            )
            
            if response.status_code == 200:
                self.logger.info(f"Webhook通知已发送: {alert_data['id']}")
            else:
                self.logger.warning(f"Webhook通知失败，状态码: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"发送Webhook通知失败: {e}")
    
    def _format_details(self, details: Dict[str, Any]) -> str:
        """格式化详细信息"""
        if not details:
            return "无"
        
        lines = []
        for key, value in details.items():
            lines.append(f"  {key}: {value}")
        
        return "\n".join(lines)
    
    def send_alert(self, alert_id: str, message: str, severity: str, details: Dict[str, Any] = None):
        """手动发送告警"""
        current_time = time.time()
        self._trigger_alert(alert_id, message, severity, details or {}, current_time)
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return [alert for alert in self.alert_history if alert.get('status') == 'active']
    
    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_alerts = []
        for alert in self.alert_history:
            try:
                alert_time = datetime.fromisoformat(alert['timestamp'])
                if alert_time >= cutoff_time:
                    recent_alerts.append(alert)
            except (KeyError, ValueError):
                continue
        
        return recent_alerts
    
    def clear_alert(self, alert_id: str):
        """手动清除告警"""
        if alert_id in self.active_alerts:
            self._resolve_alert(alert_id, time.time())
