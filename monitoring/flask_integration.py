"""
Flask应用监控集成模块

提供Flask应用的监控集成功能
"""

import time
import functools
from flask import request, g, jsonify
from typing import Dict, Any, Optional
from .lightweight_monitor import LightweightMonitor
from .config import MonitoringConfig


class FlaskMonitoringIntegration:
    """Flask监控集成类"""
    
    def __init__(self, app=None, config: Optional[MonitoringConfig] = None):
        self.app = app
        self.config = config or MonitoringConfig.from_env()
        self.monitor = None
        
        # 请求统计
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用监控"""
        self.app = app
        
        if not self.config.enabled:
            app.logger.info("监控已禁用")
            return
        
        # 初始化监控器
        self.monitor = LightweightMonitor(self.config, app)
        
        # 注册请求钩子
        app.before_request(self._before_request)
        app.after_request(self._after_request)
        app.teardown_appcontext(self._teardown_request)
        
        # 注册监控路由
        self._register_monitoring_routes(app)
        
        # 启动监控
        self.monitor.start()
        
        app.logger.info("Flask监控集成已启用")
    
    def _before_request(self):
        """请求开始前的处理"""
        g.start_time = time.time()
        self.request_count += 1
    
    def _after_request(self, response):
        """请求结束后的处理"""
        if hasattr(g, 'start_time'):
            response_time = time.time() - g.start_time
            self.total_response_time += response_time
            
            # 记录慢请求
            if response_time > self.config.response_time_threshold:
                if self.monitor:
                    self.monitor.alert_manager.send_alert(
                        'slow_request',
                        f'慢请求检测: {request.endpoint} 耗时 {response_time:.2f}秒',
                        'warning',
                        {
                            'endpoint': request.endpoint,
                            'method': request.method,
                            'response_time': response_time,
                            'threshold': self.config.response_time_threshold
                        }
                    )
            
            # 记录错误响应
            if response.status_code >= 400:
                self.error_count += 1
                
                # 计算错误率
                error_rate = (self.error_count / self.request_count) * 100
                if error_rate > self.config.error_rate_threshold:
                    if self.monitor:
                        self.monitor.alert_manager.send_alert(
                            'high_error_rate',
                            f'错误率过高: {error_rate:.1f}%',
                            'warning',
                            {
                                'error_rate': error_rate,
                                'error_count': self.error_count,
                                'total_requests': self.request_count,
                                'threshold': self.config.error_rate_threshold
                            }
                        )
        
        return response
    
    def _teardown_request(self, exception):
        """请求清理"""
        if exception:
            self.error_count += 1
            
            # 记录异常
            if self.monitor:
                self.monitor.logger.error(f"请求异常: {exception}", exc_info=True)
    
    def _register_monitoring_routes(self, app):
        """注册监控相关路由"""
        
        @app.route('/api/monitoring/status')
        def monitoring_status():
            """获取监控状态"""
            if not self.monitor:
                return jsonify({'error': '监控未启用'}), 503
            
            status = self.monitor.get_status()
            
            # 添加请求统计
            status['requests'] = {
                'total_count': self.request_count,
                'error_count': self.error_count,
                'error_rate': (self.error_count / max(self.request_count, 1)) * 100,
                'avg_response_time': self.total_response_time / max(self.request_count, 1)
            }
            
            return jsonify(status)
        
        @app.route('/api/monitoring/metrics')
        def monitoring_metrics():
            """获取当前指标"""
            if not self.monitor:
                return jsonify({'error': '监控未启用'}), 503
            
            metrics = self.monitor.get_current_metrics()
            return jsonify(metrics)
        
        @app.route('/api/monitoring/metrics/summary')
        def monitoring_metrics_summary():
            """获取指标摘要"""
            if not self.monitor:
                return jsonify({'error': '监控未启用'}), 503
            
            minutes = request.args.get('minutes', 10, type=int)
            summary = self.monitor.metrics_collector.get_metrics_summary(minutes)
            return jsonify(summary)
        
        @app.route('/api/monitoring/alerts')
        def monitoring_alerts():
            """获取告警信息"""
            if not self.monitor:
                return jsonify({'error': '监控未启用'}), 503
            
            hours = request.args.get('hours', 24, type=int)
            
            return jsonify({
                'active_alerts': self.monitor.alert_manager.get_active_alerts(),
                'alert_history': self.monitor.alert_manager.get_alert_history(hours)
            })
        
        @app.route('/api/monitoring/health')
        def monitoring_health():
            """执行健康检查"""
            if not self.monitor:
                return jsonify({'error': '监控未启用'}), 503
            
            health_status = self.monitor.force_health_check()
            
            if health_status['healthy']:
                return jsonify(health_status)
            else:
                return jsonify(health_status), 503
        
        @app.route('/api/monitoring/alerts/<alert_id>/clear', methods=['POST'])
        def clear_alert(alert_id):
            """清除指定告警"""
            if not self.monitor:
                return jsonify({'error': '监控未启用'}), 503
            
            self.monitor.alert_manager.clear_alert(alert_id)
            return jsonify({'message': f'告警 {alert_id} 已清除'})
    
    def get_request_stats(self) -> Dict[str, Any]:
        """获取请求统计"""
        return {
            'total_requests': self.request_count,
            'error_count': self.error_count,
            'error_rate': (self.error_count / max(self.request_count, 1)) * 100,
            'avg_response_time': self.total_response_time / max(self.request_count, 1)
        }
    
    def reset_stats(self):
        """重置统计数据"""
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
    
    def shutdown(self):
        """关闭监控"""
        if self.monitor:
            self.monitor.stop()


def monitor_endpoint(severity='warning'):
    """装饰器：监控特定端点"""
    def decorator(f):
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = f(*args, **kwargs)
                return result
            except Exception as e:
                # 记录端点异常
                from flask import current_app
                if hasattr(current_app, 'monitoring'):
                    current_app.monitoring.monitor.alert_manager.send_alert(
                        f'endpoint_error_{f.__name__}',
                        f'端点 {f.__name__} 发生异常: {str(e)}',
                        severity,
                        {
                            'endpoint': f.__name__,
                            'exception': str(e),
                            'args': str(args),
                            'kwargs': str(kwargs)
                        }
                    )
                raise
            finally:
                # 记录执行时间
                execution_time = time.time() - start_time
                if execution_time > 5.0:  # 超过5秒的慢端点
                    from flask import current_app
                    if hasattr(current_app, 'monitoring'):
                        current_app.monitoring.monitor.logger.warning(
                            f'慢端点: {f.__name__} 耗时 {execution_time:.2f}秒'
                        )
        
        return decorated_function
    return decorator


# 便捷函数
def init_monitoring(app, config: Optional[MonitoringConfig] = None) -> FlaskMonitoringIntegration:
    """初始化Flask应用监控的便捷函数"""
    monitoring = FlaskMonitoringIntegration(app, config)
    app.monitoring = monitoring  # 将监控实例附加到app对象
    return monitoring
