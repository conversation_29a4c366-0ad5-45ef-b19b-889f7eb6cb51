"""
轻量级指标收集器

收集系统和应用指标，内存占用最小化
"""

import os
import time
import sqlite3
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import deque
from pathlib import Path


class MetricsCollector:
    """轻量级指标收集器"""
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # 使用deque存储最近的指标，自动限制内存使用
        # 计算最大指标数量，避免除零错误
        interval_minutes = max(1, self.config.metrics_interval // 60)
        max_metrics = min(1000, self.config.metrics_retention_hours * 60 // interval_minutes)
        max_metrics = max(10, max_metrics)  # 至少保留10个指标点
        self.metrics_history = deque(maxlen=max_metrics)
        
        # 缓存进程对象
        self._process = psutil.Process()
        
        # 上次收集的数据（用于计算差值）
        self._last_cpu_times = None
        self._last_disk_io = None
        self._last_network_io = None
        
        self.logger.info(f"指标收集器初始化完成，最大保留 {max_metrics} 个指标点")
    
    def collect_all(self) -> Dict[str, Any]:
        """收集所有指标"""
        timestamp = datetime.now()
        
        metrics = {
            'timestamp': timestamp.isoformat(),
            'system': self._collect_system_metrics(),
            'application': self._collect_application_metrics(),
            'database': self._collect_database_metrics()
        }
        
        # 根据监控级别决定收集详细程度
        if self.config.level in ['standard', 'detailed']:
            metrics['detailed'] = self._collect_detailed_metrics()
        
        # 存储到历史记录
        self.metrics_history.append(metrics)
        
        return metrics
    
    def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # 内存使用
            memory = psutil.virtual_memory()
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory': {
                    'total_mb': memory.total / 1024 / 1024,
                    'used_mb': memory.used / 1024 / 1024,
                    'available_mb': memory.available / 1024 / 1024,
                    'percent': memory.percent
                },
                'disk': {
                    'total_gb': disk.total / 1024 / 1024 / 1024,
                    'used_gb': disk.used / 1024 / 1024 / 1024,
                    'free_gb': disk.free / 1024 / 1024 / 1024,
                    'percent': (disk.used / disk.total) * 100
                }
            }
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {'error': str(e)}
    
    def _collect_application_metrics(self) -> Dict[str, Any]:
        """收集应用指标"""
        try:
            # 当前进程的资源使用
            memory_info = self._process.memory_info()
            cpu_percent = self._process.cpu_percent()
            
            # 文件描述符数量
            try:
                num_fds = self._process.num_fds()
            except (AttributeError, psutil.AccessDenied):
                num_fds = 0
            
            # 线程数量
            try:
                num_threads = self._process.num_threads()
            except psutil.AccessDenied:
                num_threads = 0
            
            return {
                'process': {
                    'memory_mb': memory_info.rss / 1024 / 1024,
                    'cpu_percent': cpu_percent,
                    'num_fds': num_fds,
                    'num_threads': num_threads
                },
                'uptime_seconds': time.time() - self._process.create_time()
            }
        except Exception as e:
            self.logger.error(f"收集应用指标失败: {e}")
            return {'error': str(e)}
    
    def _collect_database_metrics(self) -> Dict[str, Any]:
        """收集数据库指标"""
        try:
            # 获取数据库路径
            db_path = os.getenv('DATABASE_PATH', 'database/tempmail.db')
            
            metrics = {
                'file_exists': os.path.exists(db_path),
                'file_size_mb': 0,
                'connection_test': False,
                'table_counts': {}
            }
            
            if os.path.exists(db_path):
                # 文件大小
                metrics['file_size_mb'] = os.path.getsize(db_path) / 1024 / 1024
                
                # 测试数据库连接和查询
                try:
                    conn = sqlite3.connect(db_path, timeout=5)
                    cursor = conn.cursor()
                    
                    # 测试连接
                    cursor.execute("SELECT 1")
                    metrics['connection_test'] = True
                    
                    # 获取表记录数（仅在basic级别以上）
                    if self.config.level != 'basic':
                        tables = ['temporary_emails', 'received_mails', 'email_history']
                        for table in tables:
                            try:
                                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                                count = cursor.fetchone()[0]
                                metrics['table_counts'][table] = count
                            except sqlite3.OperationalError:
                                # 表不存在
                                metrics['table_counts'][table] = 0
                    
                    conn.close()
                    
                except sqlite3.Error as e:
                    self.logger.warning(f"数据库查询失败: {e}")
                    metrics['connection_error'] = str(e)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集数据库指标失败: {e}")
            return {'error': str(e)}
    
    def _collect_detailed_metrics(self) -> Dict[str, Any]:
        """收集详细指标（仅在standard/detailed级别）"""
        try:
            metrics = {}
            
            # 网络IO（如果可用）
            try:
                net_io = psutil.net_io_counters()
                if net_io:
                    metrics['network'] = {
                        'bytes_sent': net_io.bytes_sent,
                        'bytes_recv': net_io.bytes_recv,
                        'packets_sent': net_io.packets_sent,
                        'packets_recv': net_io.packets_recv
                    }
            except Exception:
                pass
            
            # 磁盘IO
            try:
                disk_io = psutil.disk_io_counters()
                if disk_io:
                    metrics['disk_io'] = {
                        'read_bytes': disk_io.read_bytes,
                        'write_bytes': disk_io.write_bytes,
                        'read_count': disk_io.read_count,
                        'write_count': disk_io.write_count
                    }
            except Exception:
                pass
            
            # 日志文件大小
            log_dir = Path('logs')
            if log_dir.exists():
                total_log_size = sum(f.stat().st_size for f in log_dir.glob('*.log'))
                metrics['logs'] = {
                    'total_size_mb': total_log_size / 1024 / 1024
                }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集详细指标失败: {e}")
            return {'error': str(e)}
    
    def get_latest_metrics(self) -> Dict[str, Any]:
        """获取最新的指标"""
        if not self.metrics_history:
            return {'error': '暂无指标数据'}
        
        return self.metrics_history[-1]
    
    def get_metrics_summary(self, minutes: int = 10) -> Dict[str, Any]:
        """获取指定时间范围内的指标摘要"""
        if not self.metrics_history:
            return {'error': '暂无指标数据'}
        
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        # 筛选时间范围内的指标
        recent_metrics = []
        for metric in self.metrics_history:
            try:
                metric_time = datetime.fromisoformat(metric['timestamp'])
                if metric_time >= cutoff_time:
                    recent_metrics.append(metric)
            except (KeyError, ValueError):
                continue
        
        if not recent_metrics:
            return {'error': f'最近{minutes}分钟内无指标数据'}
        
        # 计算摘要统计
        cpu_values = []
        memory_values = []
        
        for metric in recent_metrics:
            try:
                if 'system' in metric and 'cpu_percent' in metric['system']:
                    cpu_values.append(metric['system']['cpu_percent'])
                if 'system' in metric and 'memory' in metric['system']:
                    memory_values.append(metric['system']['memory']['percent'])
            except (KeyError, TypeError):
                continue
        
        summary = {
            'time_range_minutes': minutes,
            'data_points': len(recent_metrics),
            'latest_timestamp': recent_metrics[-1]['timestamp']
        }
        
        if cpu_values:
            summary['cpu'] = {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            }
        
        if memory_values:
            summary['memory'] = {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            }
        
        return summary
    
    def cleanup_old_metrics(self):
        """清理过期指标（deque会自动限制大小，这里主要是日志记录）"""
        initial_count = len(self.metrics_history)
        
        # deque会自动清理，这里只是记录
        if initial_count > 0:
            self.logger.debug(f"指标历史记录: {initial_count} 个数据点")
    
    def get_memory_usage(self) -> float:
        """获取指标收集器的内存使用量（MB）"""
        try:
            import sys
            
            # 估算metrics_history的内存使用
            if self.metrics_history:
                # 粗略估算：每个指标约1KB
                estimated_mb = len(self.metrics_history) * 1024 / 1024 / 1024
                return estimated_mb
            
            return 0.0
        except Exception:
            return 0.0
