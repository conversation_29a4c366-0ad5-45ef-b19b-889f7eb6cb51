"""
内部邮件发送器

利用临时邮箱系统的Postfix基础设施发送监控告警邮件
通过创建专用管理员邮箱和本地邮件发送来实现自给自足的告警通知
"""

import os
import subprocess
import tempfile
import logging
import sqlite3
import datetime
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.utils import formatdate, make_msgid


class InternalMailSender:
    """内部邮件发送器"""

    def __init__(self, config, logger, app=None):
        """初始化内部邮件发送器

        Args:
            config: 监控配置对象
            logger: 日志记录器
            app: Flask应用实例（可选）
        """
        self.config = config
        self.logger = logger
        self.app = app

        # 管理员邮箱配置
        self.admin_email_prefix = config.internal_admin_email_prefix or "monitoring-admin"
        self.domain_name = self._get_domain_name()
        self.admin_email = f"{self.admin_email_prefix}@{self.domain_name}"

        # 邮件发送配置
        self.sendmail_path = config.sendmail_path or "/usr/sbin/sendmail"
        self.max_retry_attempts = config.mail_retry_attempts or 3

        # 初始化管理员邮箱
        self._ensure_admin_email_exists()

        self.logger.info(f"内部邮件发送器初始化完成，管理员邮箱: {self.admin_email}")

    def _get_domain_name(self) -> str:
        """获取域名配置"""
        # 优先从监控配置获取
        if hasattr(self.config, 'domain_name') and self.config.domain_name:
            return self.config.domain_name

        # 从环境变量获取
        domain = os.getenv('DOMAIN_NAME')
        if domain:
            return domain

        # 从Flask应用配置获取
        if self.app and hasattr(self.app, 'config'):
            domain = self.app.config.get('DOMAIN_NAME')
            if domain:
                return domain

        # 默认域名
        return "localhost"

    def _ensure_admin_email_exists(self):
        """确保管理员邮箱存在"""
        try:
            # 检查管理员邮箱是否已存在
            if self._check_admin_email_exists():
                self.logger.debug(f"管理员邮箱 {self.admin_email} 已存在")
                return

            # 创建管理员邮箱（永不过期）
            self._create_admin_email()
            self.logger.info(f"已创建管理员邮箱: {self.admin_email}")

        except Exception as e:
            self.logger.error(f"确保管理员邮箱存在时发生错误: {e}")

    def _check_admin_email_exists(self) -> bool:
        """检查管理员邮箱是否存在"""
        try:
            db_path = self._get_database_path()
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT id FROM temporary_emails WHERE address = ?",
                    (self.admin_email,)
                )
                return cursor.fetchone() is not None
        except Exception as e:
            self.logger.error(f"检查管理员邮箱存在性时出错: {e}")
            return False

    def _create_admin_email(self):
        """创建管理员邮箱"""
        try:
            db_path = self._get_database_path()

            # 设置过期时间为100年后（实际上永不过期）
            now_utc = datetime.datetime.now(datetime.timezone.utc)
            expires_at = now_utc + datetime.timedelta(days=365 * 100)

            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """INSERT INTO temporary_emails
                       (address, session_id, expires_at, created_at)
                       VALUES (?, ?, ?, ?)""",
                    (
                        self.admin_email,
                        "monitoring-system",  # 特殊会话ID
                        expires_at.isoformat(),
                        now_utc.isoformat()
                    )
                )
                conn.commit()

        except sqlite3.IntegrityError:
            # 邮箱已存在，忽略错误
            pass
        except Exception as e:
            self.logger.error(f"创建管理员邮箱时出错: {e}")
            raise

    def _get_database_path(self) -> str:
        """获取数据库路径"""
        # 优先从Flask应用配置获取
        if self.app and hasattr(self.app, 'config'):
            db_path = self.app.config.get('DATABASE_PATH')
            if db_path:
                return str(db_path)

        # 从环境变量获取
        db_path = os.getenv('DATABASE_PATH')
        if db_path:
            return db_path

        # 默认路径
        return 'database/tempmail.db'

    def send_alert_email(self, alert_data: Dict[str, Any], recipient_email: str) -> bool:
        """发送告警邮件

        Args:
            alert_data: 告警数据
            recipient_email: 收件人邮箱

        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建邮件内容
            msg = self._build_alert_message(alert_data, recipient_email)

            # 发送邮件
            success = self._send_message(msg)

            if success:
                self.logger.info(f"内部邮件告警已发送: {alert_data['id']} -> {recipient_email}")
            else:
                self.logger.error(f"内部邮件告警发送失败: {alert_data['id']} -> {recipient_email}")

            return success

        except Exception as e:
            self.logger.error(f"发送内部邮件告警时发生异常: {e}", exc_info=True)
            return False

    def _build_alert_message(self, alert_data: Dict[str, Any], recipient_email: str) -> MIMEMultipart:
        """构建告警邮件消息"""
        msg = MIMEMultipart()

        # 邮件头
        msg['From'] = self.admin_email
        msg['To'] = recipient_email
        msg['Subject'] = f"[临时邮箱监控] {alert_data['severity'].upper()}: {alert_data['id']}"
        msg['Date'] = formatdate(localtime=True)
        msg['Message-ID'] = make_msgid(domain=self.domain_name)

        # 邮件正文
        body = self._format_alert_body(alert_data)
        msg.attach(MIMEText(body, 'plain', 'utf-8'))

        return msg

    def _format_alert_body(self, alert_data: Dict[str, Any]) -> str:
        """格式化告警邮件正文"""
        severity_emoji = {
            'warning': '⚠️',
            'critical': '🚨',
            'urgent': '🔥'
        }

        emoji = severity_emoji.get(alert_data['severity'], '📢')

        body = f"""
{emoji} 临时邮箱系统监控告警

告警ID: {alert_data['id']}
严重程度: {alert_data['severity'].upper()}
时间: {alert_data['timestamp']}
消息: {alert_data['message']}

详细信息:
{self._format_alert_details(alert_data.get('details', {}))}

系统信息:
- 监控系统: 临时邮箱轻量级监控
- 发送方式: 内部邮件系统
- 管理员邮箱: {self.admin_email}

---
此邮件由临时邮箱系统监控自动发送
如需停止接收告警邮件，请联系系统管理员
        """.strip()

        return body

    def _format_alert_details(self, details: Dict[str, Any]) -> str:
        """格式化告警详细信息"""
        if not details:
            return "  无详细信息"

        lines = []
        for key, value in details.items():
            if isinstance(value, (int, float)):
                if key.endswith('_percent') or key.endswith('_threshold'):
                    lines.append(f"  {key}: {value:.1f}%")
                elif key.endswith('_mb'):
                    lines.append(f"  {key}: {value:.1f}MB")
                elif key.endswith('_seconds') or key.endswith('_time'):
                    lines.append(f"  {key}: {value:.2f}秒")
                else:
                    lines.append(f"  {key}: {value}")
            else:
                lines.append(f"  {key}: {value}")

        return "\n".join(lines)

    def _send_message(self, msg: MIMEMultipart) -> bool:
        """通过本地sendmail发送邮件消息"""
        for attempt in range(self.max_retry_attempts):
            try:
                # 将邮件内容写入临时文件
                with tempfile.NamedTemporaryFile(mode='w', suffix='.eml', delete=False) as temp_file:
                    temp_file.write(msg.as_string())
                    temp_file_path = temp_file.name

                try:
                    # 使用sendmail发送邮件
                    cmd = [self.sendmail_path, '-t', '-i']

                    with open(temp_file_path, 'r') as f:
                        result = subprocess.run(
                            cmd,
                            input=f.read(),
                            text=True,
                            capture_output=True,
                            timeout=30
                        )

                    if result.returncode == 0:
                        self.logger.debug(f"邮件发送成功 (尝试 {attempt + 1})")
                        return True
                    else:
                        self.logger.warning(
                            f"sendmail返回错误码 {result.returncode} (尝试 {attempt + 1}): "
                            f"stderr={result.stderr}"
                        )

                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_file_path)
                    except OSError:
                        pass

            except subprocess.TimeoutExpired:
                self.logger.warning(f"sendmail超时 (尝试 {attempt + 1})")
            except FileNotFoundError:
                self.logger.error(f"sendmail程序未找到: {self.sendmail_path}")
                return False
            except Exception as e:
                self.logger.warning(f"发送邮件时发生异常 (尝试 {attempt + 1}): {e}")

        self.logger.error(f"邮件发送失败，已重试 {self.max_retry_attempts} 次")
        return False

    def test_mail_sending(self, recipient_email: str) -> Tuple[bool, str]:
        """测试邮件发送功能

        Args:
            recipient_email: 测试收件人邮箱

        Returns:
            Tuple[bool, str]: (是否成功, 结果消息)
        """
        try:
            # 构建测试邮件
            test_alert = {
                'id': 'test_mail_sending',
                'severity': 'warning',
                'message': '这是一封测试邮件，用于验证内部邮件发送功能',
                'timestamp': datetime.datetime.now().isoformat(),
                'details': {
                    'test_type': '邮件发送功能测试',
                    'admin_email': self.admin_email,
                    'domain_name': self.domain_name,
                    'sendmail_path': self.sendmail_path
                }
            }

            # 发送测试邮件
            success = self.send_alert_email(test_alert, recipient_email)

            if success:
                return True, f"测试邮件已发送到 {recipient_email}"
            else:
                return False, "测试邮件发送失败"

        except Exception as e:
            return False, f"测试邮件发送时发生异常: {e}"

    def get_admin_email_info(self) -> Dict[str, Any]:
        """获取管理员邮箱信息"""
        try:
            db_path = self._get_database_path()
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """SELECT id, address, session_id, created_at, expires_at
                       FROM temporary_emails WHERE address = ?""",
                    (self.admin_email,)
                )
                row = cursor.fetchone()

                if row:
                    return {
                        'id': row[0],
                        'address': row[1],
                        'session_id': row[2],
                        'created_at': row[3],
                        'expires_at': row[4],
                        'exists': True
                    }
                else:
                    return {'exists': False}

        except Exception as e:
            self.logger.error(f"获取管理员邮箱信息时出错: {e}")
            return {'exists': False, 'error': str(e)}

    def cleanup_admin_emails(self) -> int:
        """清理过期的管理员邮件（保留最近100封）

        Returns:
            int: 清理的邮件数量
        """
        try:
            db_path = self._get_database_path()
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # 获取管理员邮箱ID
                cursor.execute(
                    "SELECT id FROM temporary_emails WHERE address = ?",
                    (self.admin_email,)
                )
                admin_email_row = cursor.fetchone()

                if not admin_email_row:
                    return 0

                admin_email_id = admin_email_row[0]

                # 删除除最近100封邮件外的所有邮件
                cursor.execute(
                    """DELETE FROM received_mails
                       WHERE email_address_id = ?
                       AND id NOT IN (
                           SELECT id FROM received_mails
                           WHERE email_address_id = ?
                           ORDER BY received_at DESC
                           LIMIT 100
                       )""",
                    (admin_email_id, admin_email_id)
                )

                deleted_count = cursor.rowcount
                conn.commit()

                if deleted_count > 0:
                    self.logger.info(f"已清理 {deleted_count} 封过期的管理员邮件")

                return deleted_count

        except Exception as e:
            self.logger.error(f"清理管理员邮件时出错: {e}")
            return 0