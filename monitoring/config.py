"""
轻量级监控配置模块
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass
class MonitoringConfig:
    """监控配置类"""
    
    # 基础配置
    enabled: bool = True
    level: str = "basic"  # basic, standard, detailed
    
    # 采集间隔（秒）
    metrics_interval: int = 60
    health_check_interval: int = 300
    
    # 资源限制
    max_memory_mb: int = 20
    max_log_size_mb: int = 50
    
    # 告警阈值
    cpu_threshold: float = 80.0
    memory_threshold: float = 85.0
    disk_threshold: float = 90.0
    response_time_threshold: float = 5.0  # 秒
    error_rate_threshold: float = 10.0  # 百分比
    
    # 通知配置
    email_notifications: bool = True
    webhook_notifications: bool = False
    use_internal_mail: bool = True  # 是否使用内部邮件系统

    # 内部邮件配置
    internal_admin_email_prefix: Optional[str] = None  # 管理员邮箱前缀
    domain_name: Optional[str] = None  # 域名
    sendmail_path: str = "/usr/sbin/sendmail"  # sendmail程序路径
    mail_retry_attempts: int = 3  # 邮件发送重试次数
    alert_email_to: Optional[str] = None  # 告警收件人邮箱
    
    # Webhook配置
    webhook_url: Optional[str] = None
    webhook_timeout: int = 10
    
    # 数据保留
    metrics_retention_hours: int = 24
    log_retention_days: int = 7
    
    @classmethod
    def from_env(cls) -> 'MonitoringConfig':
        """从环境变量加载配置"""
        return cls(
            enabled=os.getenv('MONITORING_ENABLED', 'true').lower() == 'true',
            level=os.getenv('MONITORING_LEVEL', 'basic'),
            
            metrics_interval=int(os.getenv('MONITORING_METRICS_INTERVAL', '60')),
            health_check_interval=int(os.getenv('MONITORING_HEALTH_INTERVAL', '300')),
            
            max_memory_mb=int(os.getenv('MONITORING_MAX_MEMORY_MB', '20')),
            max_log_size_mb=int(os.getenv('MONITORING_MAX_LOG_SIZE_MB', '50')),
            
            cpu_threshold=float(os.getenv('MONITORING_CPU_THRESHOLD', '80.0')),
            memory_threshold=float(os.getenv('MONITORING_MEMORY_THRESHOLD', '85.0')),
            disk_threshold=float(os.getenv('MONITORING_DISK_THRESHOLD', '90.0')),
            response_time_threshold=float(os.getenv('MONITORING_RESPONSE_TIME_THRESHOLD', '5.0')),
            error_rate_threshold=float(os.getenv('MONITORING_ERROR_RATE_THRESHOLD', '10.0')),
            
            email_notifications=os.getenv('MONITORING_EMAIL_ENABLED', 'true').lower() == 'true',
            webhook_notifications=os.getenv('MONITORING_WEBHOOK_ENABLED', 'false').lower() == 'true',
            use_internal_mail=os.getenv('MONITORING_USE_INTERNAL_MAIL', 'true').lower() == 'true',

            internal_admin_email_prefix=os.getenv('MONITORING_ADMIN_EMAIL_PREFIX', 'monitoring-admin'),
            domain_name=os.getenv('DOMAIN_NAME'),
            sendmail_path=os.getenv('MONITORING_SENDMAIL_PATH', '/usr/sbin/sendmail'),
            mail_retry_attempts=int(os.getenv('MONITORING_MAIL_RETRY_ATTEMPTS', '3')),
            alert_email_to=os.getenv('MONITORING_ALERT_EMAIL_TO'),
            
            webhook_url=os.getenv('MONITORING_WEBHOOK_URL'),
            webhook_timeout=int(os.getenv('MONITORING_WEBHOOK_TIMEOUT', '10')),
            
            metrics_retention_hours=int(os.getenv('MONITORING_METRICS_RETENTION_HOURS', '24')),
            log_retention_days=int(os.getenv('MONITORING_LOG_RETENTION_DAYS', '7'))
        )
    
    def get_alert_thresholds(self) -> Dict[str, float]:
        """获取告警阈值字典"""
        return {
            'cpu': self.cpu_threshold,
            'memory': self.memory_threshold,
            'disk': self.disk_threshold,
            'response_time': self.response_time_threshold,
            'error_rate': self.error_rate_threshold
        }
    
    def is_notification_enabled(self) -> bool:
        """检查是否启用了任何通知方式"""
        return self.email_notifications or self.webhook_notifications
    
    def validate(self) -> List[str]:
        """验证配置，返回错误列表"""
        errors = []
        
        if self.level not in ['basic', 'standard', 'detailed']:
            errors.append(f"无效的监控级别: {self.level}")
        
        if self.metrics_interval < 10:
            errors.append("指标采集间隔不能小于10秒")
        
        if self.health_check_interval < 30:
            errors.append("健康检查间隔不能小于30秒")
        
        if self.email_notifications:
            if not self.alert_email_to:
                errors.append("启用邮件通知时必须配置收件人邮箱")

            # 内部邮件验证
            if not self.domain_name:
                errors.append("使用内部邮件时必须配置域名")
            if not self.internal_admin_email_prefix:
                errors.append("使用内部邮件时必须配置管理员邮箱前缀")
        
        if self.webhook_notifications and not self.webhook_url:
            errors.append("启用Webhook通知时必须配置Webhook URL")
        
        return errors


# 默认配置实例
DEFAULT_CONFIG = MonitoringConfig()
