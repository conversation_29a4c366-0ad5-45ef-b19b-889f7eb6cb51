"""
临时邮箱 MVP 应用
基于 Flask + SQLite + JavaScript 的轻量级临时邮箱服务
"""

import os
import sqlite3
import datetime
import secrets  # 用于生成更安全的随机字符串
import logging
import sys  # 导入 sys 以便使用 sys.exit()
import re
import html
from functools import lru_cache, wraps
from pathlib import Path

from flask import Flask, jsonify, request, render_template, current_app
from flask_caching import Cache
from dotenv import load_dotenv
import click  # 用于 @app.cli.command

# 导入数据库适配器
try:
    from database.db_adapter import db_manager
    DATABASE_ADAPTER_AVAILABLE = True
except ImportError as e:
    print(f"警告: 数据库适配器导入失败: {e}")
    DATABASE_ADAPTER_AVAILABLE = False

# 导入监控模块
try:
    from monitoring.flask_integration import init_monitoring
    from monitoring.config import MonitoringConfig
    MONITORING_AVAILABLE = True
except ImportError as e:
    print(f"警告: 监控模块导入失败: {e}")
    MONITORING_AVAILABLE = False

# --- 配置加载 ---
project_root = Path(__file__).resolve().parent
load_dotenv(str(project_root / '.env'))

# Flask 应用实例化
app = Flask(__name__, static_folder='static', template_folder='templates')

# 配置 debug 模式
# 为了测试，直接设置 Flask 环境变量
app.config['DEBUG'] = False  # 直接设置为 False
app.config['ENV'] = 'production'  # 直接设置为 production

# 缓存配置
app.config['CACHE_TYPE'] = os.getenv('FLASK_CACHE_TYPE', 'simple')
app.config['CACHE_DEFAULT_TIMEOUT'] = int(os.getenv('FLASK_CACHE_DEFAULT_TIMEOUT', '300'))
app.config['CACHE_THRESHOLD'] = int(os.getenv('FLASK_CACHE_THRESHOLD', '500'))

# 如果使用Redis缓存
if app.config['CACHE_TYPE'] == 'redis':
    app.config['CACHE_REDIS_URL'] = os.getenv('CACHE_REDIS_URL', 'redis://localhost:6379/0')

# 如果使用文件系统缓存
if app.config['CACHE_TYPE'] == 'filesystem':
    app.config['CACHE_DIR'] = os.getenv('CACHE_DIR', '/tmp/flask_cache')

# 初始化缓存
cache = Cache(app)

# 将配置加载到 app.config
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
SECRET_KEY_IS_DEFAULT_OR_UNSET = False

# 加载自动刷新配置
auto_refresh_env = os.getenv('AUTO_REFRESH_ENABLED', 'true').lower()
app.config['AUTO_REFRESH_ENABLED'] = auto_refresh_env in ('true', '1', 't')
refresh_interval = os.getenv('AUTO_REFRESH_INTERVAL', '10000')
# 移除可能存在的注释
refresh_interval = refresh_interval.split('#')[0].strip()
app.config['AUTO_REFRESH_INTERVAL'] = int(refresh_interval)

# 加载API配置
app.config['API_BASE_URL'] = os.getenv('API_BASE_URL', '')
try:
    app.config['API_TIMEOUT'] = int(os.getenv('API_TIMEOUT', '10000'))
except ValueError:
    app.config['API_TIMEOUT'] = 10000
    print(f"警告: 环境变量 API_TIMEOUT ('{os.getenv('API_TIMEOUT')}') 配置无效，使用默认值 10000。")

try:
    app.config['API_RETRY_ATTEMPTS'] = int(os.getenv('API_RETRY_ATTEMPTS', '3'))
except ValueError:
    app.config['API_RETRY_ATTEMPTS'] = 3
    print(f"警告: 环境变量 API_RETRY_ATTEMPTS ('{os.getenv('API_RETRY_ATTEMPTS')}') 配置无效，使用默认值 3。")

try:
    app.config['API_RETRY_DELAY'] = int(os.getenv('API_RETRY_DELAY', '1000'))
except ValueError:
    app.config['API_RETRY_DELAY'] = 1000
    print(f"警告: 环境变量 API_RETRY_DELAY ('{os.getenv('API_RETRY_DELAY')}') 配置无效，使用默认值 1000。")

if not app.config['SECRET_KEY']:
    if app.config['DEBUG'] or app.config['TESTING']:
        # 在开发环境中，生成一个随机的密钥
        app.config['SECRET_KEY'] = secrets.token_hex(32)
        SECRET_KEY_IS_DEFAULT_OR_UNSET = True
        print("警告: 已为开发环境自动生成随机 SECRET_KEY。在生产环境中请务必手动设置！")
    else:
        print("CRITICAL: SECRET_KEY 未在环境变量中设置。为了生产安全，应用将终止。")
        sys.exit(1)

# 检查密钥强度
if not app.config['DEBUG'] and not app.config['TESTING']:
    if len(app.config['SECRET_KEY']) < 32:
        print("CRITICAL: SECRET_KEY 不够强大。为了生产安全，应用将终止。请设置一个至少32字符的 SECRET_KEY。")
        sys.exit(1)

# 使用 pathlib 处理数据库和日志路径
default_db_path = project_root / 'database/tempmail.db'
app.config['DATABASE_PATH'] = Path(os.getenv('DATABASE_PATH', default_db_path))
app.config['DOMAIN_NAME'] = os.getenv('DOMAIN_NAME') # 从环境变量加载域名配置

if not app.config['DOMAIN_NAME']:
    if not os.getenv('FLASK_DEBUG') == '1' and not app.config['TESTING']:
        print("CRITICAL: DOMAIN_NAME 未在环境变量中配置。核心功能将无法工作。应用将终止。")
        sys.exit(1)
    else:
        print("警告: DOMAIN_NAME 未在环境变量中配置。邮件地址生成功能将无法正常工作。")

try:
    app.config['EMAIL_EXPIRATION_HOURS'] = int(os.getenv('EMAIL_EXPIRATION_HOURS', '1'))
except ValueError:
    app.config['EMAIL_EXPIRATION_HOURS'] = 1
    env_value = os.getenv('EMAIL_EXPIRATION_HOURS')
    print(f"警告: 环境变量 EMAIL_EXPIRATION_HOURS ('{env_value}') 配置无效，使用默认值 1 小时。")

app.config['LOG_FILE_APP'] = Path(os.getenv('LOG_FILE_APP', project_root / 'logs/app.log'))

# 日志配置
def configure_logging(app_instance):
    """配置应用的日志记录器"""
    log_file_path = app_instance.config.get('LOG_FILE_APP')
    # 从配置或环境变量获取日志级别，默认为 INFO
    log_level_str = os.getenv('FLASK_LOG_LEVEL', 'INFO').upper()
    log_level = getattr(logging, log_level_str, logging.INFO)

    # 清除 app.logger 上可能已存在的任何处理器，以避免重复
    for handler in list(app_instance.logger.handlers):
        app_instance.logger.removeHandler(handler)

    if log_file_path:
        log_dir = log_file_path.parent
        if log_dir and not log_dir.exists():
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
            except OSError as e:
                print(f"错误: 无法创建日志目录 {log_dir}: {e}. 日志将输出到 stderr。")
                log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                logging.basicConfig(level=log_level, format=log_format)
                app_instance.logger.setLevel(log_level)
                app_instance.logger.propagate = True
                return
        try:
            file_handler = logging.FileHandler(str(log_file_path))
            file_handler.setLevel(log_level)
            detailed_format = ('%(asctime)s - %(name)s - %(levelname)s - '
                              '%(process)d - %(thread)d - %(message)s')
            formatter = logging.Formatter(detailed_format)
            file_handler.setFormatter(formatter)
            app_instance.logger.addHandler(file_handler)
            app_instance.logger.setLevel(log_level)
            app_instance.logger.propagate = False
            app_instance.logger.info(f"文件日志处理器已配置。级别: {log_level_str}。文件: {log_file_path}")
        except (OSError, IOError) as e:
            print(f"错误: 无法设置文件日志处理器到 {log_file_path}: {e}. 日志将输出到 stderr。")
            logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            app_instance.logger.setLevel(log_level)
            app_instance.logger.propagate = True
    else:
        logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        app_instance.logger.setLevel(log_level)
        app_instance.logger.propagate = True
        app_instance.logger.info(f"未配置文件日志路径。使用基本日志配置 (通常输出到stderr)。级别: {log_level_str}")

configure_logging(app)

# 初始化监控系统
if MONITORING_AVAILABLE:
    try:
        monitoring_config = MonitoringConfig.from_env()
        app.monitoring = init_monitoring(app, monitoring_config)
        app.logger.info(f"监控系统已启用，级别: {monitoring_config.level}")
    except Exception as e:
        app.logger.error(f"监控系统初始化失败: {e}")
        app.monitoring = None
else:
    app.monitoring = None
    app.logger.info("监控系统不可用")

# --- 数据库辅助函数 ---
def execute_db_query(query, params=None, fetch_one=False, fetch_all=False):
    """统一的数据库查询执行函数

    Args:
        query (str): SQL查询语句
        params (tuple): 查询参数
        fetch_one (bool): 是否返回单行结果
        fetch_all (bool): 是否返回所有结果

    Returns:
        查询结果或None
    """
    try:
        # 检查是否使用MySQL适配器
        if DATABASE_ADAPTER_AVAILABLE:
            db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
            if db_type == 'mysql':
                # 使用SQLAlchemy的text()和命名参数
                from sqlalchemy import text
                # 将位置参数转换为命名参数
                param_names = [f"param_{i}" for i in range(len(params or []))]
                named_query = query
                for i, param_name in enumerate(param_names):
                    named_query = named_query.replace('?', f':{param_name}', 1)

                param_dict = {param_names[i]: param for i, param in enumerate(params or [])}

                with get_db_connection() as conn:
                    result = conn.execute(text(named_query), param_dict)
                    if fetch_one:
                        row = result.fetchone()
                        return dict(row._mapping) if row else None
                    elif fetch_all:
                        return [dict(row._mapping) for row in result.fetchall()]
                    return result

        # 回退到SQLite逻辑
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params or ())
            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            return cursor
    except Exception as e:
        current_app.logger.error(f"数据库查询执行失败: {e}")
        raise

def get_db_connection():
    """获取数据库连接并设置超时和优化参数"""
    # 检查是否使用数据库适配器
    if DATABASE_ADAPTER_AVAILABLE:
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        if db_type == 'mysql':
            # 使用MySQL适配器
            if not db_manager._adapter:
                if not db_manager.initialize():
                    raise RuntimeError("MySQL数据库初始化失败")
            return db_manager.adapter.get_connection()
        # 如果DATABASE_TYPE不是mysql，继续使用SQLite逻辑

    # 回退到原有的SQLite连接逻辑
    db_path = current_app.config.get('DATABASE_PATH')
    if not db_path:
        fallback_db_path = Path(__file__).resolve().parent / 'database/tempmail.db'
        current_app.logger.warning(f"DATABASE_PATH 在配置中未找到，回退到默认路径: {fallback_db_path}")
        db_path = fallback_db_path

    # 根据是否为压力测试模式调整超时时间，支持环境变量配置
    default_timeout = 60 if current_app.config.get('STRESS_TESTING', False) else 20
    timeout = int(os.getenv('SQLITE_TIMEOUT', default_timeout))

    # 处理特殊情况：内存数据库或字符串路径
    if db_path == ':memory:' or isinstance(db_path, str):
        # 对于内存数据库或字符串路径，直接使用
        conn = sqlite3.connect(str(db_path), timeout=timeout)
    else:
        # 对于 Path 对象，确保目录存在
        if isinstance(db_path, str):
            db_path = Path(db_path)
        db_dir = db_path.parent
        if db_dir and not db_dir.exists():
            try:
                db_dir.mkdir(parents=True, exist_ok=True)
                current_app.logger.info(f"数据库目录 {db_dir} 已创建。")
            except OSError as e:
                current_app.logger.error(f"创建数据库目录 {db_dir} 失败: {e}")
                raise
        conn = sqlite3.connect(str(db_path), timeout=timeout)

    conn.row_factory = sqlite3.Row

    # 基础性能优化设置
    conn.execute('PRAGMA journal_mode=WAL')
    conn.execute('PRAGMA synchronous=NORMAL')
    conn.execute('PRAGMA temp_store=MEMORY')
    conn.execute('PRAGMA mmap_size=268435456')  # 256MB内存映射

    # 缓存大小配置（支持环境变量）
    default_cache_size = -8000 if current_app.config.get('STRESS_TESTING', False) else -8000
    cache_size = int(os.getenv('SQLITE_CACHE_SIZE', default_cache_size))
    conn.execute(f'PRAGMA cache_size={cache_size}')

    # 页面大小优化（注意：页面大小只能在数据库创建时设置）
    page_size = int(os.getenv('SQLITE_PAGE_SIZE', '4096'))
    try:
        conn.execute(f'PRAGMA page_size={page_size}')
    except sqlite3.OperationalError:
        # 页面大小无法在现有数据库上更改
        pass

    # 自动清理设置
    auto_vacuum_mode = os.getenv('SQLITE_AUTO_VACUUM', 'INCREMENTAL').upper()
    if auto_vacuum_mode == 'INCREMENTAL':
        conn.execute('PRAGMA auto_vacuum=INCREMENTAL')
    elif auto_vacuum_mode == 'FULL':
        conn.execute('PRAGMA auto_vacuum=FULL')
    else:
        conn.execute('PRAGMA auto_vacuum=NONE')

    # 压力测试模式下的额外优化
    if current_app.config.get('STRESS_TESTING', False):
        conn.execute('PRAGMA busy_timeout=30000')  # 30秒忙等待
        conn.execute('PRAGMA wal_autocheckpoint=1000')  # 更频繁的检查点

    return conn

def init_db_schema():
    """初始化数据库表结构 (只创建不存在的表和索引)"""

    # 检查是否使用数据库适配器
    if DATABASE_ADAPTER_AVAILABLE:
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        if db_type == 'mysql':
            # MySQL数据库初始化已在适配器中完成
            current_app.logger.info("MySQL数据库表结构初始化已完成。")
            return

    # SQLite数据库初始化逻辑
    db_sql_statements = [
        """
        CREATE TABLE IF NOT EXISTS temporary_emails (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            address TEXT NOT NULL,
            session_id TEXT,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        """,
        "CREATE INDEX IF NOT EXISTS idx_temporary_emails_address ON temporary_emails (address);",
        "CREATE INDEX IF NOT EXISTS idx_temporary_emails_session_id ON temporary_emails (session_id);",
        "CREATE INDEX IF NOT EXISTS idx_temporary_emails_expires_at ON temporary_emails (expires_at);",
        """
        CREATE TABLE IF NOT EXISTS received_mails (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email_address_id INTEGER NOT NULL,
            sender TEXT,
            subject TEXT,
            body_text TEXT,
            body_html TEXT,
            received_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (email_address_id) REFERENCES temporary_emails (id) ON DELETE CASCADE
        );
        """,
        "CREATE INDEX IF NOT EXISTS idx_received_mails_email_address_id ON received_mails (email_address_id);",
        "CREATE INDEX IF NOT EXISTS idx_received_mails_received_at ON received_mails (received_at);",
        """
        CREATE TABLE IF NOT EXISTS email_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            email_address TEXT NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            is_active BOOLEAN DEFAULT 0,
            FOREIGN KEY (email_address) REFERENCES temporary_emails (address) ON DELETE CASCADE
        );
        """,
        "CREATE INDEX IF NOT EXISTS idx_email_history_session_id ON email_history (session_id);",
        "CREATE INDEX IF NOT EXISTS idx_email_history_email_address ON email_history (email_address);",
        "CREATE INDEX IF NOT EXISTS idx_email_history_created_at ON email_history (created_at);"
    ]
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            for statement in db_sql_statements:
                cursor.execute(statement)
            conn.commit()
        current_app.logger.info("SQLite数据库表结构初始化/检查完毕。")
    except sqlite3.Error as e:
        current_app.logger.error(f"数据库初始化失败: {e}")
        raise



# 数据库初始化命令
@app.cli.command('init-db')
def init_db_command():
    """确保数据库表结构存在。如果表已存在，则不进行任何操作。""" # 修改了 docstring 以符合实际行为
    try:
        init_db_schema()
        click.echo("数据库表结构已初始化或已存在。")
    except Exception as e:
        click.echo(f"数据库初始化命令失败: {e}", err=True)

# 缓存管理命令
@app.cli.command('cache')
@click.option('--action', type=click.Choice(['clear', 'stats', 'info']),
              default='info', help='缓存操作')
def cache_command(action):
    """管理应用缓存"""
    if action == 'clear':
        cache.clear()
        click.echo("缓存已清空")
    elif action == 'stats':
        # 简单的缓存统计（如果缓存后端支持）
        try:
            if hasattr(cache.cache, 'get_stats'):
                stats = cache.cache.get_stats()
                click.echo(f"缓存统计: {stats}")
            else:
                click.echo("当前缓存后端不支持统计信息")
        except Exception as e:
            click.echo(f"获取缓存统计失败: {e}")
    elif action == 'info':
        click.echo(f"缓存类型: {app.config['CACHE_TYPE']}")
        click.echo(f"默认超时: {app.config['CACHE_DEFAULT_TIMEOUT']}秒")
        click.echo(f"缓存阈值: {app.config['CACHE_THRESHOLD']}")

# 监控系统管理命令
@app.cli.command('monitoring')
@click.option('--action', type=click.Choice(['status', 'start', 'stop', 'restart']),
              default='status', help='监控操作')
def monitoring_command(action):
    """管理监控系统"""
    if not MONITORING_AVAILABLE:
        click.echo("监控系统不可用", err=True)
        return

    if not hasattr(app, 'monitoring') or app.monitoring is None:
        click.echo("监控系统未初始化", err=True)
        return

    if action == 'status':
        status = app.monitoring.get_request_stats()
        click.echo(f"监控状态:")
        click.echo(f"  总请求数: {status['total_requests']}")
        click.echo(f"  错误数: {status['error_count']}")
        click.echo(f"  错误率: {status['error_rate']:.2f}%")
        click.echo(f"  平均响应时间: {status['avg_response_time']:.3f}秒")

        if app.monitoring.monitor:
            monitor_status = app.monitoring.monitor.get_status()
            click.echo(f"  监控运行状态: {'运行中' if monitor_status['running'] else '已停止'}")
            click.echo(f"  运行时间: {monitor_status['uptime_seconds']:.0f}秒")
            click.echo(f"  监控级别: {monitor_status['level']}")

    elif action == 'start':
        if app.monitoring.monitor:
            app.monitoring.monitor.start()
            click.echo("监控系统已启动")
        else:
            click.echo("监控器未初始化", err=True)

    elif action == 'stop':
        if app.monitoring.monitor:
            app.monitoring.monitor.stop()
            click.echo("监控系统已停止")
        else:
            click.echo("监控器未初始化", err=True)

    elif action == 'restart':
        if app.monitoring.monitor:
            app.monitoring.monitor.stop()
            app.monitoring.monitor.start()
            click.echo("监控系统已重启")
        else:
            click.echo("监控器未初始化", err=True)

# --- 配置验证函数 ---
def validate_config():
    """验证应用配置的完整性"""
    required_configs = {
        'DOMAIN_NAME': '域名配置',
        'DATABASE_PATH': '数据库路径',
        'EMAIL_EXPIRATION_HOURS': '邮箱过期时间'
    }

    missing = []
    for config_key, config_name in required_configs.items():
        if not app.config.get(config_key):
            missing.append(config_name)

    if missing:
        msg = f"缺少必要的配置项: {', '.join(missing)}"
        app.logger.error(msg)
        if not app.config['DEBUG']:
            raise RuntimeError(msg)

# --- API 辅助函数 ---
def error_response(message, status_code=400, details=None, error_code=None):
    """统一的错误响应格式，支持 error_code"""
    response_data = {"error": message}
    if details:
        response_data["details"] = details
    if error_code:
        response_data["error_code"] = error_code
    return jsonify(response_data), status_code


def validate_email_address(email):
    """验证邮箱地址格式

    Args:
        email (str): 邮箱地址

    Returns:
        tuple: (is_valid, error_message)
    """
    if not email:
        return False, "邮箱地址不能为空"

    if len(email) > 254:  # RFC 5321 限制
        return False, "邮箱地址长度不能超过254个字符"

    # 基本邮箱格式验证
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return False, "邮箱地址格式无效"

    return True, None


def validate_custom_prefix(prefix):
    """验证自定义前缀的有效性

    Args:
        prefix (str): 要验证的前缀

    Returns:
        tuple: (is_valid, error_message)
    """
    if not prefix:
        return False, "自定义前缀只能包含字母、数字和连字符"

    if len(prefix) > 20:
        return False, "自定义前缀只能包含字母、数字和连字符，长度1-20字符"

    if not re.match(r'^[a-zA-Z0-9\-]{1,20}$', prefix):
        return False, "自定义前缀只能包含字母、数字和连字符"

    return True, None


def validate_timestamp(timestamp_str):
    """验证时间戳格式

    Args:
        timestamp_str (str): 时间戳字符串

    Returns:
        tuple: (is_valid, error_message, datetime_obj)
    """
    if not timestamp_str:
        return False, "时间戳不能为空", None

    try:
        dt = datetime.datetime.fromisoformat(timestamp_str)
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=datetime.timezone.utc)
        return True, None, dt
    except ValueError:
        return False, "时间戳格式无效，请使用 ISO 格式", None


def sanitize_html_content(content):
    """清理HTML内容，防止XSS攻击

    Args:
        content (str): 原始HTML内容

    Returns:
        str: 清理后的安全内容
    """
    if not content:
        return ""

    # 简单的HTML转义，防止XSS
    return html.escape(content)


def validate_request_data(flask_request):
    """验证请求数据格式

    Args:
        flask_request: Flask request 对象

    Returns:
        tuple: (data, error_message)
    """
    if flask_request.content_type != 'application/json':
        return None, "请求必须使用 application/json 内容类型"

    try:
        data = flask_request.get_json()
        if data is None:
            return None, "无效的JSON格式"
        return data, None
    except Exception:
        return None, "无效的JSON格式"


def generate_email_prefix(custom_prefix, attempt):
    """生成邮箱前缀

    Args:
        custom_prefix (str): 自定义前缀
        attempt (int): 尝试次数

    Returns:
        str: 生成的邮箱前缀
    """
    if custom_prefix:
        if attempt == 0:
            # 第一次尝试使用纯自定义前缀
            return custom_prefix
        # 如果冲突，添加随机后缀
        random_suffix = secrets.token_hex(2)
        return f"{custom_prefix}-{random_suffix}"

    # 默认行为：生成随机前缀
    return secrets.token_hex(6)


def generate_email_suggestions(original_prefix, domain_name, count=5, session_id=None):
    """生成邮箱地址建议

    Args:
        original_prefix (str): 原始前缀
        domain_name (str): 域名
        count (int): 生成建议数量
        session_id (str, optional): 会话ID，如果提供则只检查该用户的邮箱

    Returns:
        list: 可用的邮箱地址建议列表
    """
    suggestions = []
    current_year = datetime.datetime.now().year
    current_date = datetime.datetime.now().strftime("%m%d")

    # 生成不同类型的建议
    suggestion_patterns = [
        f"{original_prefix}1",
        f"{original_prefix}2",
        f"{original_prefix}123",
        f"{original_prefix}_{current_year}",
        f"{original_prefix}_{current_date}",
        f"{original_prefix}_1",
        f"{original_prefix}_2",
        f"{original_prefix}_new",
        f"{original_prefix}_temp",
        f"{original_prefix}{current_year}",
    ]

    # 检查每个建议的可用性
    for pattern in suggestion_patterns:
        if len(suggestions) >= count:
            break

        email_address = f"{pattern}@{domain_name}"

        # 使用更新后的可用性检查函数
        if check_email_availability(email_address, session_id):
            suggestions.append(email_address)

    # 如果建议不够，添加一些随机后缀的建议
    while len(suggestions) < count:
        random_suffix = secrets.token_hex(2)
        pattern = f"{original_prefix}_{random_suffix}"
        email_address = f"{pattern}@{domain_name}"

        if check_email_availability(email_address, session_id):
            suggestions.append(email_address)
        else:
            # 如果检查失败，仍然添加建议（可能可用）
            suggestions.append(email_address)

    return suggestions[:count]


def check_email_availability(email_address, session_id=None):
    """检查邮箱地址是否可用

    Args:
        email_address (str): 要检查的邮箱地址
        session_id (str, optional): 会话ID，如果提供则只检查该用户的邮箱

    Returns:
        bool: True表示可用，False表示已被占用
    """
    try:
        # 检查是否使用MySQL适配器
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        if DATABASE_ADAPTER_AVAILABLE and SQLALCHEMY_AVAILABLE and db_type == 'mysql':
            # 使用MySQL适配器
            with get_db_connection() as conn:
                if session_id:
                    # 检查指定用户会话中是否已存在该邮箱地址
                    result = conn.execute(
                        text("SELECT id FROM temporary_emails WHERE address = :address AND session_id = :session_id"),
                        {"address": email_address, "session_id": session_id}
                    )
                else:
                    # 检查全局是否存在该邮箱地址（向后兼容）
                    result = conn.execute(
                        text("SELECT id FROM temporary_emails WHERE address = :address"),
                        {"address": email_address}
                    )
                return result.fetchone() is None

        # 回退到SQLite逻辑
        with get_db_connection() as conn:
            cursor = conn.cursor()
            if session_id:
                # 检查指定用户会话中是否已存在该邮箱地址
                cursor.execute(
                    "SELECT id FROM temporary_emails WHERE address = ? AND session_id = ?",
                    (email_address, session_id)
                )
            else:
                # 检查全局是否存在该邮箱地址（向后兼容）
                cursor.execute(
                    "SELECT id FROM temporary_emails WHERE address = ?",
                    (email_address,)
                )
            return cursor.fetchone() is None
    except sqlite3.Error as e:
        current_app.logger.error(f"检查邮箱可用性时出错: {e}")
        return False


def create_email_record(email_address, expires_at_iso, created_at_iso, session_id=None):
    """在数据库中创建邮箱记录

    Args:
        email_address (str): 邮箱地址
        expires_at_iso (str): 过期时间ISO格式
        created_at_iso (str): 创建时间ISO格式
        session_id (str, optional): 会话ID，用于用户隔离

    Returns:
        bool: 是否创建成功

    Raises:
        sqlite3.IntegrityError: 地址冲突
        sqlite3.Error: 其他数据库错误
    """
    try:
        # 检查是否使用MySQL适配器
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        current_app.logger.info(f"create_email_record: DATABASE_ADAPTER_AVAILABLE={DATABASE_ADAPTER_AVAILABLE}, SQLALCHEMY_AVAILABLE={SQLALCHEMY_AVAILABLE}, db_type={db_type}")
        if DATABASE_ADAPTER_AVAILABLE and SQLALCHEMY_AVAILABLE and db_type == 'mysql':
            # 使用MySQL适配器
            with get_db_connection() as conn:
                # 检查在同一个session中是否已存在相同的邮箱地址
                if session_id:
                    result = conn.execute(
                        text("SELECT id FROM temporary_emails WHERE address = :address AND session_id = :session_id"),
                        {"address": email_address, "session_id": session_id}
                    )
                    existing = result.fetchone()
                    if existing:
                        raise sqlite3.IntegrityError(f"邮箱地址 {email_address} 在会话 {session_id} 中已存在")

                conn.execute(
                    text("INSERT INTO temporary_emails (address, session_id, expires_at, created_at) VALUES (:address, :session_id, :expires_at, :created_at)"),
                    {"address": email_address, "session_id": session_id, "expires_at": expires_at_iso, "created_at": created_at_iso}
                )
                conn.commit()
            return True

        # 回退到SQLite逻辑
        with get_db_connection() as conn:
            cursor = conn.cursor()

            # 检查在同一个session中是否已存在相同的邮箱地址
            if session_id:
                cursor.execute(
                    "SELECT id FROM temporary_emails WHERE address = ? AND session_id = ?",
                    (email_address, session_id)
                )
                existing = cursor.fetchone()
                if existing:
                    raise sqlite3.IntegrityError(f"邮箱地址 {email_address} 在会话 {session_id} 中已存在")

            cursor.execute(
                "INSERT INTO temporary_emails (address, session_id, expires_at, created_at) VALUES (?, ?, ?, ?)",
                (email_address, session_id, expires_at_iso, created_at_iso)
            )
            conn.commit()
        return True
    except Exception as e:
        current_app.logger.error(f"创建邮箱记录失败: {type(e).__name__}: {e}")
        raise


def add_to_email_history(session_id, email_address, expires_at_iso, created_at_iso):
    """将邮箱添加到历史记录

    Args:
        session_id (str): 会话ID
        email_address (str): 邮箱地址
        expires_at_iso (str): 过期时间ISO格式
        created_at_iso (str): 创建时间ISO格式

    Returns:
        bool: 是否添加成功
    """
    # 检查是否使用MySQL适配器
    db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
    if DATABASE_ADAPTER_AVAILABLE and SQLALCHEMY_AVAILABLE and db_type == 'mysql':
        # 使用MySQL适配器
        with get_db_connection() as conn:
            # 先将该会话的所有邮箱设为非活跃状态
            conn.execute(
                text("UPDATE email_history SET is_active = 0 WHERE session_id = :session_id"),
                {"session_id": session_id}
            )
            # 添加新邮箱到历史记录并设为活跃状态
            conn.execute(
                text("""INSERT INTO email_history
                   (session_id, email_address, created_at, expires_at, is_active)
                   VALUES (:session_id, :email_address, :created_at, :expires_at, 1)"""),
                {"session_id": session_id, "email_address": email_address, "created_at": created_at_iso, "expires_at": expires_at_iso}
            )
            conn.commit()
        return True

    # 回退到SQLite逻辑
    with get_db_connection() as conn:
        cursor = conn.cursor()
        # 先将该会话的所有邮箱设为非活跃状态
        cursor.execute(
            "UPDATE email_history SET is_active = 0 WHERE session_id = ?",
            (session_id,)
        )
        # 添加新邮箱到历史记录并设为活跃状态
        cursor.execute(
            """INSERT INTO email_history
               (session_id, email_address, created_at, expires_at, is_active)
               VALUES (?, ?, ?, ?, 1)""",
            (session_id, email_address, created_at_iso, expires_at_iso)
        )
        conn.commit()
    return True


def get_email_history(session_id):
    """获取用户的邮箱历史记录

    Args:
        session_id (str): 会话ID

    Returns:
        list: 邮箱历史记录列表
    """
    # 检查是否使用MySQL适配器
    if DATABASE_ADAPTER_AVAILABLE:
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        if db_type == 'mysql':
            # 使用MySQL适配器
            with get_db_connection() as conn:
                from sqlalchemy import text
                result = conn.execute(
                    text("""SELECT eh.email_address, eh.created_at, eh.expires_at, eh.is_active,
                                  te.id as temp_email_id
                           FROM email_history eh
                           LEFT JOIN temporary_emails te ON eh.email_address = te.address
                           WHERE eh.session_id = :session_id
                           ORDER BY eh.created_at DESC"""),
                    {"session_id": session_id}
                )
                history = result.fetchall()

                # 检查邮箱是否过期
                now_utc = datetime.datetime.now(datetime.timezone.utc)
                result_list = []
                for record in history:
                    expires_at_dt = datetime.datetime.fromisoformat(record[2])  # expires_at
                    if expires_at_dt.tzinfo is None:
                        expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)

                    is_expired = now_utc > expires_at_dt
                    result_list.append({
                        'email_address': record[0],  # email_address
                        'created_at': record[1],     # created_at
                        'expires_at': record[2],     # expires_at
                        'is_active': bool(record[3]), # is_active
                        'is_expired': is_expired,
                        'exists_in_db': record[4] is not None  # temp_email_id
                    })

                return result_list

    # 回退到SQLite逻辑
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(
            """SELECT eh.email_address, eh.created_at, eh.expires_at, eh.is_active,
                      te.id as temp_email_id
               FROM email_history eh
               LEFT JOIN temporary_emails te ON eh.email_address = te.address
               WHERE eh.session_id = ?
               ORDER BY eh.created_at DESC""",
            (session_id,)
        )
        history = cursor.fetchall()

        # 检查邮箱是否过期
        now_utc = datetime.datetime.now(datetime.timezone.utc)
        result = []
        for record in history:
            expires_at_dt = datetime.datetime.fromisoformat(record['expires_at'])
            if expires_at_dt.tzinfo is None:
                expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)

            is_expired = now_utc > expires_at_dt
            result.append({
                'email_address': record['email_address'],
                'created_at': record['created_at'],
                'expires_at': record['expires_at'],
                'is_active': bool(record['is_active']),
                'is_expired': is_expired,
                'exists_in_db': record['temp_email_id'] is not None
            })

        return result


def switch_to_email(session_id, email_address):
    """切换到指定的历史邮箱

    Args:
        session_id (str): 会话ID
        email_address (str): 要切换到的邮箱地址

    Returns:
        tuple: (success, error_message, email_data)
    """
    # 检查是否使用MySQL适配器
    if DATABASE_ADAPTER_AVAILABLE:
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        if db_type == 'mysql':
            # 使用MySQL适配器
            with get_db_connection() as conn:
                from sqlalchemy import text

                # 检查邮箱是否在历史记录中
                result = conn.execute(
                    text("SELECT * FROM email_history WHERE session_id = :session_id AND email_address = :email_address"),
                    {"session_id": session_id, "email_address": email_address}
                )
                history_record = result.fetchone()

                if not history_record:
                    return False, "邮箱不在历史记录中", None

                # 检查邮箱是否仍然存在于temporary_emails表中
                result = conn.execute(
                    text("SELECT * FROM temporary_emails WHERE address = :address"),
                    {"address": email_address}
                )
                temp_email_record = result.fetchone()

                if not temp_email_record:
                    return False, "邮箱已被删除", None

                # 检查邮箱是否过期
                expires_at_dt = datetime.datetime.fromisoformat(temp_email_record[3])  # expires_at
                if expires_at_dt.tzinfo is None:
                    expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)

                now_utc = datetime.datetime.now(datetime.timezone.utc)
                if now_utc > expires_at_dt:
                    return False, "邮箱已过期", None

                # 更新活跃状态
                conn.execute(
                    text("UPDATE email_history SET is_active = 0 WHERE session_id = :session_id"),
                    {"session_id": session_id}
                )
                conn.execute(
                    text("UPDATE email_history SET is_active = 1 WHERE session_id = :session_id AND email_address = :email_address"),
                    {"session_id": session_id, "email_address": email_address}
                )
                conn.commit()

                return True, None, {
                    'address': temp_email_record[1],  # address
                    'expires_at': temp_email_record[3]  # expires_at
                }

    # 回退到SQLite逻辑
    with get_db_connection() as conn:
        cursor = conn.cursor()

        # 检查邮箱是否在历史记录中
        cursor.execute(
            "SELECT * FROM email_history WHERE session_id = ? AND email_address = ?",
            (session_id, email_address)
        )
        history_record = cursor.fetchone()

        if not history_record:
            return False, "邮箱不在历史记录中", None

        # 检查邮箱是否仍然存在于temporary_emails表中
        cursor.execute(
            "SELECT * FROM temporary_emails WHERE address = ?",
            (email_address,)
        )
        temp_email_record = cursor.fetchone()

        if not temp_email_record:
            return False, "邮箱已被删除", None

        # 检查邮箱是否过期
        expires_at_dt = datetime.datetime.fromisoformat(temp_email_record['expires_at'])
        if expires_at_dt.tzinfo is None:
            expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)

        now_utc = datetime.datetime.now(datetime.timezone.utc)
        if now_utc > expires_at_dt:
            return False, "邮箱已过期", None

        # 更新活跃状态
        cursor.execute(
            "UPDATE email_history SET is_active = 0 WHERE session_id = ?",
            (session_id,)
        )
        cursor.execute(
            "UPDATE email_history SET is_active = 1 WHERE session_id = ? AND email_address = ?",
            (session_id, email_address)
        )
        conn.commit()

        return True, None, {
            'address': temp_email_record['address'],
            'expires_at': temp_email_record['expires_at']
        }


def delete_email(email_address):
    """删除指定的邮箱地址

    Args:
        email_address (str): 要删除的邮箱地址

    Returns:
        tuple: (success, error_message)
    """
    # 检查是否使用MySQL适配器
    if DATABASE_ADAPTER_AVAILABLE:
        db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
        if db_type == 'mysql':
            # 使用MySQL适配器
            with get_db_connection() as conn:
                from sqlalchemy import text

                # 检查邮箱是否存在
                result = conn.execute(
                    text("SELECT id FROM temporary_emails WHERE address = :address"),
                    {"address": email_address}
                )
                email_record = result.fetchone()

                if not email_record:
                    return False, "邮箱地址不存在"

                # 删除邮箱记录（由于外键约束，相关的邮件也会被删除）
                conn.execute(
                    text("DELETE FROM temporary_emails WHERE address = :address"),
                    {"address": email_address}
                )
                conn.commit()

                return True, None

    # 回退到SQLite逻辑
    with get_db_connection() as conn:
        cursor = conn.cursor()

        # 检查邮箱是否存在
        cursor.execute(
            "SELECT id FROM temporary_emails WHERE address = ?",
            (email_address,)
        )
        email_record = cursor.fetchone()

        if not email_record:
            return False, "邮箱地址不存在"

        # 删除邮箱记录（由于外键约束，相关的邮件也会被删除）
        cursor.execute(
            "DELETE FROM temporary_emails WHERE address = ?",
            (email_address,)
        )
        conn.commit()

        return True, None

def generate_random_address(custom_prefix=None):
    """生成随机邮箱地址

    Args:
        custom_prefix (str, optional): 自定义前缀，如果提供则使用该前缀

    Returns:
        str: 生成的邮箱地址
    """
    domain_name = current_app.config.get('DOMAIN_NAME', 'develop.local')

    if custom_prefix:
        # 使用自定义前缀
        email_prefix = custom_prefix
    else:
        # 生成随机前缀（12个字符的十六进制字符串）
        email_prefix = secrets.token_hex(6)

    return f"{email_prefix}@{domain_name}"

@lru_cache(maxsize=100)
def get_email_cache_key(email_id, address):
    """缓存邮件内容的键生成器"""
    return f"{email_id}:{address}"

def rate_limit(max_requests=5, window_seconds=60):
    """简单的内存速率限制装饰器"""
    storage = {}
    rate_limit._storage = storage

    def clear():
        storage.clear()
    rate_limit.clear = clear

    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            # Skip rate limiting in testing mode or stress testing mode
            if (current_app.config.get('TESTING', False) or
                current_app.config.get('STRESS_TESTING', False) or
                current_app.config.get('DISABLE_RATE_LIMIT', False)):
                return f(*args, **kwargs)

            client_ip = request.remote_addr
            now = datetime.datetime.now(datetime.timezone.utc)

            # 清理过期记录
            if client_ip in storage:
                storage[client_ip] = [t for t in storage[client_ip]
                                    if (now - t).total_seconds() < window_seconds]

            # 优化：只锁定 window_seconds / max_requests 的最短间隔
            min_interval = window_seconds / max_requests
            if client_ip in storage and len(storage[client_ip]) >= max_requests:
                # 只要距离最早一次请求已过最小间隔，就允许新请求
                earliest = min(storage[client_ip])
                if (now - earliest).total_seconds() < min_interval:
                    return error_response(
                        "请求过于频繁，请稍后再试", 429, error_code="RATE_LIMIT_EXCEEDED"
                    )
                # 移除最早的请求，允许新请求
                storage[client_ip].pop(0)
            # 记录本次请求
            storage.setdefault(client_ip, []).append(now)

            return f(*args, **kwargs)
        return wrapped
    return decorator

# --- API 端点实现 ---
def _validate_generate_request():
    """验证生成邮箱地址的请求"""
    domain_name = current_app.config.get('DOMAIN_NAME')
    if not domain_name:
        current_app.logger.error("API /generate-address 调用失败：DOMAIN_NAME 未配置。")
        return None, jsonify({"success": False, "data": None, "error": "服务配置不完整 (域名未设置)"}), 503

    # 验证请求数据
    data, error_msg = validate_request_data(request)
    if error_msg:
        return None, jsonify({"success": False, "data": None, "error": error_msg}), 400

    # 获取并验证自定义前缀
    custom_prefix = None
    prefix_value = data.get('custom_prefix')
    if prefix_value is not None:
        custom_prefix = str(prefix_value).strip()
        is_valid, error_msg = validate_custom_prefix(custom_prefix)
        if not is_valid:
            return None, jsonify({"success": False, "data": None, "error": error_msg}), 400

    return custom_prefix, None


def _attempt_create_email(custom_prefix, domain_name, email_expiration_hours, session_id=None, max_retries=5):
    """尝试创建邮箱地址"""
    logger = current_app.logger
    retry_errors = []

    for attempt in range(max_retries):
        # 生成邮箱前缀和地址
        email_prefix = generate_email_prefix(custom_prefix, attempt)
        new_email_address = f"{email_prefix}@{domain_name}"
        logger.info(f"尝试创建邮箱地址: {new_email_address} (尝试 {attempt + 1}/{max_retries})")

        # 生成时间戳
        now_utc = datetime.datetime.now(datetime.timezone.utc)
        expires_at_dt = now_utc + datetime.timedelta(hours=email_expiration_hours)
        expires_at_iso = expires_at_dt.isoformat()
        created_at_iso = now_utc.isoformat()

        try:
            create_email_record(new_email_address, expires_at_iso, created_at_iso, session_id)

            # 如果提供了session_id，则添加到历史记录
            if session_id:
                try:
                    add_to_email_history(session_id, new_email_address, expires_at_iso, created_at_iso)
                    logger.info(f"邮箱 {new_email_address} 已添加到会话 {session_id} 的历史记录")
                except Exception as e:
                    logger.warning(f"添加邮箱到历史记录失败: {e}")

            logger.info(f"生成新邮箱地址: {new_email_address}, 过期时间: {expires_at_iso}")
            return {
                "success": True,
                "data": {"address": new_email_address, "expires_at": expires_at_iso},
                "error": None
            }, 201
        except sqlite3.IntegrityError as e:
            error_detail = {
                'attempt': attempt + 1,
                'address': new_email_address,
                'error_type': 'IntegrityError',
                'error_message': str(e)
            }
            retry_errors.append(error_detail)
            logger.warning(
                f"生成邮箱地址失败 (尝试 {attempt + 1}/{max_retries}): "
                f"地址 {new_email_address} 发生冲突: {str(e)}"
            )

            # 如果是自定义前缀且第一次尝试失败，生成建议
            if custom_prefix and attempt == 0:
                logger.info(f"自定义邮箱地址 {new_email_address} 已被占用，生成替代建议")
                suggestions = generate_email_suggestions(custom_prefix, domain_name, 5, session_id)
                return {
                    "success": False,
                    "error": "邮箱地址已被占用",
                    "error_code": "EMAIL_ALREADY_EXISTS",
                    "data": {
                        "requested_address": new_email_address,
                        "suggestions": suggestions
                    }
                }, 409
            continue
        except sqlite3.Error as e:
            error_detail = {
                'attempt': attempt + 1,
                'address': new_email_address,
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
            retry_errors.append(error_detail)
            logger.error(
                f"生成邮箱地址时发生数据库错误 (尝试 {attempt + 1}/{max_retries}): "
                f"地址 {new_email_address}, 错误: {str(e)}",
                exc_info=True
            )
            continue

    # 所有尝试都失败了
    error_summary = {
        'total_attempts': max_retries,
        'all_errors': retry_errors,
        'last_attempted_address': new_email_address if 'new_email_address' in locals() else None
    }
    logger.error(
        "生成邮箱地址达到最大重试次数，所有尝试均失败。详细错误记录：",
        extra={'error_details': error_summary}
    )
    return jsonify({"success": False, "data": None, "error": "生成邮箱地址失败，请稍后重试"}), 500


@app.route('/api/generate-address', methods=['POST'])
@rate_limit(max_requests=5, window_seconds=60)
def generate_address():
    """生成一个新的临时邮箱地址"""
    try:
        # 验证请求
        validation_result = _validate_generate_request()
        if len(validation_result) == 3:  # 有错误
            return validation_result[1], validation_result[2]

        custom_prefix = validation_result[0]

        # 获取session_id（如果提供）
        data, _ = validate_request_data(request)
        session_id = data.get('session_id') if data else None

        # 获取配置
        domain_name = current_app.config.get('DOMAIN_NAME')
        email_expiration_hours = current_app.config.get('EMAIL_EXPIRATION_HOURS', 1)

        # 尝试创建邮箱
        return _attempt_create_email(custom_prefix, domain_name, email_expiration_hours, session_id)
    except Exception as e:
        current_app.logger.error(f"生成邮箱地址时发生异常: {type(e).__name__}: {e}", exc_info=True)
        return jsonify({"success": False, "data": None, "error": "生成邮箱地址失败，请稍后重试"}), 500


@app.route('/api/create-suggested-email', methods=['POST'])
@rate_limit(max_requests=5, window_seconds=60)
def create_suggested_email():
    """使用建议的邮箱地址创建邮箱"""
    # 验证请求数据
    data, error_msg = validate_request_data(request)
    if error_msg:
        return error_response(error_msg, 400)

    # 获取建议的邮箱地址
    suggested_address = data.get('suggested_address')
    if not suggested_address:
        return error_response("缺少 'suggested_address' 参数", 400, error_code="MISSING_SUGGESTED_ADDRESS")

    # 验证邮箱地址格式
    is_valid, error_msg = validate_email_address(suggested_address)
    if not is_valid:
        return error_response(error_msg, 400, error_code="INVALID_EMAIL")

    # 获取session_id（如果提供）
    session_id = data.get('session_id')

    # 获取配置
    domain_name = current_app.config.get('DOMAIN_NAME')
    email_expiration_hours = current_app.config.get('EMAIL_EXPIRATION_HOURS', 1)

    if not domain_name:
        current_app.logger.error("API /create-suggested-email 调用失败：DOMAIN_NAME 未配置。")
        return error_response("服务配置不完整 (域名未设置)", 503)

    # 验证建议的邮箱地址是否属于当前域名
    if not suggested_address.endswith(f"@{domain_name}"):
        return error_response("建议的邮箱地址域名不匹配", 400, error_code="DOMAIN_MISMATCH")

    # 检查邮箱是否仍然可用（在用户会话范围内）
    if not check_email_availability(suggested_address, session_id):
        return error_response("建议的邮箱地址已被占用", 409, error_code="EMAIL_NO_LONGER_AVAILABLE")

    # 生成时间戳
    now_utc = datetime.datetime.now(datetime.timezone.utc)
    expires_at_dt = now_utc + datetime.timedelta(hours=email_expiration_hours)
    expires_at_iso = expires_at_dt.isoformat()
    created_at_iso = now_utc.isoformat()

    try:
        # 创建邮箱记录
        create_email_record(suggested_address, expires_at_iso, created_at_iso, session_id)

        # 如果提供了session_id，则添加到历史记录
        if session_id:
            try:
                add_to_email_history(session_id, suggested_address, expires_at_iso, created_at_iso)
                current_app.logger.info(f"建议邮箱 {suggested_address} 已添加到会话 {session_id} 的历史记录")
            except Exception as e:
                current_app.logger.warning(f"添加建议邮箱到历史记录失败: {e}")

        current_app.logger.info(f"成功创建建议邮箱地址: {suggested_address}")
        return jsonify({
            "success": True,
            "data": {"address": suggested_address, "expires_at": expires_at_iso},
            "error": None
        }), 201

    except sqlite3.IntegrityError:
        # 邮箱地址在创建过程中被其他请求占用
        return error_response("邮箱地址已被占用", 409, error_code="EMAIL_ALREADY_EXISTS")
    except sqlite3.Error as e:
        current_app.logger.error(f"创建建议邮箱时发生数据库错误: {e}", exc_info=True)
        return error_response("数据库操作失败", 500, error_code="DATABASE_ERROR")
    except Exception as e:
        current_app.logger.error(f"创建建议邮箱时发生未知错误: {e}", exc_info=True)
        return error_response("服务器内部错误", 500, error_code="INTERNAL_SERVER_ERROR")

@app.route('/api/emails', methods=['GET'])
@cache.cached(timeout=60, query_string=True)  # 缓存1分钟，根据查询参数区分
def get_emails_for_address():
    """获取指定临时邮箱地址的邮件列表"""
    email_address = request.args.get('address')
    if not email_address:
        return error_response("缺少 'address' 参数", 400, error_code="MISSING_ADDRESS")

    # 验证邮箱地址格式
    is_valid, error_msg = validate_email_address(email_address)
    if not is_valid:
        return error_response(error_msg, 400, error_code="INVALID_EMAIL")

    # 验证时间戳参数
    last_received_dt = None
    last_received = request.args.get('last_received')
    if last_received:
        is_valid, error_msg, last_received_dt = validate_timestamp(last_received)
        if not is_valid:
            return error_response(error_msg, 400, error_code="INVALID_TIMESTAMP")
    logger = current_app.logger
    try:
        # 检查是否使用MySQL适配器
        if DATABASE_ADAPTER_AVAILABLE:
            db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
            if db_type == 'mysql':
                # 使用MySQL适配器
                with get_db_connection() as conn:
                    from sqlalchemy import text
                    # 查询邮箱记录
                    result = conn.execute(
                        text("SELECT id, expires_at FROM temporary_emails WHERE address = :address"),
                        {"address": email_address}
                    )
                    temp_email_record = result.fetchone()
                    if not temp_email_record:
                        logger.info(f"请求邮件列表：邮箱地址 {email_address} 不存在。")
                        return jsonify({"success": False, "data": None, "error": "邮箱地址不存在"}), 404

                    temp_email_id = temp_email_record[0]
                    expires_at_str = temp_email_record[1]
                    expires_at_dt = datetime.datetime.fromisoformat(expires_at_str)
                    now_utc = datetime.datetime.now(datetime.timezone.utc)
                    if expires_at_dt.tzinfo is None:
                        expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)
                    if now_utc > expires_at_dt:
                        logger.info(f"请求邮件列表：邮箱地址 {email_address} 已过期。")
                        return jsonify({"success": False, "data": None, "error": "邮箱地址已过期"}), 410

                    # 查询邮件
                    query = """
                        SELECT id, sender, subject, received_at, SUBSTR(body_text, 1, 100) as summary, body_text
                        FROM received_mails
                        WHERE email_address_id = :email_id
                    """
                    params = {"email_id": temp_email_id}
                    if last_received:
                        query += " AND received_at >= :last_received"
                        params["last_received"] = last_received
                    query += " ORDER BY received_at DESC"

                    result = conn.execute(text(query), params)
                    emails = result.fetchall()
                    emails_list = [dict(email._mapping) for email in emails]

                    if last_received:
                        logger.info(
                            f"增量获取邮箱 {email_address} 的新邮件，"
                            f"时间范围: {last_received} 之后，获取到 {len(emails_list)} 封。"
                        )
                    else:
                        logger.info(f"获取邮箱 {email_address} 的全部邮件，共 {len(emails_list)} 封。")

                    return jsonify({"success": True, "data": {"emails": emails_list, "timestamp": now_utc.isoformat()}, "error": None}), 200

        # 回退到SQLite逻辑
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT id, expires_at FROM temporary_emails WHERE address = ?",
                (email_address,)
            )
            temp_email_record = cursor.fetchone()
            if not temp_email_record:
                logger.info(f"请求邮件列表：邮箱地址 {email_address} 不存在。")
                return jsonify({"success": False, "data": None, "error": "邮箱地址不存在"}), 404
            temp_email_id = temp_email_record['id']
            expires_at_dt = datetime.datetime.fromisoformat(temp_email_record['expires_at'])
            now_utc = datetime.datetime.now(datetime.timezone.utc)
            if expires_at_dt.tzinfo is None:
                expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)
            if now_utc > expires_at_dt:
                logger.info(f"请求邮件列表：邮箱地址 {email_address} 已过期。")
                return jsonify({"success": False, "data": None, "error": "邮箱地址已过期"}), 410
            # 修改查询以避免时间格式转换问题
            query = """
                SELECT id, sender, subject, received_at, SUBSTR(body_text, 1, 100) as summary, body_text
                FROM received_mails
                WHERE email_address_id = ?
            """
            params = [temp_email_id]
            if last_received:
                query += " AND received_at >= ?"
                params.append(last_received)  # 使用ISO格式的时间字符串直接比较
            query += " ORDER BY received_at DESC"
            cursor.execute(query, params)
            emails_data = cursor.fetchall()
            emails_list = [dict(row) for row in emails_data]
            if last_received:
                logger.info(
                    f"增量获取邮箱 {email_address} 的新邮件，"
                    f"时间范围: {last_received} 之后，获取到 {len(emails_list)} 封。"
                )
            else:
                logger.info(f"获取邮箱 {email_address} 的全部邮件，共 {len(emails_list)} 封。")

            # Return an empty list instead of an error when no emails are found
            return jsonify({"success": True, "data": {"emails": emails_list, "timestamp": now_utc.isoformat()}, "error": None}), 200
    except Exception as e:
        logger.error(f"数据库错误 (get_emails_for_address): {e}", exc_info=True)
        return jsonify({"success": False, "data": None, "error": "数据库操作失败"}), 500

@app.route('/api/email/<int:email_id>', methods=['GET'])
@cache.cached(timeout=300, query_string=True)  # 缓存5分钟，邮件内容不常变化
def get_email_content(email_id):
    """获取特定邮件的详细内容"""
    requesting_address = request.args.get('address')
    logger = current_app.logger
    if not requesting_address:
        logger.warning(f"查看邮件 {email_id} 时未提供address参数进行校验 (当前允许)。")
    try:
        # 检查是否使用MySQL适配器
        if DATABASE_ADAPTER_AVAILABLE:
            db_type = os.getenv('DATABASE_TYPE', 'sqlite').lower()
            if db_type == 'mysql':
                # 使用MySQL适配器
                with get_db_connection() as conn:
                    from sqlalchemy import text
                    sql_query = """SELECT rm.id, rm.sender, rm.subject, rm.body_text, rm.body_html, rm.received_at
                                   FROM received_mails rm"""
                    params = {"email_id": email_id}
                    if requesting_address:
                        sql_query += " JOIN temporary_emails te ON rm.email_address_id = te.id WHERE rm.id = :email_id AND te.address = :address"
                        params["address"] = requesting_address
                    else:
                        sql_query += " WHERE rm.id = :email_id"

                    result = conn.execute(text(sql_query), params)
                    email_data = result.fetchone()
                    if not email_data:
                        logger.info(f"请求邮件内容：邮件ID {email_id} (地址: {requesting_address or '未提供'}) 未找到或无权访问。")
                        return jsonify({"success": False, "data": None, "error": "邮件未找到或无权访问"}), 404
                    logger.info(f"获取邮件ID {email_id} 的内容。")
                    return jsonify({"success": True, "data": dict(email_data._mapping), "error": None}), 200

        # 回退到SQLite逻辑
        with get_db_connection() as conn:
            cursor = conn.cursor()
            sql_query = """SELECT rm.id, rm.sender, rm.subject, rm.body_text, rm.body_html, rm.received_at \
                           FROM received_mails rm"""
            params = (email_id,)
            if requesting_address:
                sql_query += " JOIN temporary_emails te ON rm.email_address_id = te.id WHERE rm.id = ? AND te.address = ?"
                params = (email_id, requesting_address)
            else:
                sql_query += " WHERE rm.id = ?"
            cursor.execute(sql_query, params)
            email_data = cursor.fetchone()
            if not email_data:
                logger.info(f"请求邮件内容：邮件ID {email_id} (地址: {requesting_address or '未提供'}) 未找到或无权访问。")
                return jsonify({"success": False, "data": None, "error": "邮件未找到或无权访问"}), 404
            logger.info(f"获取邮件ID {email_id} 的内容。")
            return jsonify({"success": True, "data": dict(email_data), "error": None}), 200
    except Exception as e:
        logger.error(f"数据库错误 (get_email_content): {e}", exc_info=True)
        return jsonify({"success": False, "data": None, "error": "数据库操作失败"}), 500
    except (TypeError, AttributeError) as e:
        logger.error(f"数据处理错误 (get_email_content): {e}", exc_info=True)
        return error_response("邮件数据处理失败", 500, error_code="DATA_PROCESSING_ERROR")
    except Exception as e:
        logger.error(f"未知错误 (get_email_content): {e}", exc_info=True)
        return error_response("服务器内部错误", 500, error_code="UNKNOWN_ERROR")


@app.route('/api/email-history', methods=['GET'])
def get_email_history_api():
    """获取用户的邮箱历史记录"""
    session_id = request.args.get('session_id')
    if not session_id:
        return error_response("缺少 'session_id' 参数", 400, error_code="MISSING_SESSION_ID")

    try:
        history = get_email_history(session_id)
        return jsonify({
            "success": True,
            "data": {"history": history},
            "error": None
        }), 200
    except Exception as e:
        current_app.logger.error(f"获取邮箱历史记录失败: {e}", exc_info=True)
        return error_response("获取历史记录失败", 500, error_code="HISTORY_FETCH_ERROR")


@app.route('/api/switch-email', methods=['POST'])
@rate_limit(max_requests=10, window_seconds=60)
def switch_email_api():
    """切换到指定的历史邮箱"""
    data, error_msg = validate_request_data(request)
    if error_msg:
        return error_response(error_msg, 400)

    session_id = data.get('session_id')
    email_address = data.get('email_address')

    if not session_id:
        return error_response("缺少 'session_id' 参数", 400, error_code="MISSING_SESSION_ID")

    if not email_address:
        return error_response("缺少 'email_address' 参数", 400, error_code="MISSING_EMAIL_ADDRESS")

    # 验证邮箱地址格式
    is_valid, error_msg = validate_email_address(email_address)
    if not is_valid:
        return error_response(error_msg, 400, error_code="INVALID_EMAIL")

    try:
        success, error_msg, email_data = switch_to_email(session_id, email_address)

        if not success:
            return error_response(error_msg, 400, error_code="SWITCH_FAILED")

        return jsonify({
            "success": True,
            "data": email_data,
            "error": None
        }), 200

    except Exception as e:
        current_app.logger.error(f"切换邮箱失败: {e}", exc_info=True)
        return error_response("切换邮箱失败", 500, error_code="SWITCH_ERROR")


@app.route('/api/delete-email', methods=['DELETE'])
@rate_limit(max_requests=10, window_seconds=60)
def delete_email_api():
    """删除指定的邮箱地址"""
    data, error_msg = validate_request_data(request)
    if error_msg:
        return error_response(error_msg, 400)

    email_address = data.get('address')

    if not email_address:
        return error_response("缺少 'address' 参数", 400, error_code="MISSING_ADDRESS")

    # 验证邮箱地址格式
    is_valid, error_msg = validate_email_address(email_address)
    if not is_valid:
        return error_response(error_msg, 400, error_code="INVALID_EMAIL")

    try:
        success, error_msg = delete_email(email_address)

        if not success:
            return error_response(error_msg, 404, error_code="DELETE_FAILED")

        current_app.logger.info(f"邮箱 {email_address} 已被删除")
        return jsonify({
            "success": True,
            "data": {"message": "邮箱删除成功"},
            "error": None
        }), 200

    except Exception as e:
        current_app.logger.error(f"删除邮箱失败: {e}", exc_info=True)
        return error_response("删除邮箱失败", 500, error_code="DELETE_ERROR")


# --- 服务前端页面 ---
@app.route('/')
def index():
    """服务主HTML页面"""
    return render_template('index.html',
                         config={
                             'AUTO_REFRESH_ENABLED': app.config['AUTO_REFRESH_ENABLED'],
                             'AUTO_REFRESH_INTERVAL': app.config['AUTO_REFRESH_INTERVAL'],
                             'ENV': app.config['ENV'],
                             'API_BASE_URL': app.config.get('API_BASE_URL', ''),
                             'API_TIMEOUT': app.config.get('API_TIMEOUT', 10000),
                             'API_RETRY_ATTEMPTS': app.config.get('API_RETRY_ATTEMPTS', 3),
                             'API_RETRY_DELAY': app.config.get('API_RETRY_DELAY', 1000)
                         })

@app.route('/test-custom')
def test_custom():
    """服务自定义前缀测试页面"""
    return render_template('test_custom.html',
                         config={
                             'AUTO_REFRESH_ENABLED': app.config['AUTO_REFRESH_ENABLED'],
                             'AUTO_REFRESH_INTERVAL': app.config['AUTO_REFRESH_INTERVAL'],
                             'ENV': app.config['ENV'],
                             'API_BASE_URL': app.config.get('API_BASE_URL', ''),
                             'API_TIMEOUT': app.config.get('API_TIMEOUT', 10000),
                             'API_RETRY_ATTEMPTS': app.config.get('API_RETRY_ATTEMPTS', 3),
                             'API_RETRY_DELAY': app.config.get('API_RETRY_DELAY', 1000)
                         })

@app.route('/test_navigation_layout.html')
def test_navigation():
    """导航栏测试页面路由"""
    from flask import send_from_directory
    return send_from_directory('.', 'test_navigation_layout.html')

@app.route('/verify_navigation_fix.html')
def verify_navigation():
    """导航栏修复验证页面路由"""
    from flask import send_from_directory
    return send_from_directory('.', 'verify_navigation_fix.html')

@app.errorhandler(400)
def bad_request_error(error):
    """处理400错误"""
    current_app.logger.warning(f"400 错误: {str(error)}")
    return error_response("请求参数错误", 400, error_code="BAD_REQUEST")


@app.errorhandler(404)
def not_found_error(error):
    """处理404错误"""
    current_app.logger.warning(f"404 错误: {str(error)}")
    return error_response("请求的资源不存在", 404, error_code="NOT_FOUND")


@app.errorhandler(429)
def too_many_requests_error(error):
    """处理429错误"""
    current_app.logger.warning(f"429 错误: {str(error)}")
    return error_response("请求过于频繁，请稍后再试", 429, error_code="RATE_LIMIT_EXCEEDED")


@app.errorhandler(500)
def internal_server_error(error):
    """处理500错误"""
    current_app.logger.error(f"500 错误: {str(error)}", exc_info=True)
    return error_response("服务器内部错误", 500, error_code="INTERNAL_SERVER_ERROR")


@app.errorhandler(sqlite3.Error)
def sqlite_database_error(error):
    """处理SQLite数据库错误"""
    current_app.logger.error(f"SQLite数据库错误: {str(error)}", exc_info=True)
    return error_response("数据库操作失败", 500, error_code="DATABASE_ERROR")


# 添加SQLAlchemy导入
try:
    from sqlalchemy.exc import SQLAlchemyError
    from sqlalchemy import text
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

# 添加MySQL错误处理
if DATABASE_ADAPTER_AVAILABLE:
    try:
        import pymysql

        @app.errorhandler(SQLAlchemyError)
        def sqlalchemy_error(error):
            """处理SQLAlchemy错误"""
            current_app.logger.error(f"SQLAlchemy错误: {str(error)}", exc_info=True)
            return error_response("数据库操作失败", 500, error_code="DATABASE_ERROR")

        @app.errorhandler(pymysql.Error)
        def pymysql_error(error):
            """处理PyMySQL错误"""
            current_app.logger.error(f"MySQL错误: {str(error)}", exc_info=True)
            return error_response("数据库连接失败", 500, error_code="DATABASE_CONNECTION_ERROR")

    except ImportError:
        pass


@app.errorhandler(ValueError)
def value_error(error):
    """处理值错误"""
    current_app.logger.warning(f"值错误: {str(error)}")
    return error_response("参数值无效", 400, error_code="INVALID_VALUE")

# 在应用启动时初始化数据库
with app.app_context():
    try:
        init_db_schema()
        print("数据库初始化完成")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise

if __name__ == '__main__':
    validate_config()  # 启动时主动验证配置

    # 解析命令行参数
    port = 5000
    is_production = False

    # 解析参数（生产模式和端口）
    if len(sys.argv) > 1:
        if sys.argv[1] == 'production':
            is_production = True
        # 检查是否有端口参数 "--port 数字"
        for i in range(1, len(sys.argv) - 1):
            if sys.argv[i] == '--port':
                try:
                    port = int(sys.argv[i + 1])
                except ValueError:
                    print(f"警告: 无效的端口 '{sys.argv[i + 1]}'，使用默认端口 5000")

    if is_production:
        print(f"Flask 生产服务器启动... (访问 http://0.0.0.0:{port})")
        app.config['DEBUG'] = False
        app.config['ENV'] = 'production'
        app.run(debug=False, host='0.0.0.0', port=port)
    else:
        print(f"Flask 开发服务器启动... (访问 http://0.0.0.0:{port})")
        app.config['DEBUG'] = True
        app.config['ENV'] = 'development'
        app.run(debug=True, host='0.0.0.0', port=port)