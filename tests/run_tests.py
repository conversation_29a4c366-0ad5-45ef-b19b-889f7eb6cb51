#!/usr/bin/env python3
"""
测试运行脚本
提供便捷的测试运行选项和报告生成
"""
import subprocess
import sys
import argparse
import os
from pathlib import Path


def run_command(cmd, description=""):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    if description:
        print(f"运行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"返回码: {result.returncode}")
        return result.returncode == 0
    
    except Exception as e:
        print(f"执行命令时出错: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="运行测试套件")
    parser.add_argument("--type", choices=[
        "all", "unit", "integration", "functional", "api", 
        "frontend", "performance", "fast", "slow"
    ], default="fast", help="测试类型")
    
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--html", action="store_true", help="生成HTML报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--parallel", "-n", type=int, help="并行运行测试的进程数")
    parser.add_argument("--file", help="运行特定的测试文件")
    parser.add_argument("--function", help="运行特定的测试函数")
    
    args = parser.parse_args()
    
    # 确保在正确的目录中
    script_dir = Path(__file__).parent
    os.chdir(script_dir.parent)
    
    # 构建pytest命令
    cmd = ["python", "-m", "pytest"]
    
    # 添加详细输出
    if args.verbose:
        cmd.extend(["-v", "-s"])
    
    # 添加并行选项
    if args.parallel:
        cmd.extend(["-n", str(args.parallel)])
    
    # 添加覆盖率选项
    if args.coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
        if args.html:
            cmd.append("--cov-report=html")
    
    # 添加HTML报告
    if args.html and not args.coverage:
        cmd.extend(["--html=tests/reports/report.html", "--self-contained-html"])
    
    # 根据测试类型添加标记过滤器
    test_filters = {
        "all": [],
        "unit": ["-m", "unit"],
        "integration": ["-m", "integration"],
        "functional": ["-m", "functional"],
        "api": ["-m", "api"],
        "frontend": ["-m", "frontend"],
        "performance": ["-m", "performance"],
        "fast": ["-m", "not slow"],
        "slow": ["-m", "slow"]
    }
    
    if args.type in test_filters:
        cmd.extend(test_filters[args.type])
    
    # 添加特定文件或函数
    if args.file:
        if args.function:
            cmd.append(f"{args.file}::{args.function}")
        else:
            cmd.append(args.file)
    elif args.function:
        cmd.extend(["-k", args.function])
    
    # 创建报告目录
    reports_dir = Path("tests/reports")
    reports_dir.mkdir(exist_ok=True)
    
    # 运行测试
    success = run_command(cmd, f"运行{args.type}测试")
    
    if success:
        print("\n✅ 测试运行成功!")
        
        # 显示覆盖率报告位置
        if args.coverage:
            print("\n📊 覆盖率报告:")
            if args.html:
                html_report = Path("htmlcov/index.html")
                if html_report.exists():
                    print(f"  HTML报告: {html_report.absolute()}")
            
            xml_report = Path("coverage.xml")
            if xml_report.exists():
                print(f"  XML报告: {xml_report.absolute()}")
        
        # 显示HTML测试报告位置
        if args.html and not args.coverage:
            html_report = Path("tests/reports/report.html")
            if html_report.exists():
                print(f"  测试报告: {html_report.absolute()}")
    
    else:
        print("\n❌ 测试运行失败!")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
