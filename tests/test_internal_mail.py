#!/usr/bin/env python3
"""
内部邮件系统测试脚本

测试临时邮箱系统的内部邮件发送功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
os.environ['MONITORING_ENABLED'] = 'true'
os.environ['MONITORING_LEVEL'] = 'basic'
os.environ['MONITORING_USE_INTERNAL_MAIL'] = 'true'
os.environ['MONITORING_EMAIL_ENABLED'] = 'true'
os.environ['MONITORING_WEBHOOK_ENABLED'] = 'false'
os.environ['MONITORING_ADMIN_EMAIL_PREFIX'] = 'test-monitoring'
os.environ['DOMAIN_NAME'] = 'kuroneko.lol'  # 使用示例域名
os.environ['MONITORING_ALERT_EMAIL_TO'] = '<EMAIL>'  # 测试收件人

from monitoring.config import MonitoringConfig
from monitoring.internal_mail_sender import InternalMailSender


def test_internal_mail_config():
    """测试内部邮件配置"""
    print("🔧 测试内部邮件配置...")
    
    config = MonitoringConfig.from_env()
    
    print(f"  使用内部邮件: {config.email_notifications}")
    print(f"  管理员邮箱前缀: {config.internal_admin_email_prefix}")
    print(f"  域名: {config.domain_name}")
    print(f"  sendmail路径: {config.sendmail_path}")
    print(f"  重试次数: {config.mail_retry_attempts}")
    print(f"  收件人邮箱: {config.alert_email_to}")
    
    # 验证配置
    errors = config.validate()
    if errors:
        print(f"  ❌ 配置错误: {errors}")
        return False
    else:
        print("  ✅ 配置验证通过")
        return True


def test_internal_mail_sender():
    """测试内部邮件发送器"""
    print("\n📧 测试内部邮件发送器...")
    
    config = MonitoringConfig.from_env()
    
    # 创建日志记录器
    import logging
    logger = logging.getLogger('test_internal_mail')
    logger.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    try:
        # 初始化内部邮件发送器
        mail_sender = InternalMailSender(config, logger)
        
        print(f"  管理员邮箱: {mail_sender.admin_email}")
        print(f"  域名: {mail_sender.domain_name}")
        print(f"  sendmail路径: {mail_sender.sendmail_path}")
        
        # 获取管理员邮箱信息
        admin_info = mail_sender.get_admin_email_info()
        print(f"  管理员邮箱存在: {admin_info.get('exists', False)}")
        
        if admin_info.get('exists'):
            print(f"    邮箱ID: {admin_info.get('id')}")
            print(f"    创建时间: {admin_info.get('created_at')}")
            print(f"    过期时间: {admin_info.get('expires_at')}")
        
        return mail_sender
        
    except Exception as e:
        print(f"  ❌ 初始化内部邮件发送器失败: {e}")
        return None


def test_mail_sending(mail_sender, test_recipient):
    """测试邮件发送功能"""
    print(f"\n📤 测试邮件发送功能...")
    
    if not mail_sender:
        print("  ❌ 邮件发送器未初始化")
        return False
    
    try:
        # 测试邮件发送
        success, message = mail_sender.test_mail_sending(test_recipient)
        
        if success:
            print(f"  ✅ {message}")
            return True
        else:
            print(f"  ❌ {message}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试邮件发送时发生异常: {e}")
        return False


def test_alert_email(mail_sender, test_recipient):
    """测试告警邮件发送"""
    print(f"\n🚨 测试告警邮件发送...")
    
    if not mail_sender:
        print("  ❌ 邮件发送器未初始化")
        return False
    
    try:
        # 构建测试告警数据
        alert_data = {
            'id': 'test_internal_mail_alert',
            'severity': 'warning',
            'message': '这是一个内部邮件系统测试告警',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'details': {
                'test_type': '内部邮件系统测试',
                'sender_system': '临时邮箱监控',
                'admin_email': mail_sender.admin_email,
                'recipient': test_recipient,
                'domain': mail_sender.domain_name
            }
        }
        
        # 发送告警邮件
        success = mail_sender.send_alert_email(alert_data, test_recipient)
        
        if success:
            print(f"  ✅ 告警邮件发送成功")
            print(f"    告警ID: {alert_data['id']}")
            print(f"    收件人: {test_recipient}")
            print(f"    发件人: {mail_sender.admin_email}")
            return True
        else:
            print(f"  ❌ 告警邮件发送失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 发送告警邮件时发生异常: {e}")
        return False


def test_with_flask_app():
    """测试与Flask应用的集成"""
    print(f"\n🌐 测试Flask应用集成...")
    
    try:
        from app import app
        
        with app.app_context():
            config = MonitoringConfig.from_env()
            
            # 创建日志记录器
            import logging
            logger = logging.getLogger('test_flask_integration')
            logger.setLevel(logging.INFO)
            
            # 初始化内部邮件发送器（传入Flask应用）
            mail_sender = InternalMailSender(config, logger, app)
            
            print(f"  ✅ Flask集成成功")
            print(f"    管理员邮箱: {mail_sender.admin_email}")
            print(f"    数据库路径: {mail_sender._get_database_path()}")
            
            # 获取管理员邮箱信息
            admin_info = mail_sender.get_admin_email_info()
            print(f"    管理员邮箱存在: {admin_info.get('exists', False)}")
            
            return mail_sender
            
    except ImportError as e:
        print(f"  ❌ 无法导入Flask应用: {e}")
        return None
    except Exception as e:
        print(f"  ❌ Flask集成测试失败: {e}")
        return None


def test_cleanup_functionality(mail_sender):
    """测试清理功能"""
    print(f"\n🧹 测试邮件清理功能...")
    
    if not mail_sender:
        print("  ❌ 邮件发送器未初始化")
        return False
    
    try:
        # 执行清理
        deleted_count = mail_sender.cleanup_admin_emails()
        
        print(f"  ✅ 清理完成")
        print(f"    清理的邮件数量: {deleted_count}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 清理功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 内部邮件系统测试开始\n")
    
    test_recipient = os.getenv('MONITORING_ALERT_EMAIL_TO', '<EMAIL>')
    
    try:
        # 1. 测试配置
        config_ok = test_internal_mail_config()
        if not config_ok:
            print("\n❌ 配置测试失败，停止测试")
            return
        
        # 2. 测试内部邮件发送器
        mail_sender = test_internal_mail_sender()
        
        # 3. 测试Flask集成
        flask_mail_sender = test_with_flask_app()
        if flask_mail_sender:
            mail_sender = flask_mail_sender  # 使用Flask集成的版本
        
        # 4. 测试邮件发送功能
        if mail_sender:
            test_mail_sending(mail_sender, test_recipient)
            test_alert_email(mail_sender, test_recipient)
            test_cleanup_functionality(mail_sender)
        
        print("\n🎉 内部邮件系统测试完成！")
        
        # 总结
        print("\n📝 测试总结:")
        print("  ✅ 配置管理正常")
        if mail_sender:
            print("  ✅ 内部邮件发送器初始化成功")
            print("  ✅ 管理员邮箱创建/验证成功")
            print("  ✅ 邮件发送功能可用")
            print("  ✅ 清理功能正常")
        else:
            print("  ❌ 内部邮件发送器初始化失败")
        
        print(f"\n💡 使用说明:")
        print(f"  1. 确保Postfix已正确配置")
        print(f"  2. 确保sendmail程序可用")
        print(f"  3. 设置正确的收件人邮箱地址")
        print(f"  4. 检查邮件是否到达收件箱")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
