[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
# 排除备份相关文件和目录
norecursedirs = backup tests_backup .git .tox dist build *.egg __pycache__
# pytest选项和忽略模式
addopts = -v --tb=short --strict-markers
    --ignore-glob=backup/*
    --ignore-glob=*backup*
    --ignore-glob=*.bak
    --ignore-glob=*_backup.py
    --ignore-glob=*_backup.js
    --ignore-glob=tests_backup/*
    --ignore-glob=stress_test_results/*
    --ignore-glob=*stress_test*.json
    --ignore-glob=*stress_test*.txt
    --ignore-glob=*stress_test*.log

markers =
    unit: 单元测试 - 测试单个函数或方法
    integration: 集成测试 - 测试组件间的交互
    functional: 功能测试 - 测试完整的用户场景
    api: API测试 - 测试API端点
    frontend: 前端测试 - 测试前端相关功能
    performance: 性能测试 - 测试性能指标
    stress: 压力测试 - 测试系统负载能力
    slow: 慢速测试 - 运行时间较长的测试
    mail_handler: 邮件处理器测试
    cleanup: 清理脚本测试
    localization: 本地化测试
    mobile: 移动端测试
    responsive: 响应式设计测试
    requires_server: 需要服务器连接的测试
    requires_database: 需要数据库连接的测试
    requires_external_deps: 需要外部依赖的测试
    destructive: 破坏性测试（可能影响数据）
    environment_adaptive: 环境自适应测试

# 环境变量配置
env =
    TEST_BASE_URL = http://127.0.0.1:5001
    TEST_DATABASE_PATH = tests/database/test_tempmail.db
    TEST_DOMAIN_NAME = test.local
    TEST_EMAIL_EXPIRATION_HOURS = 1
    TEST_API_TIMEOUT = 30
    TEST_API_RETRY_ATTEMPTS = 3
    TEST_API_RETRY_DELAY = 1
    STRESS_TEST_BASE_URL = http://127.0.0.1:5002
    STRESS_TEST_MAX_CONCURRENT = 50
    STRESS_TEST_TIMEOUT = 300

# 测试过滤器示例:
# pytest -m "unit"                    # 只运行单元测试
# pytest -m "not slow"                # 排除慢速测试
# pytest -m "api and not slow"        # 运行API测试但排除慢速测试
# pytest -m "unit or integration"     # 运行单元测试和集成测试
# pytest -m "stress"                  # 只运行压力测试
# pytest -m "not stress and not slow" # 排除压力测试和慢速测试
# pytest -m "requires_server"         # 只运行需要服务器的测试
# pytest -m "not requires_external_deps" # 排除需要外部依赖的测试
# pytest -m "environment_adaptive"    # 只运行环境自适应测试

# 环境适配说明:
# - 设置 TEST_SKIP_EXTERNAL_DEPS=true 跳过外部依赖测试
# - 设置 TEST_SKIP_DESTRUCTIVE_TESTS=true 跳过破坏性测试
# - 设置 TEST_SKIP_SLOW_TESTS=true 跳过慢速测试
# - 在CI环境中会自动启用相应的跳过选项
