#!/usr/bin/env python3
"""
临时邮箱中文本地化测试脚本
验证所有界面文本是否正确中文化
"""

import os
import re
import json
from pathlib import Path

def test_html_localization():
    """测试HTML模板的中文化"""
    print("🔍 测试HTML模板中文化...")

    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    html_file = project_root / "templates/index.html"

    if not html_file.exists():
        assert False, f"HTML模板文件不存在: {html_file}"
    
    content = html_file.read_text(encoding='utf-8')
    
    # 检查页面标题
    if "临时邮箱 - 免费一次性邮箱服务" in content:
        print("✅ 页面标题已中文化")
    else:
        print("❌ 页面标题未中文化")
        return False
    
    # 检查导航栏
    nav_items = ["首页", "常见问题", "联系我们"]
    for item in nav_items:
        if item in content:
            print(f"✅ 导航项 '{item}' 已中文化")
        else:
            print(f"❌ 导航项 '{item}' 未中文化")
            return False
    
    # 检查按钮文本
    buttons = ["复制", "刷新", "新邮箱", "自定义", "历史记录", "删除并重置"]
    for button in buttons:
        if button in content:
            print(f"✅ 按钮 '{button}' 已中文化")
        else:
            print(f"❌ 按钮 '{button}' 未中文化")
            return False
    
    # 检查国际化属性
    i18n_attributes = ["data-i18n", "data-i18n-placeholder", "data-i18n-title"]
    has_i18n = any(attr in content for attr in i18n_attributes)
    if has_i18n:
        print("✅ HTML模板包含国际化属性")
    else:
        print("❌ HTML模板缺少国际化属性")
        return False
    
    return True

def test_javascript_localization():
    """测试JavaScript文件的中文化"""
    print("\n🔍 测试JavaScript中文化...")

    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    js_file = project_root / "static/js/main.js"

    if not js_file.exists():
        assert False, f"JavaScript主文件不存在: {js_file}"
    
    content = js_file.read_text(encoding='utf-8')
    
    # 检查国际化函数调用
    i18n_calls = re.findall(r'window\.i18n\s*\?\s*window\.i18n\.t\([\'"]([^\'"]+)[\'"]\)', content)
    if i18n_calls:
        print(f"✅ 找到 {len(i18n_calls)} 个国际化函数调用")
        print(f"   示例: {i18n_calls[:3]}")
    else:
        print("❌ 未找到国际化函数调用")
        return False
    
    # 检查欢迎消息
    if "window.i18n.t('welcome.title')" in content:
        print("✅ 欢迎消息已国际化")
    else:
        print("❌ 欢迎消息未国际化")
        return False
    
    return True

def test_i18n_module():
    """测试国际化模块"""
    print("\n🔍 测试国际化模块...")

    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    i18n_file = project_root / "static/js/i18n.js"

    if not i18n_file.exists():
        assert False, f"国际化模块文件不存在: {i18n_file}"
    
    content = i18n_file.read_text(encoding='utf-8')
    
    # 检查语言支持
    if "'zh-CN':" in content and "'en-US':" in content:
        print("✅ 支持中英文双语")
    else:
        print("❌ 语言支持不完整")
        return False
    
    # 检查关键翻译项
    key_translations = [
        "page.title", "nav.title", "button.copy", "button.refresh",
        "status.copied", "error.no_email_to_copy", "welcome.title"
    ]
    
    missing_keys = []
    for key in key_translations:
        if f"'{key}'" in content:
            print(f"✅ 翻译键 '{key}' 存在")
        else:
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ 缺少翻译键: {missing_keys}")
        return False
    
    # 检查类定义
    if "class I18n" in content:
        print("✅ 国际化类定义正确")
    else:
        print("❌ 国际化类定义缺失")
        return False
    
    return True

def test_language_completeness():
    """测试语言包完整性"""
    print("\n🔍 测试语言包完整性...")

    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    i18n_file = project_root / "static/js/i18n.js"

    if not i18n_file.exists():
        assert False, f"i18n.js文件不存在: {i18n_file}"

    content = i18n_file.read_text(encoding='utf-8')
    
    # 提取中文翻译键
    zh_pattern = r"'zh-CN':\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}"
    zh_match = re.search(zh_pattern, content, re.DOTALL)
    
    if not zh_match:
        print("❌ 未找到中文翻译")
        return False
    
    zh_content = zh_match.group(1)
    zh_keys = re.findall(r"'([^']+)':", zh_content)
    
    # 提取英文翻译键
    en_pattern = r"'en-US':\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}"
    en_match = re.search(en_pattern, content, re.DOTALL)
    
    if not en_match:
        print("❌ 未找到英文翻译")
        return False
    
    en_content = en_match.group(1)
    en_keys = re.findall(r"'([^']+)':", en_content)
    
    print(f"✅ 中文翻译键数量: {len(zh_keys)}")
    print(f"✅ 英文翻译键数量: {len(en_keys)}")
    
    # 检查键的一致性
    zh_set = set(zh_keys)
    en_set = set(en_keys)
    
    missing_in_en = zh_set - en_set
    missing_in_zh = en_set - zh_set
    
    if missing_in_en:
        print(f"❌ 英文翻译中缺少的键: {missing_in_en}")
        return False
    
    if missing_in_zh:
        print(f"❌ 中文翻译中缺少的键: {missing_in_zh}")
        return False
    
    print("✅ 中英文翻译键完全一致")
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n🔍 测试文件结构...")

    # 获取项目根目录
    project_root = Path(__file__).parent.parent

    required_files = [
        "templates/index.html",
        "static/js/main.js",
        "static/js/i18n.js",
        "static/js/api-config.js"
    ]

    all_exist = True
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False

    assert all_exist, "部分必需文件不存在"
    return all_exist

def main():
    """主测试函数"""
    print("🚀 开始临时邮箱中文本地化测试\n")
    
    tests = [
        ("文件结构", test_file_structure),
        ("HTML模板中文化", test_html_localization),
        ("JavaScript中文化", test_javascript_localization),
        ("国际化模块", test_i18n_module),
        ("语言包完整性", test_language_completeness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}\n")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！中文本地化实施成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
