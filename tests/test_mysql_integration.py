#!/usr/bin/env python3
"""
MySQL集成测试
测试MySQL数据库适配器和相关功能
"""

import os
import sys
import pytest
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from database.mysql_config import MySQLConfig, MySQLConnectionManager
from database.mysql_schema import MySQLSchemaManager
from database.db_adapter import DatabaseManager, MySQLAdapter


class TestMySQLConfig:
    """测试MySQL配置类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = MySQLConfig()
        
        assert config.host == 'localhost'
        assert config.port == 3306
        assert config.database == 'tempmail'
        assert config.username == 'tempmail_user'
        assert config.charset == 'utf8mb4'
        assert config.collation == 'utf8mb4_unicode_ci'
    
    def test_config_from_env(self, monkeypatch):
        """测试从环境变量加载配置"""
        # 设置环境变量
        monkeypatch.setenv('MYSQL_HOST', 'testhost')
        monkeypatch.setenv('MYSQL_PORT', '3307')
        monkeypatch.setenv('MYSQL_DATABASE', 'testdb')
        monkeypatch.setenv('MYSQL_USERNAME', 'testuser')
        monkeypatch.setenv('MYSQL_PASSWORD', 'testpass')
        
        config = MySQLConfig()
        
        assert config.host == 'testhost'
        assert config.port == 3307
        assert config.database == 'testdb'
        assert config.username == 'testuser'
        assert config.password == 'testpass'
    
    def test_connection_url(self):
        """测试连接URL生成"""
        config = MySQLConfig()
        config.host = 'localhost'
        config.port = 3306
        config.database = 'testdb'
        config.username = 'testuser'
        config.password = 'testpass'
        
        url = config.get_connection_url()
        expected = "mysql+pymysql://testuser:testpass@localhost:3306/testdb?charset=utf8mb4&autocommit=true"
        assert url == expected
    
    def test_connection_url_without_database(self):
        """测试不包含数据库的连接URL"""
        config = MySQLConfig()
        config.host = 'localhost'
        config.port = 3306
        config.username = 'testuser'
        config.password = 'testpass'
        
        url = config.get_connection_url(include_database=False)
        expected = "mysql+pymysql://testuser:testpass@localhost:3306?charset=utf8mb4&autocommit=true"
        assert url == expected
    
    def test_config_validation(self):
        """测试配置验证"""
        config = MySQLConfig()
        
        # 有效配置
        config.host = 'localhost'
        config.database = 'testdb'
        config.username = 'testuser'
        config.password = 'testpass'
        config.port = 3306
        
        errors = config.validate()
        assert len(errors) == 0
        
        # 无效配置
        config.host = ''
        config.database = ''
        config.username = ''
        config.password = ''
        config.port = 0
        
        errors = config.validate()
        assert len(errors) > 0
        assert any('主机地址' in error for error in errors)
        assert any('数据库名' in error for error in errors)
        assert any('用户名' in error for error in errors)
        assert any('密码' in error for error in errors)
        assert any('端口号' in error for error in errors)


class TestMySQLConnectionManager:
    """测试MySQL连接管理器"""
    
    def test_connection_manager_init(self):
        """测试连接管理器初始化"""
        config = MySQLConfig()
        manager = MySQLConnectionManager(config)
        
        assert manager.config == config
        assert manager._engine is None
    
    @pytest.mark.skipif(
        not os.getenv('MYSQL_TEST_ENABLED'),
        reason="MySQL测试未启用，设置MYSQL_TEST_ENABLED=1启用"
    )
    def test_real_connection(self):
        """测试真实MySQL连接（需要配置测试数据库）"""
        config = MySQLConfig()
        # 使用测试数据库配置
        config.database = os.getenv('MYSQL_TEST_DATABASE', 'tempmail_test')
        
        manager = MySQLConnectionManager(config)
        
        # 测试连接
        success, message = manager.test_connection()
        if success:
            assert "连接成功" in message
        else:
            pytest.skip(f"MySQL连接失败: {message}")


class TestMySQLSchemaManager:
    """测试MySQL表结构管理器"""
    
    @pytest.mark.skipif(
        not os.getenv('MYSQL_TEST_ENABLED'),
        reason="MySQL测试未启用，设置MYSQL_TEST_ENABLED=1启用"
    )
    def test_schema_operations(self):
        """测试表结构操作"""
        config = MySQLConfig()
        config.database = os.getenv('MYSQL_TEST_DATABASE', 'tempmail_test')
        
        manager = MySQLConnectionManager(config)
        
        # 测试连接
        success, message = manager.test_connection()
        if not success:
            pytest.skip(f"MySQL连接失败: {message}")
        
        # 创建数据库
        if not manager.create_database_if_not_exists():
            pytest.skip("无法创建测试数据库")
        
        engine = manager.get_engine()
        schema_manager = MySQLSchemaManager(engine)
        
        # 删除现有表
        schema_manager.drop_tables()
        
        # 检查表不存在
        tables_status = schema_manager.check_tables_exist()
        assert not any(tables_status.values())
        
        # 创建表
        assert schema_manager.create_tables()
        
        # 检查表存在
        tables_status = schema_manager.check_tables_exist()
        assert all(tables_status.values())
        
        # 获取表信息
        tables_info = schema_manager.get_table_info()
        assert len(tables_info) == 3
        
        # 优化表
        assert schema_manager.optimize_tables()
        
        # 清理
        schema_manager.drop_tables()


class TestDatabaseManager:
    """测试数据库管理器"""
    
    def test_database_manager_init(self):
        """测试数据库管理器初始化"""
        manager = DatabaseManager()
        
        assert manager._adapter is None
        assert manager._db_type is None
    
    def test_sqlite_initialization(self, monkeypatch):
        """测试SQLite初始化"""
        monkeypatch.setenv('DATABASE_TYPE', 'sqlite')
        
        manager = DatabaseManager()
        
        # 使用临时数据库文件
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_db = f.name
        
        try:
            # 模拟Flask应用上下文
            class MockApp:
                config = {'DATABASE_PATH': temp_db}
            
            import database.db_adapter
            database.db_adapter.current_app = MockApp()
            
            assert manager.initialize('sqlite')
            assert manager.db_type == 'sqlite'
            assert manager._adapter is not None
            
        finally:
            os.unlink(temp_db)
    
    @pytest.mark.skipif(
        not os.getenv('MYSQL_TEST_ENABLED'),
        reason="MySQL测试未启用，设置MYSQL_TEST_ENABLED=1启用"
    )
    def test_mysql_initialization(self, monkeypatch):
        """测试MySQL初始化"""
        monkeypatch.setenv('DATABASE_TYPE', 'mysql')
        monkeypatch.setenv('MYSQL_DATABASE', 'tempmail_test')
        
        manager = DatabaseManager()
        
        if manager.initialize('mysql'):
            assert manager.db_type == 'mysql'
            assert manager._adapter is not None
            assert isinstance(manager._adapter, MySQLAdapter)
        else:
            pytest.skip("MySQL初始化失败")


class TestMySQLAdapter:
    """测试MySQL适配器"""
    
    @pytest.mark.skipif(
        not os.getenv('MYSQL_TEST_ENABLED'),
        reason="MySQL测试未启用，设置MYSQL_TEST_ENABLED=1启用"
    )
    def test_mysql_adapter_operations(self, monkeypatch):
        """测试MySQL适配器操作"""
        monkeypatch.setenv('MYSQL_DATABASE', 'tempmail_test')
        
        adapter = MySQLAdapter()
        
        # 测试连接
        success, message = adapter.connection_manager.test_connection()
        if not success:
            pytest.skip(f"MySQL连接失败: {message}")
        
        # 创建测试数据库和表
        adapter.connection_manager.create_database_if_not_exists()
        schema_manager = MySQLSchemaManager(adapter.connection_manager.get_engine())
        schema_manager.create_tables()
        
        try:
            # 测试查询操作
            result = adapter.execute_query("SELECT 1 as test")
            assert len(result) == 1
            assert result[0]['test'] == 1
            
            # 测试插入操作
            insert_query = """
                INSERT INTO temporary_emails (address, session_id, expires_at, created_at)
                VALUES (%s, %s, %s, %s)
            """
            
            from datetime import datetime, timezone
            now = datetime.now(timezone.utc)
            
            rows_affected = adapter.execute_update(
                insert_query,
                ('<EMAIL>', 'test_session', now, now)
            )
            assert rows_affected == 1
            
            # 测试查询插入的数据
            result = adapter.execute_query(
                "SELECT * FROM temporary_emails WHERE address = %s",
                ('<EMAIL>',)
            )
            assert len(result) == 1
            assert result[0]['address'] == '<EMAIL>'
            
        finally:
            # 清理测试数据
            adapter.execute_update("DELETE FROM temporary_emails WHERE address = %s", ('<EMAIL>',))


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
