# tests/conftest.py
"""
统一的测试配置文件
包含所有测试所需的fixtures和配置
"""
import os
import tempfile
import pytest
import sqlite3
import logging
import requests
from datetime import datetime, timedelta, timezone
import sys
from pathlib import Path

# 确保能从项目根目录导入 app 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app import app as flask_app, init_db_schema, get_db_connection

# 环境检测函数
def detect_environment():
    """检测当前运行环境"""
    if os.environ.get('CI'):
        return 'ci'
    elif os.environ.get('PYTEST_CURRENT_TEST'):
        return 'test'
    elif os.environ.get('FLASK_ENV') == 'development':
        return 'development'
    elif os.environ.get('FLASK_ENV') == 'production':
        return 'production'
    else:
        return 'development'  # 默认为开发环境

def is_server_available(url, timeout=5):
    """检查服务器是否可用"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200
    except Exception:
        return False

def is_database_available(db_path):
    """检查数据库是否可用"""
    try:
        if not db_path or not os.path.exists(str(db_path)):
            return False
        with sqlite3.connect(str(db_path)) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            return True
    except Exception:
        return False

# 设置测试环境变量
def setup_test_environment():
    """设置测试环境变量"""
    current_env = detect_environment()

    # 基础环境变量配置
    test_env_vars = {
        'TEST_BASE_URL': os.environ.get('TEST_BASE_URL', 'http://127.0.0.1:5001'),
        'TEST_DATABASE_PATH': os.environ.get('TEST_DATABASE_PATH', 'tests/database/test_tempmail.db'),
        'TEST_DOMAIN_NAME': os.environ.get('TEST_DOMAIN_NAME', 'test.local'),
        'TEST_EMAIL_EXPIRATION_HOURS': os.environ.get('TEST_EMAIL_EXPIRATION_HOURS', '1'),
        'TEST_API_TIMEOUT': os.environ.get('TEST_API_TIMEOUT', '30'),
        'TEST_API_RETRY_ATTEMPTS': os.environ.get('TEST_API_RETRY_ATTEMPTS', '3'),
        'TEST_API_RETRY_DELAY': os.environ.get('TEST_API_RETRY_DELAY', '1'),
        'TEST_ENVIRONMENT': current_env,
    }

    # 根据环境调整配置
    if current_env == 'ci':
        test_env_vars.update({
            'TEST_API_TIMEOUT': '60',  # CI环境增加超时时间
            'TEST_SKIP_EXTERNAL_DEPS': 'true',  # CI环境跳过外部依赖
        })
    elif current_env == 'production':
        test_env_vars.update({
            'TEST_SKIP_DESTRUCTIVE_TESTS': 'true',  # 生产环境跳过破坏性测试
        })

    for key, value in test_env_vars.items():
        if key not in os.environ:
            os.environ[key] = value

# 在导入时设置环境变量
setup_test_environment()

# 环境检测fixtures
@pytest.fixture(scope='session')
def test_environment():
    """获取当前测试环境"""
    return detect_environment()

@pytest.fixture(scope='session')
def server_available():
    """检查测试服务器是否可用"""
    base_url = os.environ.get('TEST_BASE_URL', 'http://127.0.0.1:8080')
    return is_server_available(base_url)

@pytest.fixture(scope='session')
def database_available():
    """检查测试数据库是否可用"""
    db_path = os.environ.get('TEST_DATABASE_PATH', 'tests/database/test_tempmail.db')
    return is_database_available(db_path)

# 跳过条件fixtures
@pytest.fixture(scope='session')
def skip_if_no_server():
    """如果服务器不可用则跳过测试的条件"""
    base_url = os.environ.get('TEST_BASE_URL', 'http://127.0.0.1:5001')
    return pytest.mark.skipif(
        not is_server_available(base_url),
        reason=f"测试服务器不可用: {base_url}"
    )

@pytest.fixture(scope='session')
def skip_if_no_external_deps():
    """如果设置跳过外部依赖则跳过测试的条件"""
    return pytest.mark.skipif(
        os.environ.get('TEST_SKIP_EXTERNAL_DEPS', '').lower() == 'true',
        reason="跳过外部依赖测试"
    )

@pytest.fixture(scope='session')
def skip_if_production():
    """如果是生产环境则跳过破坏性测试的条件"""
    return pytest.mark.skipif(
        os.environ.get('TEST_SKIP_DESTRUCTIVE_TESTS', '').lower() == 'true',
        reason="生产环境跳过破坏性测试"
    )

# 注册自定义测试标记
def pytest_configure(config):
    """注册自定义测试标记"""
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "functional: 功能测试")
    config.addinivalue_line("markers", "api: API测试")
    config.addinivalue_line("markers", "frontend: 前端测试")
    config.addinivalue_line("markers", "mail_handler: 邮件处理器测试")
    config.addinivalue_line("markers", "cleanup: 清理脚本测试")
    config.addinivalue_line("markers", "performance: 性能测试")
    config.addinivalue_line("markers", "slow: 慢速测试")
    config.addinivalue_line("markers", "requires_server: 需要服务器连接的测试")
    config.addinivalue_line("markers", "requires_external_deps: 需要外部依赖的测试")
    config.addinivalue_line("markers", "destructive: 破坏性测试（可能影响数据）")
    config.addinivalue_line("markers", "stress: 压力测试")
    config.addinivalue_line("markers", "environment_adaptive: 环境自适应测试")


@pytest.fixture(scope='module')
def app_instance():
    """
    创建并配置一个新的 Flask app 实例用于每个测试模块。
    使用临时的 SQLite 文件作为数据库。
    """
    # 创建一个临时的数据库文件
    db_fd, db_path = tempfile.mkstemp(suffix='.db', prefix='test_tempmail_')
    db_path = Path(db_path)  # Ensure pathlib.Path object for compatibility

    # 配置 Flask app 进行测试
    flask_app.config.update({
        "TESTING": True,
        "DATABASE_PATH": db_path,
        "DOMAIN_NAME": "test.local", # 测试时使用的域名
        "EMAIL_EXPIRATION_HOURS": 1, # 测试时使用的过期小时数
        # 如果有其他需要为测试覆盖的配置，也在这里添加
    })

    # 在应用上下文中初始化测试数据库的表结构
    with flask_app.app_context():
        init_db_schema()

    yield flask_app # 提供 app 实例给测试函数

    # 测试完成后清理：关闭文件描述符并删除临时数据库文件
    os.close(db_fd)
    os.unlink(str(db_path))


@pytest.fixture(autouse=True)
def force_stream_logger(app_instance):
    """配置测试日志记录器"""
    for handler in list(app_instance.logger.handlers):
        app_instance.logger.removeHandler(handler)
    stream_handler = logging.StreamHandler(sys.stderr)
    stream_handler.setLevel(logging.ERROR)
    formatter = logging.Formatter('%(message)s')
    stream_handler.setFormatter(formatter)
    app_instance.logger.addHandler(stream_handler)
    app_instance.logger.setLevel(logging.ERROR)
    app_instance.logger.propagate = True  # 关键：让日志冒泡到 pytest caplog
    yield
    for handler in list(app_instance.logger.handlers):
        app_instance.logger.removeHandler(handler)


@pytest.fixture(scope='module')
def client(app_instance):
    """提供一个 Flask 测试客户端"""
    return app_instance.test_client()


@pytest.fixture(scope='function')
def db_conn(app_instance):
    """提供一个数据库连接，并在测试后关闭"""
    with app_instance.app_context():
        connection = get_db_connection()
        yield connection
        connection.close()


@pytest.fixture
def temp_db_path():
    """创建临时数据库文件路径"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        db_path = f.name
    yield db_path
    # 清理临时文件
    if os.path.exists(db_path):
        os.unlink(db_path)


@pytest.fixture
def clear_rate_limit():
    """清理速率限制存储的fixture"""
    def _clear():
        try:
            from app import rate_limit
            if hasattr(rate_limit, "clear"):
                rate_limit.clear()
        except (ImportError, AttributeError):
            pass

    _clear()  # 测试前清理
    yield _clear
    _clear()  # 测试后清理