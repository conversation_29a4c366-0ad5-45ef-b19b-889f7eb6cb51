#!/usr/bin/env python3
"""
环境自适应测试运行器
根据当前环境自动调整测试配置和执行策略
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from tests.utils.environment_adapter import env_adapter
    from tests.utils.test_environment_validator import TestEnvironmentValidator
    HAS_ENV_ADAPTER = True
except ImportError:
    HAS_ENV_ADAPTER = False


class AdaptiveTestRunner:
    """环境自适应测试运行器"""
    
    def __init__(self):
        self.environment = self._detect_environment()
        self.validator = TestEnvironmentValidator() if HAS_ENV_ADAPTER else None
        
    def _detect_environment(self):
        """检测当前环境"""
        if os.environ.get('CI'):
            return 'ci'
        elif os.environ.get('PYTEST_CURRENT_TEST'):
            return 'test'
        elif os.environ.get('FLASK_ENV') == 'production':
            return 'production'
        else:
            return 'development'
    
    def validate_environment(self):
        """验证环境"""
        if not self.validator:
            print("⚠️  环境适配工具不可用，跳过环境验证")
            return True
        
        print("🔍 验证测试环境...")
        success, data = self.validator.validate_all()
        
        if not success:
            print("❌ 环境验证失败:")
            for error in data['errors']:
                print(f"  {error}")
            return False
        
        if data['warnings']:
            print("⚠️  环境警告:")
            for warning in data['warnings']:
                print(f"  {warning}")
        
        print("✅ 环境验证通过")
        return True
    
    def get_pytest_args(self, test_type='all', extra_args=None):
        """根据环境和测试类型生成pytest参数"""
        args = ['pytest', '-v']
        
        # 根据环境调整配置
        if self.environment == 'ci':
            args.extend([
                '--tb=short',
                '--maxfail=5',
                '-x',  # 遇到第一个失败就停止
            ])
            # CI环境跳过某些测试
            os.environ['TEST_SKIP_EXTERNAL_DEPS'] = 'true'
            os.environ['TEST_SKIP_SLOW_TESTS'] = 'true'
            
        elif self.environment == 'production':
            args.extend([
                '--tb=line',
                '--maxfail=1',
            ])
            # 生产环境跳过破坏性测试
            os.environ['TEST_SKIP_DESTRUCTIVE_TESTS'] = 'true'
            os.environ['TEST_SKIP_PERFORMANCE_TESTS'] = 'true'
            
        else:  # development
            args.extend([
                '--tb=short',
                '--capture=no',  # 开发环境显示print输出
            ])
        
        # 根据测试类型添加标记过滤器
        if test_type == 'fast':
            args.extend(['-m', 'not slow and not stress'])
        elif test_type == 'unit':
            args.extend(['-m', 'unit'])
        elif test_type == 'integration':
            args.extend(['-m', 'integration'])
        elif test_type == 'api':
            args.extend(['-m', 'api'])
        elif test_type == 'performance':
            args.extend(['-m', 'performance'])
        elif test_type == 'stress':
            args.extend(['-m', 'stress'])
        elif test_type == 'frontend':
            args.extend(['-m', 'frontend'])
        elif test_type == 'requires_server':
            args.extend(['-m', 'requires_server'])
        elif test_type == 'no_external_deps':
            args.extend(['-m', 'not requires_external_deps'])
        elif test_type == 'environment_adaptive':
            args.extend(['-m', 'environment_adaptive'])
        # 'all' 类型不添加过滤器
        
        # 添加额外参数
        if extra_args:
            args.extend(extra_args)
        
        return args
    
    def run_tests(self, test_type='all', extra_args=None, validate_env=True):
        """运行测试"""
        print(f"🚀 启动环境自适应测试运行器")
        print(f"📍 当前环境: {self.environment}")
        print(f"🎯 测试类型: {test_type}")
        
        # 验证环境
        if validate_env and not self.validate_environment():
            print("❌ 环境验证失败，无法运行测试")
            return 1
        
        # 生成pytest参数
        pytest_args = self.get_pytest_args(test_type, extra_args)
        
        print(f"🔧 pytest命令: {' '.join(pytest_args)}")
        print("=" * 60)
        
        # 运行测试
        try:
            result = subprocess.run(pytest_args, cwd=project_root)
            return result.returncode
        except KeyboardInterrupt:
            print("\n⚠️  测试被用户中断")
            return 130
        except Exception as e:
            print(f"❌ 运行测试时出错: {e}")
            return 1
    
    def list_test_types(self):
        """列出可用的测试类型"""
        test_types = {
            'all': '运行所有测试',
            'fast': '快速测试（排除慢速和压力测试）',
            'unit': '单元测试',
            'integration': '集成测试',
            'api': 'API测试',
            'performance': '性能测试',
            'stress': '压力测试',
            'frontend': '前端测试',
            'requires_server': '需要服务器的测试',
            'no_external_deps': '不需要外部依赖的测试',
            'environment_adaptive': '环境自适应测试',
        }
        
        print("📋 可用的测试类型:")
        for test_type, description in test_types.items():
            print(f"  {test_type:<20} - {description}")
    
    def show_environment_info(self):
        """显示环境信息"""
        print("🌍 环境信息:")
        print(f"  当前环境: {self.environment}")
        print(f"  Python版本: {sys.version}")
        print(f"  工作目录: {os.getcwd()}")
        print(f"  项目根目录: {project_root}")
        
        # 显示关键环境变量
        env_vars = [
            'TEST_BASE_URL',
            'TEST_DATABASE_PATH',
            'TEST_DOMAIN_NAME',
            'TEST_SKIP_EXTERNAL_DEPS',
            'TEST_SKIP_DESTRUCTIVE_TESTS',
            'TEST_SKIP_SLOW_TESTS',
            'CI',
            'FLASK_ENV',
        ]
        
        print("\n📝 环境变量:")
        for var in env_vars:
            value = os.environ.get(var, '(未设置)')
            print(f"  {var}: {value}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='环境自适应测试运行器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python tests/run_adaptive_tests.py                    # 运行所有测试
  python tests/run_adaptive_tests.py --type fast        # 运行快速测试
  python tests/run_adaptive_tests.py --type api         # 运行API测试
  python tests/run_adaptive_tests.py --list-types       # 列出测试类型
  python tests/run_adaptive_tests.py --env-info         # 显示环境信息
  python tests/run_adaptive_tests.py --no-validate      # 跳过环境验证
        """
    )
    
    parser.add_argument(
        '--type', '-t',
        default='all',
        help='测试类型 (默认: all)'
    )
    
    parser.add_argument(
        '--list-types',
        action='store_true',
        help='列出可用的测试类型'
    )
    
    parser.add_argument(
        '--env-info',
        action='store_true',
        help='显示环境信息'
    )
    
    parser.add_argument(
        '--no-validate',
        action='store_true',
        help='跳过环境验证'
    )
    
    parser.add_argument(
        'pytest_args',
        nargs='*',
        help='传递给pytest的额外参数'
    )
    
    args = parser.parse_args()
    
    runner = AdaptiveTestRunner()
    
    if args.list_types:
        runner.list_test_types()
        return 0
    
    if args.env_info:
        runner.show_environment_info()
        return 0
    
    return runner.run_tests(
        test_type=args.type,
        extra_args=args.pytest_args,
        validate_env=not args.no_validate
    )


if __name__ == "__main__":
    sys.exit(main())
