#!/usr/bin/env python3
"""
临时邮箱MVP项目简化压力测试脚本
测试系统在多个邮箱并发使用情况下的邮件处理能力

功能：
1. 创建多个临时邮箱（10-100个）
2. 向每个邮箱发送不同数量的邮件
3. 测试邮件接收、存储、检索的性能
4. 记录详细的性能数据和错误日志
5. 生成测试报告

作者：Augment Agent
日期：2025-01-27
"""

import os
import sys
import time
import json
import requests
import statistics
import random
from datetime import datetime
from pathlib import Path
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

class SimpleStressTest:
    """简化的压力测试类"""
    
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 30
        self.test_session_id = f"stress-test-{int(time.time())}"
        self.test_results = []
        
    def create_mailbox(self, custom_prefix=None):
        """创建临时邮箱"""
        start_time = time.time()
        
        try:
            data = {"session_id": self.test_session_id}
            if custom_prefix:
                data["custom_prefix"] = custom_prefix
            
            response = self.session.post(
                f"{self.base_url}/api/generate-address",
                json=data,
                timeout=10
            )
            
            elapsed_time = time.time() - start_time
            
            if response.status_code == 201:
                result = response.json()
                if result.get("success"):
                    return True, result["data"]["address"], elapsed_time
            
            return False, "", elapsed_time
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"创建邮箱异常: {e}")
            return False, "", elapsed_time
    
    def get_emails(self, email_address):
        """获取邮箱邮件"""
        start_time = time.time()
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/emails",
                params={"address": email_address},
                timeout=10
            )
            
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    emails = result.get("data", {}).get("emails", [])
                    return True, len(emails), elapsed_time
            
            return False, 0, elapsed_time
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"获取邮件异常: {e}")
            return False, 0, elapsed_time
    
    def simulate_email_sending(self, email_address, count=1):
        """模拟发送邮件（通过外部SMTP或API）"""
        # 这里可以实现实际的邮件发送逻辑
        # 为了简化，我们只是等待一段时间模拟邮件到达
        time.sleep(0.1 * count)  # 模拟邮件发送和到达的延迟
        return True
    
    def run_test(self, mailbox_count=10, emails_per_mailbox=5):
        """运行单个测试"""
        print(f"开始测试: {mailbox_count} 个邮箱，每个邮箱 {emails_per_mailbox} 封邮件")
        
        test_start_time = time.time()
        
        # 阶段1: 创建邮箱
        print("创建邮箱...")
        mailboxes = []
        mailbox_creation_times = []
        
        for i in range(mailbox_count):
            prefix = f"test-{i}-{int(time.time())}"
            success, address, elapsed_time = self.create_mailbox(prefix)
            mailbox_creation_times.append(elapsed_time)
            
            if success:
                mailboxes.append(address)
                print(f"  创建邮箱成功: {address}")
            else:
                print(f"  创建邮箱失败: {prefix}")
        
        print(f"成功创建 {len(mailboxes)} / {mailbox_count} 个邮箱")
        
        # 阶段2: 模拟发送邮件
        print("模拟发送邮件...")
        total_emails_sent = 0
        
        for address in mailboxes:
            # 随机发送1-emails_per_mailbox封邮件
            email_count = random.randint(1, emails_per_mailbox)
            if self.simulate_email_sending(address, email_count):
                total_emails_sent += email_count
                print(f"  向 {address} 发送了 {email_count} 封邮件")
        
        # 等待邮件处理
        print("等待邮件处理...")
        time.sleep(2)
        
        # 阶段3: 检索邮件
        print("检索邮件...")
        email_retrieval_times = []
        total_emails_retrieved = 0
        
        for address in mailboxes:
            success, email_count, elapsed_time = self.get_emails(address)
            email_retrieval_times.append(elapsed_time)
            
            if success:
                total_emails_retrieved += email_count
                print(f"  从 {address} 检索到 {email_count} 封邮件")
            else:
                print(f"  从 {address} 检索邮件失败")
        
        total_test_time = time.time() - test_start_time
        
        # 计算统计数据
        result = {
            "timestamp": datetime.now().isoformat(),
            "mailbox_count": mailbox_count,
            "created_mailboxes": len(mailboxes),
            "emails_per_mailbox_target": emails_per_mailbox,
            "total_emails_sent": total_emails_sent,
            "total_emails_retrieved": total_emails_retrieved,
            "total_test_time": total_test_time,
            "avg_mailbox_creation_time": statistics.mean(mailbox_creation_times) if mailbox_creation_times else 0,
            "avg_email_retrieval_time": statistics.mean(email_retrieval_times) if email_retrieval_times else 0,
            "mailbox_creation_success_rate": len(mailboxes) / mailbox_count * 100,
            "email_retrieval_success_rate": len([t for t in email_retrieval_times if t > 0]) / len(mailboxes) * 100 if mailboxes else 0
        }
        
        self.test_results.append(result)
        
        print(f"测试完成:")
        print(f"  总时间: {total_test_time:.2f}秒")
        print(f"  邮箱创建成功率: {result['mailbox_creation_success_rate']:.1f}%")
        print(f"  邮件检索成功率: {result['email_retrieval_success_rate']:.1f}%")
        print(f"  发送邮件: {total_emails_sent} 封")
        print(f"  检索邮件: {total_emails_retrieved} 封")
        print()
        
        return result
    
    def run_stress_test(self, min_mailboxes=10, max_mailboxes=50, step_size=10, emails_per_mailbox=5):
        """运行完整的压力测试"""
        print("开始压力测试...")
        print(f"配置: {min_mailboxes}-{max_mailboxes} 邮箱，步长 {step_size}，每邮箱 {emails_per_mailbox} 封邮件")
        print("=" * 60)

        for mailbox_count in range(min_mailboxes, max_mailboxes + 1, step_size):
            try:
                self.run_test(mailbox_count, emails_per_mailbox)
                # 等待速率限制重置（60秒窗口）
                if mailbox_count < max_mailboxes:
                    print(f"等待速率限制重置... (60秒)")
                    time.sleep(65)  # 等待65秒确保速率限制重置
            except Exception as e:
                print(f"测试 {mailbox_count} 个邮箱时发生错误: {e}")
                continue

        print("压力测试完成!")
        return self.test_results
    
    def generate_report(self):
        """生成测试报告"""
        if not self.test_results:
            return "没有测试结果"
        
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("临时邮箱MVP项目压力测试报告")
        report_lines.append("=" * 60)
        report_lines.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"测试会话ID: {self.test_session_id}")
        report_lines.append("")
        
        # 汇总统计
        max_mailboxes = max(r["mailbox_count"] for r in self.test_results)
        max_emails = max(r["total_emails_sent"] for r in self.test_results)
        avg_creation_success = statistics.mean(r["mailbox_creation_success_rate"] for r in self.test_results)
        avg_retrieval_success = statistics.mean(r["email_retrieval_success_rate"] for r in self.test_results)
        
        report_lines.append("测试结果汇总:")
        report_lines.append(f"  最大处理邮箱数: {max_mailboxes}")
        report_lines.append(f"  最大处理邮件数: {max_emails}")
        report_lines.append(f"  平均邮箱创建成功率: {avg_creation_success:.1f}%")
        report_lines.append(f"  平均邮件检索成功率: {avg_retrieval_success:.1f}%")
        report_lines.append("")
        
        # 详细结果
        report_lines.append("详细测试结果:")
        report_lines.append(f"{'邮箱数':<8} {'创建成功':<10} {'发送邮件':<10} {'检索邮件':<10} {'总时间(秒)':<12}")
        report_lines.append("-" * 60)
        
        for result in self.test_results:
            report_lines.append(
                f"{result['mailbox_count']:<8} "
                f"{result['created_mailboxes']:<10} "
                f"{result['total_emails_sent']:<10} "
                f"{result['total_emails_retrieved']:<10} "
                f"{result['total_test_time']:<12.2f}"
            )
        
        report_lines.append("")
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
    
    def save_results(self, filename=None):
        """保存测试结果到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"simple_stress_test_results_{timestamp}.json"
        
        data = {
            "test_session_id": self.test_session_id,
            "test_results": self.test_results,
            "summary": {
                "total_tests": len(self.test_results),
                "max_mailboxes": max(r["mailbox_count"] for r in self.test_results) if self.test_results else 0,
                "max_emails": max(r["total_emails_sent"] for r in self.test_results) if self.test_results else 0,
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"测试结果已保存到: {filename}")
        return filename


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="临时邮箱MVP项目简化压力测试工具")
    
    parser.add_argument('--min-mailboxes', type=int, default=10, help='最小邮箱数量')
    parser.add_argument('--max-mailboxes', type=int, default=50, help='最大邮箱数量')
    parser.add_argument('--step-size', type=int, default=10, help='邮箱数量步长')
    parser.add_argument('--emails-per-mailbox', type=int, default=5, help='每个邮箱的邮件数量')
    parser.add_argument('--base-url', type=str, default='http://127.0.0.1:5000', help='应用基础URL')
    parser.add_argument('--quick-test', action='store_true', help='快速测试模式')
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick_test:
        args.min_mailboxes = 5
        args.max_mailboxes = 15
        args.step_size = 5
        args.emails_per_mailbox = 3
    
    # 创建测试实例
    test = SimpleStressTest(args.base_url)
    
    try:
        print("简化压力测试工具")
        print(f"目标URL: {args.base_url}")
        print(f"测试范围: {args.min_mailboxes}-{args.max_mailboxes} 邮箱")
        print(f"每邮箱邮件数: {args.emails_per_mailbox}")
        print("-" * 60)
        
        # 运行压力测试
        results = test.run_stress_test(
            min_mailboxes=args.min_mailboxes,
            max_mailboxes=args.max_mailboxes,
            step_size=args.step_size,
            emails_per_mailbox=args.emails_per_mailbox
        )
        
        # 生成报告
        report = test.generate_report()
        print(report)
        
        # 保存结果
        json_file = test.save_results()
        
        # 保存报告到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"simple_stress_test_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"报告已保存到: {report_file}")
        print("\n压力测试完成！")
        return 0
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
