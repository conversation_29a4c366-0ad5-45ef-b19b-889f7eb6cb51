#!/usr/bin/env python3
"""
压力测试服务器启动脚本
专门为压力测试优化的Flask应用启动器

功能：
1. 加载压力测试专用配置
2. 禁用所有速率限制
3. 优化数据库性能
4. 启用高并发模式

使用方法：
python start_stress_test_server.py [--port PORT] [--host HOST]

作者：Augment Agent
日期：2025-01-27
"""

import os
import sys
import argparse
from pathlib import Path
from dotenv import load_dotenv

# 确保在项目根目录
project_root = Path(__file__).resolve().parent
os.chdir(project_root)
sys.path.insert(0, str(project_root))

def setup_stress_test_environment():
    """设置压力测试环境"""
    print("🚀 设置压力测试环境...")
    
    # 加载压力测试配置
    stress_test_env = project_root / '.env.stress_test'
    if stress_test_env.exists():
        load_dotenv(stress_test_env, override=True)
        print(f"✅ 已加载压力测试配置: {stress_test_env}")
    else:
        print(f"⚠️  压力测试配置文件不存在: {stress_test_env}")
        print("使用默认配置...")
    
    # 强制设置压力测试模式
    os.environ['STRESS_TESTING'] = 'true'
    os.environ['DISABLE_RATE_LIMIT'] = 'true'
    os.environ['TESTING'] = 'true'
    
    # 优化数据库配置
    if not os.environ.get('DATABASE_PATH'):
        os.environ['DATABASE_PATH'] = ':memory:'
        print("📊 使用内存数据库以获得最佳性能")
    
    # 设置域名（如果未设置）
    if not os.environ.get('DOMAIN_NAME'):
        os.environ['DOMAIN_NAME'] = 'stress-test.local'
    
    # 禁用详细日志
    os.environ['LOG_LEVEL'] = 'ERROR'
    
    print("✅ 压力测试环境配置完成")
    print(f"   - 速率限制: 已禁用")
    print(f"   - 数据库: {os.environ.get('DATABASE_PATH', '默认')}")
    print(f"   - 域名: {os.environ.get('DOMAIN_NAME')}")
    print(f"   - 日志级别: {os.environ.get('LOG_LEVEL', 'INFO')}")

def start_flask_app(host='127.0.0.1', port=5000):
    """启动Flask应用"""
    print(f"\n🌐 启动压力测试服务器...")
    print(f"   - 地址: http://{host}:{port}")
    print(f"   - 模式: 压力测试模式")
    print(f"   - 并发优化: 已启用")
    print("=" * 50)
    
    # 导入并启动应用
    try:
        from app import app, init_db_schema

        # 确保压力测试配置生效
        app.config['STRESS_TESTING'] = True
        app.config['DISABLE_RATE_LIMIT'] = True
        app.config['TESTING'] = True
        app.config['DEBUG'] = False

        # 在应用上下文中初始化数据库
        with app.app_context():
            init_db_schema()
            print("✅ 数据库表结构初始化完成")

        # 启动应用
        app.run(
            host=host,
            port=port,
            debug=False,
            threaded=True,
            processes=1,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="压力测试服务器启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 默认启动（127.0.0.1:5000）
  python start_stress_test_server.py
  
  # 指定端口
  python start_stress_test_server.py --port 8080
  
  # 指定主机和端口
  python start_stress_test_server.py --host 0.0.0.0 --port 8080
  
注意：
  - 此脚本会自动禁用所有速率限制
  - 使用内存数据库以获得最佳性能
  - 仅用于开发和测试环境
        """
    )
    
    parser.add_argument(
        '--host', type=str, default='127.0.0.1',
        help='服务器主机地址 (默认: 127.0.0.1)'
    )
    parser.add_argument(
        '--port', type=int, default=8080,
        help='服务器端口 (默认: 8080)'
    )
    parser.add_argument(
        '--no-memory-db', action='store_true',
        help='不使用内存数据库（使用文件数据库）'
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if args.port < 1 or args.port > 65535:
        print("❌ 错误: 端口必须在 1-65535 范围内")
        return 1
    
    print("🧪 临时邮箱MVP - 压力测试服务器")
    print("=" * 50)
    
    # 设置环境
    setup_stress_test_environment()
    
    # 如果用户指定不使用内存数据库
    if args.no_memory_db:
        os.environ['DATABASE_PATH'] = 'database/tempmail_stress_test.db'
        print(f"📁 使用文件数据库: {os.environ['DATABASE_PATH']}")
    
    # 启动服务器
    start_flask_app(args.host, args.port)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
