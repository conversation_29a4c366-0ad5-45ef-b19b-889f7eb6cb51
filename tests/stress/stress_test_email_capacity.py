#!/usr/bin/env python3
"""
临时邮箱MVP项目压力测试脚本
测试系统在多个邮箱并发使用情况下的邮件处理能力

功能：
1. 创建多个临时邮箱（10-1000个）
2. 向每个邮箱发送不同数量的邮件
3. 测试邮件接收、存储、检索的性能
4. 记录详细的性能数据和错误日志
5. 生成测试报告

作者：Augment Agent
日期：2025-01-27
"""

import os
import sys
import time
import json
import sqlite3
import threading
import concurrent.futures
import requests
import psutil
import statistics
import random
from datetime import datetime, timezone, timedelta
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
import logging
import argparse
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional, Tuple
import uuid

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
try:
    from app import get_db_connection
    # 不再使用有问题的mail_handler模块，改用简化的邮件存储函数
    # from tests.mail_handler import store_email, EX_OK

    # 简化的邮件存储函数
    def store_email_simple(db_path, recipient_address, sender, subject, body_text, body_html):
        """简化的邮件存储函数，避免mail_handler.py的复杂配置"""
        import sqlite3
        import datetime

        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 查找邮箱
            cursor.execute("SELECT id, expires_at FROM temporary_emails WHERE address = ?", (recipient_address,))
            email_record = cursor.fetchone()

            if not email_record:
                conn.close()
                return 67  # EX_NOUSER

            email_id, expires_at_str = email_record[0], email_record[1]

            # 检查过期时间
            try:
                expires_at_dt = datetime.datetime.fromisoformat(expires_at_str)
                if expires_at_dt.tzinfo is None:
                    expires_at_dt = expires_at_dt.replace(tzinfo=datetime.timezone.utc)
            except ValueError:
                conn.close()
                return 65  # EX_DATAERR

            now_utc = datetime.datetime.now(datetime.timezone.utc)
            if now_utc > expires_at_dt:
                conn.close()
                return 67  # EX_NOUSER

            # 存储邮件
            current_time_iso = now_utc.isoformat()
            cursor.execute(
                """INSERT INTO received_mails (email_address_id, sender, subject, body_text, body_html, received_at)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (email_id, sender, subject, body_text, body_html, current_time_iso)
            )
            conn.commit()
            conn.close()

            return 0  # EX_OK

        except Exception:
            if 'conn' in locals():
                conn.close()
            return 75  # EX_TEMPFAIL

    # 简化的数据库初始化函数
    def init_db_schema():
        """初始化数据库架构"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                # 创建临时邮箱表（如果不存在）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS temporary_emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        address TEXT UNIQUE NOT NULL,
                        session_id TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1
                    )
                ''')

                # 创建邮件表（如果不存在）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS received_mails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email_address_id INTEGER NOT NULL,
                        sender TEXT NOT NULL,
                        subject TEXT,
                        body_text TEXT,
                        body_html TEXT,
                        received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_read BOOLEAN DEFAULT 0,
                        FOREIGN KEY (email_address_id) REFERENCES temporary_emails (id)
                    )
                ''')

                conn.commit()

        except Exception as e:
            print(f"数据库初始化失败: {e}")
            raise

except ImportError as e:
    print(f"无法导入项目模块: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

@dataclass
class TestConfig:
    """测试配置"""
    min_mailboxes: int = 10
    max_mailboxes: int = 100
    step_size: int = 10
    emails_per_mailbox_min: int = 1
    emails_per_mailbox_max: int = 50
    concurrent_workers: int = 5
    test_duration_seconds: int = 300
    base_url: str = "http://127.0.0.1:5000"
    domain_name: str = "develop.local"
    cleanup_after_test: bool = True
    output_dir: str = "stress_test_results"

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: str
    mailbox_count: int
    total_emails: int
    emails_per_mailbox: float
    
    # 时间指标（秒）
    mailbox_creation_time: float
    email_sending_time: float
    email_retrieval_time: float
    total_test_time: float
    
    # 成功率指标
    mailbox_creation_success_rate: float
    email_storage_success_rate: float
    email_retrieval_success_rate: float
    
    # 系统资源指标
    peak_memory_mb: float
    peak_cpu_percent: float
    database_size_mb: float
    
    # 响应时间指标（毫秒）
    avg_mailbox_creation_time_ms: float
    avg_email_storage_time_ms: float
    avg_email_retrieval_time_ms: float
    
    # 错误统计
    errors: Dict[str, int]

class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.session = requests.Session()
        self.session.timeout = 30

        # 创建输出目录
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 设置日志
        self.setup_logging()
        
        # 测试数据存储
        self.test_results: List[PerformanceMetrics] = []
        self.test_mailboxes: List[str] = []
        self.test_session_id = str(uuid.uuid4())
        
        # 性能监控
        self.process = psutil.Process()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024
        
    def setup_logging(self):
        """设置日志记录"""
        log_file = self.output_dir / f"stress_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        self.logger.info("检查系统要求...")

        # 检查可用内存
        memory = psutil.virtual_memory()
        if memory.available < 1024 * 1024 * 1024:  # 1GB
            self.logger.warning("可用内存不足1GB，测试可能受到影响")

        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        if disk.free < 1024 * 1024 * 1024:  # 1GB
            self.logger.warning("可用磁盘空间不足1GB，测试可能受到影响")

        # 检查应用是否运行 - 尝试多个端点
        health_endpoints = [
            f"{self.config.base_url}/",
            f"{self.config.base_url}/api/emails?address=<EMAIL>"
        ]

        app_running = False
        for endpoint in health_endpoints:
            try:
                response = self.session.get(endpoint, timeout=10)
                if response.status_code in [200, 400]:  # 400也表示应用在运行
                    app_running = True
                    self.logger.info(f"应用运行检查通过: {endpoint}")
                    break
            except requests.exceptions.RequestException as e:
                self.logger.debug(f"检查端点 {endpoint} 失败: {e}")
                continue

        if not app_running:
            self.logger.error("无法连接到应用，请确保应用正在运行")
            self.logger.info("提示：可以使用 'python start_stress_test_server.py' 启动压力测试服务器")
            return False

        self.logger.info("系统要求检查通过")
        return True
    
    def create_mailbox(self, custom_prefix: Optional[str] = None) -> Tuple[bool, str, float]:
        """创建临时邮箱"""
        start_time = time.time()
        
        try:
            data = {"session_id": self.test_session_id}
            if custom_prefix:
                data["custom_prefix"] = custom_prefix
            
            response = self.session.post(
                f"{self.config.base_url}/api/generate-address",
                json=data,
                timeout=10
            )
            
            elapsed_time = time.time() - start_time
            
            if response.status_code == 201:
                result = response.json()
                if result.get("success"):
                    email_address = result["data"]["address"]
                    self.test_mailboxes.append(email_address)
                    return True, email_address, elapsed_time
            
            self.logger.error(f"创建邮箱失败: {response.status_code} - {response.text}")
            return False, "", elapsed_time
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.logger.error(f"创建邮箱异常: {e}")
            return False, "", elapsed_time
    
    def simulate_email_storage(self, email_address: str, sender: str, subject: str, body: str) -> Tuple[bool, float]:
        """模拟邮件存储（直接调用存储函数）"""
        start_time = time.time()
        
        try:
            # 确保使用压力测试专用的数据库路径
            db_path = 'database/tempmail_stress_test.db'

            # 设置环境变量以保持一致性
            os.environ['DATABASE_PATH'] = db_path

            # 使用简化的邮件存储函数
            result = store_email_simple(
                db_path=db_path,
                recipient_address=email_address,
                sender=sender,
                subject=subject,
                body_text=body,
                body_html=f"<p>{body}</p>"
            )

            elapsed_time = time.time() - start_time
            success = (result == 0)  # 0 = EX_OK
            
            if not success:
                self.logger.error(f"邮件存储失败: {email_address}, 错误码: {result}")
            
            return success, elapsed_time
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.logger.error(f"邮件存储异常: {e}")
            return False, elapsed_time
    
    def retrieve_emails(self, email_address: str) -> Tuple[bool, int, float]:
        """检索邮件"""
        start_time = time.time()
        
        try:
            response = self.session.get(
                f"{self.config.base_url}/api/emails",
                params={"address": email_address},
                timeout=10
            )
            
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    emails = result.get("data", {}).get("emails", [])
                    return True, len(emails), elapsed_time
            
            self.logger.error(f"检索邮件失败: {response.status_code} - {response.text}")
            return False, 0, elapsed_time
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.logger.error(f"检索邮件异常: {e}")
            return False, 0, elapsed_time
    
    def get_database_size(self) -> float:
        """获取数据库大小（MB）"""
        try:
            # 从环境变量或默认路径获取数据库路径
            db_path = os.getenv('DATABASE_PATH', 'database/tempmail.db')
            if os.path.exists(db_path):
                size_bytes = os.path.getsize(db_path)
                return size_bytes / 1024 / 1024
            return 0.0
        except Exception:
            return 0.0
    
    def monitor_system_resources(self) -> Tuple[float, float]:
        """监控系统资源使用"""
        try:
            memory_mb = self.process.memory_info().rss / 1024 / 1024
            cpu_percent = self.process.cpu_percent()
            return memory_mb, cpu_percent
        except Exception:
            return 0.0, 0.0

    def run_single_test(self, mailbox_count: int) -> PerformanceMetrics:
        """运行单个测试场景"""
        self.logger.info(f"开始测试: {mailbox_count} 个邮箱")

        test_start_time = time.time()
        errors = {}

        # 性能指标收集
        mailbox_creation_times = []
        email_storage_times = []
        email_retrieval_times = []

        memory_readings = []
        cpu_readings = []

        # 阶段1: 创建邮箱
        self.logger.info(f"创建 {mailbox_count} 个邮箱...")
        mailbox_creation_start = time.time()

        created_mailboxes = []
        mailbox_creation_success = 0

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.concurrent_workers) as executor:
            futures = []
            for i in range(mailbox_count):
                # 生成符合20字符限制的前缀
                timestamp_suffix = str(int(time.time()))[-6:]  # 取时间戳后6位
                prefix = f"test-{i}-{timestamp_suffix}"  # 格式: test-0-123456 (最长14字符)
                future = executor.submit(self.create_mailbox, prefix)
                futures.append(future)

            for future in concurrent.futures.as_completed(futures):
                try:
                    success, email_address, elapsed_time = future.result(timeout=30)
                    mailbox_creation_times.append(elapsed_time)

                    if success:
                        mailbox_creation_success += 1
                        created_mailboxes.append(email_address)
                    else:
                        errors["mailbox_creation"] = errors.get("mailbox_creation", 0) + 1

                except Exception as e:
                    self.logger.error(f"邮箱创建任务异常: {e}")
                    errors["mailbox_creation"] = errors.get("mailbox_creation", 0) + 1

        mailbox_creation_time = time.time() - mailbox_creation_start

        # 监控资源
        memory_mb, cpu_percent = self.monitor_system_resources()
        memory_readings.append(memory_mb)
        cpu_readings.append(cpu_percent)

        # 阶段2: 发送邮件
        self.logger.info(f"向 {len(created_mailboxes)} 个邮箱发送邮件...")
        email_sending_start = time.time()

        email_storage_success = 0
        total_emails_sent = 0

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.concurrent_workers) as executor:
            futures = []

            for email_address in created_mailboxes:
                # 每个邮箱发送随机数量的邮件
                emails_count = random.randint(
                    self.config.emails_per_mailbox_min,
                    self.config.emails_per_mailbox_max
                )

                for j in range(emails_count):
                    sender = f"test-sender-{j}@example.com"
                    subject = f"压力测试邮件 #{j+1} - {datetime.now().strftime('%H:%M:%S')}"
                    body = f"这是一封压力测试邮件。\n邮箱: {email_address}\n序号: {j+1}\n时间: {datetime.now()}"

                    future = executor.submit(self.simulate_email_storage, email_address, sender, subject, body)
                    futures.append(future)
                    total_emails_sent += 1

            for future in concurrent.futures.as_completed(futures):
                try:
                    success, elapsed_time = future.result(timeout=30)
                    email_storage_times.append(elapsed_time)

                    if success:
                        email_storage_success += 1
                    else:
                        errors["email_storage"] = errors.get("email_storage", 0) + 1

                except Exception as e:
                    self.logger.error(f"邮件存储任务异常: {e}")
                    errors["email_storage"] = errors.get("email_storage", 0) + 1

        email_sending_time = time.time() - email_sending_start

        # 监控资源
        memory_mb, cpu_percent = self.monitor_system_resources()
        memory_readings.append(memory_mb)
        cpu_readings.append(cpu_percent)

        # 阶段3: 检索邮件
        self.logger.info(f"检索 {len(created_mailboxes)} 个邮箱的邮件...")
        email_retrieval_start = time.time()

        email_retrieval_success = 0
        total_emails_retrieved = 0

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.concurrent_workers) as executor:
            futures = []

            for email_address in created_mailboxes:
                future = executor.submit(self.retrieve_emails, email_address)
                futures.append(future)

            for future in concurrent.futures.as_completed(futures):
                try:
                    success, email_count, elapsed_time = future.result(timeout=30)
                    email_retrieval_times.append(elapsed_time)

                    if success:
                        email_retrieval_success += 1
                        total_emails_retrieved += email_count
                    else:
                        errors["email_retrieval"] = errors.get("email_retrieval", 0) + 1

                except Exception as e:
                    self.logger.error(f"邮件检索任务异常: {e}")
                    errors["email_retrieval"] = errors.get("email_retrieval", 0) + 1

        email_retrieval_time = time.time() - email_retrieval_start

        # 最终资源监控
        memory_mb, cpu_percent = self.monitor_system_resources()
        memory_readings.append(memory_mb)
        cpu_readings.append(cpu_percent)

        total_test_time = time.time() - test_start_time

        # 计算性能指标
        metrics = PerformanceMetrics(
            timestamp=datetime.now().isoformat(),
            mailbox_count=mailbox_count,
            total_emails=total_emails_sent,
            emails_per_mailbox=total_emails_sent / max(len(created_mailboxes), 1),

            mailbox_creation_time=mailbox_creation_time,
            email_sending_time=email_sending_time,
            email_retrieval_time=email_retrieval_time,
            total_test_time=total_test_time,

            mailbox_creation_success_rate=mailbox_creation_success / mailbox_count * 100,
            email_storage_success_rate=email_storage_success / max(total_emails_sent, 1) * 100,
            email_retrieval_success_rate=email_retrieval_success / max(len(created_mailboxes), 1) * 100,

            peak_memory_mb=max(memory_readings) if memory_readings else 0,
            peak_cpu_percent=max(cpu_readings) if cpu_readings else 0,
            database_size_mb=self.get_database_size(),

            avg_mailbox_creation_time_ms=statistics.mean(mailbox_creation_times) * 1000 if mailbox_creation_times else 0,
            avg_email_storage_time_ms=statistics.mean(email_storage_times) * 1000 if email_storage_times else 0,
            avg_email_retrieval_time_ms=statistics.mean(email_retrieval_times) * 1000 if email_retrieval_times else 0,

            errors=errors
        )

        self.logger.info(f"测试完成: {mailbox_count} 个邮箱, 总时间: {total_test_time:.2f}秒")
        self.logger.info(f"成功率 - 邮箱创建: {metrics.mailbox_creation_success_rate:.1f}%, "
                        f"邮件存储: {metrics.email_storage_success_rate:.1f}%, "
                        f"邮件检索: {metrics.email_retrieval_success_rate:.1f}%")

        return metrics

    def run_stress_test(self) -> List[PerformanceMetrics]:
        """运行完整的压力测试"""
        self.logger.info("开始压力测试...")

        if not self.check_system_requirements():
            raise RuntimeError("系统要求检查失败")

        # 初始化数据库
        try:
            # 导入Flask应用并在应用上下文中初始化数据库
            from app import app
            with app.app_context():
                init_db_schema()
            self.logger.info("数据库初始化完成")
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise

        results = []

        # 逐步增加邮箱数量进行测试
        for mailbox_count in range(
            self.config.min_mailboxes,
            self.config.max_mailboxes + 1,
            self.config.step_size
        ):
            try:
                metrics = self.run_single_test(mailbox_count)
                results.append(metrics)
                self.test_results.append(metrics)

                # 检查是否达到系统限制
                if (metrics.mailbox_creation_success_rate < 80 or
                    metrics.email_storage_success_rate < 80 or
                    metrics.email_retrieval_success_rate < 80):
                    self.logger.warning(f"成功率低于80%，可能达到系统限制")

                # 短暂休息，让系统恢复
                time.sleep(2)

            except Exception as e:
                self.logger.error(f"测试 {mailbox_count} 个邮箱时发生错误: {e}")
                continue

        self.logger.info("压力测试完成")
        return results

    def cleanup_test_data(self):
        """清理测试数据"""
        if not self.config.cleanup_after_test:
            self.logger.info("跳过测试数据清理")
            return

        self.logger.info("清理测试数据...")

        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()

                # 删除测试会话的邮箱
                cursor.execute(
                    "DELETE FROM temporary_emails WHERE session_id = ?",
                    (self.test_session_id,)
                )

                # 删除测试邮箱的邮件（通过外键约束自动删除）
                deleted_count = cursor.rowcount
                conn.commit()

                self.logger.info(f"已清理 {deleted_count} 个测试邮箱及相关邮件")

        except Exception as e:
            self.logger.error(f"清理测试数据失败: {e}")

    def generate_report(self) -> str:
        """生成测试报告"""
        if not self.test_results:
            return "没有测试结果可生成报告"

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("临时邮箱MVP项目压力测试报告")
        report_lines.append("=" * 80)
        report_lines.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"测试配置:")
        report_lines.append(f"  - 邮箱数量范围: {self.config.min_mailboxes} - {self.config.max_mailboxes}")
        report_lines.append(f"  - 步长: {self.config.step_size}")
        report_lines.append(f"  - 每邮箱邮件数: {self.config.emails_per_mailbox_min} - {self.config.emails_per_mailbox_max}")
        report_lines.append(f"  - 并发工作线程: {self.config.concurrent_workers}")
        report_lines.append("")

        # 汇总统计
        max_mailboxes = max(r.mailbox_count for r in self.test_results)
        max_emails = max(r.total_emails for r in self.test_results)
        avg_creation_success = statistics.mean(r.mailbox_creation_success_rate for r in self.test_results)
        avg_storage_success = statistics.mean(r.email_storage_success_rate for r in self.test_results)
        avg_retrieval_success = statistics.mean(r.email_retrieval_success_rate for r in self.test_results)

        report_lines.append("测试结果汇总:")
        report_lines.append(f"  - 最大成功处理邮箱数: {max_mailboxes}")
        report_lines.append(f"  - 最大成功处理邮件数: {max_emails}")
        report_lines.append(f"  - 平均邮箱创建成功率: {avg_creation_success:.1f}%")
        report_lines.append(f"  - 平均邮件存储成功率: {avg_storage_success:.1f}%")
        report_lines.append(f"  - 平均邮件检索成功率: {avg_retrieval_success:.1f}%")
        report_lines.append("")

        # 性能指标
        report_lines.append("性能指标:")
        report_lines.append(f"{'邮箱数':<8} {'邮件数':<8} {'创建时间':<10} {'存储时间':<10} {'检索时间':<10} {'内存(MB)':<10} {'数据库(MB)':<12}")
        report_lines.append("-" * 80)

        for result in self.test_results:
            report_lines.append(
                f"{result.mailbox_count:<8} "
                f"{result.total_emails:<8} "
                f"{result.mailbox_creation_time:<10.2f} "
                f"{result.email_sending_time:<10.2f} "
                f"{result.email_retrieval_time:<10.2f} "
                f"{result.peak_memory_mb:<10.1f} "
                f"{result.database_size_mb:<12.1f}"
            )

        report_lines.append("")

        # 错误统计
        all_errors = {}
        for result in self.test_results:
            for error_type, count in result.errors.items():
                all_errors[error_type] = all_errors.get(error_type, 0) + count

        if all_errors:
            report_lines.append("错误统计:")
            for error_type, count in all_errors.items():
                report_lines.append(f"  - {error_type}: {count}")
            report_lines.append("")

        # 系统建议
        report_lines.append("系统优化建议:")

        # 基于测试结果给出建议
        if avg_creation_success < 95:
            report_lines.append("  - 邮箱创建成功率较低，建议优化数据库连接池或增加超时时间")

        if avg_storage_success < 95:
            report_lines.append("  - 邮件存储成功率较低，建议优化邮件存储逻辑或增加数据库性能")

        if avg_retrieval_success < 95:
            report_lines.append("  - 邮件检索成功率较低，建议优化查询索引或增加缓存")

        # 检查响应时间
        avg_creation_time = statistics.mean(r.avg_mailbox_creation_time_ms for r in self.test_results)
        avg_storage_time = statistics.mean(r.avg_email_storage_time_ms for r in self.test_results)
        avg_retrieval_time = statistics.mean(r.avg_email_retrieval_time_ms for r in self.test_results)

        if avg_creation_time > 1000:  # 1秒
            report_lines.append("  - 邮箱创建响应时间较慢，建议优化创建逻辑")

        if avg_storage_time > 500:  # 0.5秒
            report_lines.append("  - 邮件存储响应时间较慢，建议优化存储逻辑")

        if avg_retrieval_time > 500:  # 0.5秒
            report_lines.append("  - 邮件检索响应时间较慢，建议优化查询逻辑")

        # 内存使用建议
        max_memory = max(r.peak_memory_mb for r in self.test_results)
        if max_memory > 500:  # 500MB
            report_lines.append("  - 内存使用较高，建议优化内存管理或增加系统内存")

        report_lines.append("")
        report_lines.append("=" * 80)

        return "\n".join(report_lines)

    def save_results_to_json(self, filename: str = None):
        """保存测试结果到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"stress_test_results_{timestamp}.json"

        filepath = self.output_dir / filename

        # 转换结果为可序列化的格式
        serializable_results = []
        for result in self.test_results:
            serializable_results.append(asdict(result))

        data = {
            "test_config": asdict(self.config),
            "test_session_id": self.test_session_id,
            "test_results": serializable_results,
            "summary": {
                "total_tests": len(self.test_results),
                "max_mailboxes": max(r.mailbox_count for r in self.test_results) if self.test_results else 0,
                "max_emails": max(r.total_emails for r in self.test_results) if self.test_results else 0,
            }
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        self.logger.info(f"测试结果已保存到: {filepath}")
        return filepath


def create_test_config_from_args(args) -> TestConfig:
    """从命令行参数创建测试配置"""
    return TestConfig(
        min_mailboxes=args.min_mailboxes,
        max_mailboxes=args.max_mailboxes,
        step_size=args.step_size,
        emails_per_mailbox_min=args.emails_per_mailbox_min,
        emails_per_mailbox_max=args.emails_per_mailbox_max,
        concurrent_workers=args.concurrent_workers,
        test_duration_seconds=args.test_duration,
        base_url=args.base_url,
        domain_name=args.domain_name,
        cleanup_after_test=args.cleanup,
        output_dir=args.output_dir
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="临时邮箱MVP项目压力测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本测试（10-50个邮箱，步长10）
  python stress_test_email_capacity.py

  # 大规模测试（50-200个邮箱，步长25）
  python stress_test_email_capacity.py --min-mailboxes 50 --max-mailboxes 200 --step-size 25

  # 高并发测试（10个并发工作线程）
  python stress_test_email_capacity.py --concurrent-workers 10

  # 自定义邮件数量范围
  python stress_test_email_capacity.py --emails-per-mailbox-min 5 --emails-per-mailbox-max 100
        """
    )

    parser.add_argument(
        '--min-mailboxes', type=int, default=10,
        help='最小邮箱数量 (默认: 10)'
    )
    parser.add_argument(
        '--max-mailboxes', type=int, default=100,
        help='最大邮箱数量 (默认: 100)'
    )
    parser.add_argument(
        '--step-size', type=int, default=10,
        help='邮箱数量步长 (默认: 10)'
    )
    parser.add_argument(
        '--emails-per-mailbox-min', type=int, default=1,
        help='每个邮箱最少邮件数 (默认: 1)'
    )
    parser.add_argument(
        '--emails-per-mailbox-max', type=int, default=50,
        help='每个邮箱最多邮件数 (默认: 50)'
    )
    parser.add_argument(
        '--concurrent-workers', type=int, default=5,
        help='并发工作线程数 (默认: 5)'
    )
    parser.add_argument(
        '--test-duration', type=int, default=300,
        help='单个测试最大持续时间（秒） (默认: 300)'
    )
    parser.add_argument(
        '--base-url', type=str, default='http://127.0.0.1:5000',
        help='应用基础URL (默认: http://127.0.0.1:5000)'
    )
    parser.add_argument(
        '--domain-name', type=str, default='develop.local',
        help='邮箱域名 (默认: develop.local)'
    )
    parser.add_argument(
        '--output-dir', type=str, default='stress_test_results',
        help='输出目录 (默认: stress_test_results)'
    )
    parser.add_argument(
        '--no-cleanup', dest='cleanup', action='store_false',
        help='测试后不清理数据'
    )
    parser.add_argument(
        '--quick-test', action='store_true',
        help='快速测试模式（小规模测试）'
    )

    args = parser.parse_args()

    # 快速测试模式
    if args.quick_test:
        args.min_mailboxes = 5
        args.max_mailboxes = 20
        args.step_size = 5
        args.emails_per_mailbox_max = 10
        args.concurrent_workers = 3

    # 验证参数
    if args.min_mailboxes <= 0 or args.max_mailboxes <= 0:
        print("错误: 邮箱数量必须大于0")
        return 1

    if args.min_mailboxes > args.max_mailboxes:
        print("错误: 最小邮箱数量不能大于最大邮箱数量")
        return 1

    if args.step_size <= 0:
        print("错误: 步长必须大于0")
        return 1

    if args.emails_per_mailbox_min > args.emails_per_mailbox_max:
        print("错误: 最小邮件数不能大于最大邮件数")
        return 1

    # 创建测试配置
    config = create_test_config_from_args(args)

    # 运行测试
    runner = StressTestRunner(config)

    try:
        print("开始压力测试...")
        print(f"测试配置: {args.min_mailboxes}-{args.max_mailboxes} 邮箱, 步长 {args.step_size}")
        print(f"每邮箱邮件数: {args.emails_per_mailbox_min}-{args.emails_per_mailbox_max}")
        print(f"并发工作线程: {args.concurrent_workers}")
        print("-" * 60)

        # 运行压力测试
        results = runner.run_stress_test()

        # 生成报告
        report = runner.generate_report()
        print(report)

        # 保存结果
        json_file = runner.save_results_to_json()

        # 保存报告到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = runner.output_dir / f"stress_test_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"\n报告已保存到: {report_file}")
        print(f"详细数据已保存到: {json_file}")

        # 清理测试数据
        runner.cleanup_test_data()

        print("\n压力测试完成！")
        return 0

    except KeyboardInterrupt:
        print("\n测试被用户中断")
        runner.cleanup_test_data()
        return 1

    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        runner.cleanup_test_data()
        return 1


if __name__ == "__main__":
    sys.exit(main())
