# tests/stress/conftest_stress.py
"""
压力测试专用配置文件
包含压力测试所需的特殊fixtures和配置
"""
import os
import pytest
import tempfile
import sqlite3
import logging
from datetime import datetime, timezone
import sys
from pathlib import Path

# 确保能从项目根目录导入 app 模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from app import app as flask_app, init_db_schema, get_db_connection

# 压力测试环境变量配置
def setup_stress_test_environment():
    """设置压力测试环境变量"""
    stress_env_vars = {
        'STRESS_TEST_BASE_URL': os.environ.get('STRESS_TEST_BASE_URL', 'http://127.0.0.1:5001'),
        'STRESS_TEST_DATABASE_PATH': os.environ.get('STRESS_TEST_DATABASE_PATH', 'database/tempmail_stress_test.db'),
        'STRESS_TEST_DOMAIN_NAME': os.environ.get('STRESS_TEST_DOMAIN_NAME', 'test.local'),
        'STRESS_TEST_MAX_CONCURRENT': os.environ.get('STRESS_TEST_MAX_CONCURRENT', '50'),
        'STRESS_TEST_MAX_EMAILS_PER_MAILBOX': os.environ.get('STRESS_TEST_MAX_EMAILS_PER_MAILBOX', '100'),
        'STRESS_TEST_TIMEOUT': os.environ.get('STRESS_TEST_TIMEOUT', '300'),
        'STRESS_TEST_DISABLE_RATE_LIMITS': os.environ.get('STRESS_TEST_DISABLE_RATE_LIMITS', 'true'),
        'STRESS_TEST_CLEANUP_AFTER': os.environ.get('STRESS_TEST_CLEANUP_AFTER', 'true'),
    }
    
    for key, value in stress_env_vars.items():
        if key not in os.environ:
            os.environ[key] = value

# 在导入时设置环境变量
setup_stress_test_environment()

# 注册压力测试标记
def pytest_configure(config):
    """注册压力测试标记"""
    config.addinivalue_line("markers", "stress: 压力测试")
    config.addinivalue_line("markers", "stress_slow: 慢速压力测试")
    config.addinivalue_line("markers", "stress_concurrent: 并发压力测试")
    config.addinivalue_line("markers", "stress_capacity: 容量压力测试")
    config.addinivalue_line("markers", "stress_performance: 性能压力测试")


@pytest.fixture(scope='session')
def stress_test_app():
    """
    创建压力测试专用的 Flask app 实例
    使用专门的数据库和配置
    """
    # 使用专门的压力测试数据库
    db_path = os.environ.get('STRESS_TEST_DATABASE_PATH', 'database/tempmail_stress_test.db')
    
    # 确保数据库目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 配置 Flask app 进行压力测试
    flask_app.config.update({
        "TESTING": True,
        "DATABASE_PATH": db_path,
        "DOMAIN_NAME": os.environ.get('STRESS_TEST_DOMAIN_NAME', 'test.local'),
        "EMAIL_EXPIRATION_HOURS": 24,  # 压力测试时使用更长的过期时间
        "API_BASE_URL": os.environ.get('STRESS_TEST_BASE_URL', 'http://127.0.0.1:5001'),
        "API_TIMEOUT": int(os.environ.get('STRESS_TEST_TIMEOUT', '300')),
        "DISABLE_RATE_LIMITS": os.environ.get('STRESS_TEST_DISABLE_RATE_LIMITS', 'true').lower() == 'true',
    })

    # 在应用上下文中初始化数据库表结构
    with flask_app.app_context():
        init_db_schema()

    yield flask_app


@pytest.fixture(scope='session')
def stress_test_client(stress_test_app):
    """提供压力测试专用的 Flask 测试客户端"""
    return stress_test_app.test_client()


@pytest.fixture(scope='function')
def stress_test_db_conn(stress_test_app):
    """提供压力测试专用的数据库连接"""
    with stress_test_app.app_context():
        connection = get_db_connection()
        yield connection
        connection.close()


@pytest.fixture
def stress_test_config():
    """提供压力测试配置"""
    return {
        'base_url': os.environ.get('STRESS_TEST_BASE_URL', 'http://127.0.0.1:5001'),
        'max_concurrent': int(os.environ.get('STRESS_TEST_MAX_CONCURRENT', '50')),
        'max_emails_per_mailbox': int(os.environ.get('STRESS_TEST_MAX_EMAILS_PER_MAILBOX', '100')),
        'timeout': int(os.environ.get('STRESS_TEST_TIMEOUT', '300')),
        'disable_rate_limits': os.environ.get('STRESS_TEST_DISABLE_RATE_LIMITS', 'true').lower() == 'true',
        'cleanup_after': os.environ.get('STRESS_TEST_CLEANUP_AFTER', 'true').lower() == 'true',
        'database_path': os.environ.get('STRESS_TEST_DATABASE_PATH', 'database/tempmail_stress_test.db'),
        'domain_name': os.environ.get('STRESS_TEST_DOMAIN_NAME', 'test.local'),
    }


@pytest.fixture
def stress_test_logger():
    """提供压力测试专用的日志记录器"""
    logger = logging.getLogger('stress_test')
    logger.setLevel(logging.INFO)
    
    # 避免重复添加处理器
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


@pytest.fixture
def disable_rate_limits():
    """禁用速率限制的fixture"""
    def _disable():
        try:
            from app import rate_limit
            if hasattr(rate_limit, "clear"):
                rate_limit.clear()
            # 设置环境变量禁用速率限制
            os.environ['DISABLE_RATE_LIMITS'] = 'true'
        except (ImportError, AttributeError):
            pass

    _disable()  # 测试前禁用
    yield _disable
    # 测试后恢复（如果需要）
    if 'DISABLE_RATE_LIMITS' in os.environ:
        del os.environ['DISABLE_RATE_LIMITS']


@pytest.fixture
def stress_test_cleanup():
    """压力测试清理fixture"""
    created_resources = []
    
    def register_resource(resource_type, resource_id):
        """注册需要清理的资源"""
        created_resources.append((resource_type, resource_id))
    
    yield register_resource
    
    # 测试后清理资源
    if os.environ.get('STRESS_TEST_CLEANUP_AFTER', 'true').lower() == 'true':
        cleanup_resources(created_resources)


def cleanup_resources(resources):
    """清理测试资源"""
    db_path = os.environ.get('STRESS_TEST_DATABASE_PATH', 'database/tempmail_stress_test.db')
    
    if not os.path.exists(db_path):
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        for resource_type, resource_id in resources:
            if resource_type == 'email':
                cursor.execute("DELETE FROM temporary_emails WHERE address = ?", (resource_id,))
            elif resource_type == 'session':
                cursor.execute("DELETE FROM email_history WHERE session_id = ?", (resource_id,))
        
        conn.commit()
        conn.close()
    except Exception as e:
        logging.warning(f"清理测试资源时出错: {e}")


# 压力测试专用的pytest钩子
def pytest_runtest_setup(item):
    """压力测试运行前的设置"""
    if 'stress' in item.keywords:
        # 确保压力测试环境已设置
        setup_stress_test_environment()


def pytest_runtest_teardown(item, nextitem):
    """压力测试运行后的清理"""
    if 'stress' in item.keywords:
        # 可以在这里添加额外的清理逻辑
        pass
