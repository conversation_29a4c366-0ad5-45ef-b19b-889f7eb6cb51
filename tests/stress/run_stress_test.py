#!/usr/bin/env python3
"""
压力测试运行脚本
一键启动压力测试环境并运行测试

功能：
1. 自动启动压力测试服务器
2. 运行压力测试
3. 生成测试报告
4. 清理测试环境

使用方法：
python run_stress_test.py [选项]

作者：Augment Agent
日期：2025-01-27
"""

import os
import sys
import time
import signal
import subprocess
import argparse
import threading
from pathlib import Path

# 确保在项目根目录
project_root = Path(__file__).resolve().parent
os.chdir(project_root)

class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self):
        self.server_process = None
        self.server_port = 5001  # 使用不同的端口避免冲突
        self.server_host = '127.0.0.1'
        
    def start_server(self):
        """启动压力测试服务器"""
        print("🚀 启动压力测试服务器...")
        
        try:
            # 启动服务器进程
            cmd = [
                sys.executable,
                'start_stress_test_server.py',
                '--host', self.server_host,
                '--port', str(self.server_port),
                '--no-memory-db'  # 使用文件数据库以避免表结构问题
            ]
            
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务器启动
            print(f"⏳ 等待服务器启动 (http://{self.server_host}:{self.server_port})...")
            time.sleep(3)
            
            # 检查服务器是否启动成功
            if self.server_process.poll() is None:
                print("✅ 压力测试服务器启动成功")
                return True
            else:
                stdout, stderr = self.server_process.communicate()
                print(f"❌ 服务器启动失败:")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务器时发生错误: {e}")
            return False
    
    def stop_server(self):
        """停止压力测试服务器"""
        if self.server_process and self.server_process.poll() is None:
            print("🛑 停止压力测试服务器...")
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                print("✅ 服务器已停止")
            except subprocess.TimeoutExpired:
                print("⚠️  强制终止服务器...")
                self.server_process.kill()
                self.server_process.wait()
            except Exception as e:
                print(f"⚠️  停止服务器时发生错误: {e}")
    
    def run_stress_test(self, args):
        """运行压力测试"""
        print("🧪 开始压力测试...")
        
        # 构建测试命令
        cmd = [
            sys.executable,
            'tests/stress_test_email_capacity.py',
            '--base-url', f'http://{self.server_host}:{self.server_port}',
            '--min-mailboxes', str(args.min_mailboxes),
            '--max-mailboxes', str(args.max_mailboxes),
            '--step-size', str(args.step_size),
            '--emails-per-mailbox-min', str(args.emails_per_mailbox_min),
            '--emails-per-mailbox-max', str(args.emails_per_mailbox_max),
            '--concurrent-workers', str(args.concurrent_workers),
            '--output-dir', args.output_dir
        ]
        
        if args.no_cleanup:
            cmd.append('--no-cleanup')
        
        if args.quick_test:
            cmd.append('--quick-test')
        
        try:
            # 运行压力测试
            result = subprocess.run(cmd, check=True, text=True)
            print("✅ 压力测试完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 压力测试失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 运行压力测试时发生错误: {e}")
            return False
    
    def cleanup(self):
        """清理测试环境"""
        print("🧹 清理测试环境...")
        self.stop_server()
        print("✅ 清理完成")

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到中断信号，正在清理...")
    if hasattr(signal_handler, 'runner'):
        signal_handler.runner.cleanup()
    sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="压力测试运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 快速测试
  python run_stress_test.py --quick-test
  
  # 自定义测试范围
  python run_stress_test.py --min-mailboxes 20 --max-mailboxes 100
  
  # 高并发测试
  python run_stress_test.py --concurrent-workers 10
        """
    )
    
    parser.add_argument(
        '--min-mailboxes', type=int, default=10,
        help='最小邮箱数量 (默认: 10)'
    )
    parser.add_argument(
        '--max-mailboxes', type=int, default=50,
        help='最大邮箱数量 (默认: 50)'
    )
    parser.add_argument(
        '--step-size', type=int, default=10,
        help='邮箱数量步长 (默认: 10)'
    )
    parser.add_argument(
        '--emails-per-mailbox-min', type=int, default=1,
        help='每个邮箱最少邮件数 (默认: 1)'
    )
    parser.add_argument(
        '--emails-per-mailbox-max', type=int, default=20,
        help='每个邮箱最多邮件数 (默认: 20)'
    )
    parser.add_argument(
        '--concurrent-workers', type=int, default=5,
        help='并发工作线程数 (默认: 5)'
    )
    parser.add_argument(
        '--output-dir', type=str, default='stress_test_results',
        help='输出目录 (默认: stress_test_results)'
    )
    parser.add_argument(
        '--no-cleanup', action='store_true',
        help='测试后不清理数据'
    )
    parser.add_argument(
        '--quick-test', action='store_true',
        help='快速测试模式'
    )
    parser.add_argument(
        '--server-only', action='store_true',
        help='仅启动服务器，不运行测试'
    )
    
    args = parser.parse_args()
    
    # 快速测试模式
    if args.quick_test:
        args.min_mailboxes = 5
        args.max_mailboxes = 20
        args.step_size = 5
        args.emails_per_mailbox_max = 10
        args.concurrent_workers = 3
    
    print("🧪 临时邮箱MVP - 压力测试运行器")
    print("=" * 50)
    print(f"测试配置:")
    print(f"  - 邮箱数量范围: {args.min_mailboxes} - {args.max_mailboxes}")
    print(f"  - 步长: {args.step_size}")
    print(f"  - 每邮箱邮件数: {args.emails_per_mailbox_min} - {args.emails_per_mailbox_max}")
    print(f"  - 并发工作线程: {args.concurrent_workers}")
    print(f"  - 输出目录: {args.output_dir}")
    print("=" * 50)
    
    # 创建运行器
    runner = StressTestRunner()
    signal_handler.runner = runner
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动服务器
        if not runner.start_server():
            print("❌ 无法启动压力测试服务器")
            return 1
        
        if args.server_only:
            print("🌐 服务器已启动，按 Ctrl+C 停止")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
        else:
            # 运行压力测试
            success = runner.run_stress_test(args)
            
            if success:
                print("\n🎉 压力测试成功完成！")
                print(f"📊 查看结果: {args.output_dir}/")
                return 0
            else:
                print("\n❌ 压力测试失败")
                return 1
    
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {e}")
        return 1
    finally:
        runner.cleanup()

if __name__ == "__main__":
    sys.exit(main())
