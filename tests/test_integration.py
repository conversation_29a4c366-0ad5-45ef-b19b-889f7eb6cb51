"""
集成测试模块
测试各个组件之间的集成，包括并发访问、性能测试、端到端流程等
"""
import pytest
import concurrent.futures
import sqlite3
import time
from datetime import datetime, timezone, timedelta
from unittest import mock


@pytest.mark.integration
class TestConcurrentAccess:
    """并发访问测试"""

    def test_concurrent_address_generation(self, client, app_instance, clear_rate_limit):
        """测试并发生成邮箱地址"""
        clear_rate_limit()
        num_concurrent_requests = 10
        max_allowed = 5
        statuses = []
        addresses = set()

        # 临时禁用测试模式以启用速率限制
        original_testing = app_instance.config['TESTING']
        app_instance.config['TESTING'] = False

        try:
            def make_request():
                response = client.post('/api/generate-address',
                                     json={},
                                     content_type='application/json')
                if response.status_code == 201:
                    result = response.get_json()
                    if result["success"]:
                        return 201, result["data"]["address"]
                return response.status_code, None

            with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
                futures = [executor.submit(make_request) for _ in range(num_concurrent_requests)]
                results = [f.result() for f in concurrent.futures.as_completed(futures)]

            for status, addr in results:
                statuses.append(status)
                if addr:
                    addresses.add(addr)

            assert statuses.count(201) <= max_allowed
            assert statuses.count(201) == len(addresses)
            assert statuses.count(429) >= 0
        finally:
            # 恢复测试模式
            app_instance.config['TESTING'] = original_testing

    def test_concurrent_database_operations(self, app_instance, db_conn):
        """测试并发数据库操作"""
        num_concurrent_requests = 10
        db_path = app_instance.config['DATABASE_PATH']

        def insert_and_read():
            now_utc = datetime.now(timezone.utc)
            with sqlite3.connect(db_path) as thread_conn:
                thread_conn.row_factory = sqlite3.Row
                cursor = thread_conn.cursor()
                try:
                    cursor.execute(
                        """INSERT INTO temporary_emails (address, expires_at, created_at)
                           VALUES (?, ?, ?)""",
                        (
                            f"{now_utc.timestamp()}@example.com",
                            now_utc.isoformat(),
                            now_utc.isoformat()
                        )
                    )
                    thread_conn.commit()
                    return True
                except sqlite3.IntegrityError:
                    thread_conn.rollback()
                    return False

        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
            futures = [executor.submit(insert_and_read) for _ in range(num_concurrent_requests)]
            results = [f.result() for f in concurrent.futures.as_completed(futures)]

        cursor = db_conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM temporary_emails WHERE address LIKE '%.%@example.com'")
        total_records = cursor.fetchone()["count"]
        successful_operations = sum(1 for r in results if r)
        assert successful_operations == total_records

        # 清理测试数据
        cursor.execute("DELETE FROM temporary_emails WHERE address LIKE '%.%@example.com'")
        db_conn.commit()


@pytest.mark.integration
@pytest.mark.slow
class TestEndToEndWorkflows:
    """端到端工作流程测试"""

    def test_complete_email_lifecycle(self, client, app_instance, db_conn):
        """测试完整的邮箱生命周期"""
        session_id = f"e2e_test_{int(time.time())}"
        
        # 1. 生成邮箱
        response = client.post('/api/generate-address',
                              json={"session_id": session_id, "custom_prefix": "lifecycle"},
                              content_type='application/json')
        assert response.status_code == 201
        result = response.get_json()
        email_address = result["data"]["address"]
        
        # 2. 验证邮箱在数据库中存在
        cursor = db_conn.cursor()
        cursor.execute("SELECT id FROM temporary_emails WHERE address = ?", (email_address,))
        email_record = cursor.fetchone()
        assert email_record is not None
        email_id = email_record["id"]
        
        # 3. 模拟接收邮件
        now_utc = datetime.now(timezone.utc)
        cursor.execute(
            """INSERT INTO received_mails 
               (email_address_id, sender, subject, body_text, received_at)
               VALUES (?, ?, ?, ?, ?)""",
            (email_id, "<EMAIL>", "Test Email", "Test content", now_utc.isoformat())
        )
        db_conn.commit()
        
        # 4. 获取邮件列表
        response = client.get(f'/api/emails?address={email_address}')
        assert response.status_code == 200
        result = response.get_json()
        assert result["success"] is True
        emails = result["data"]["emails"]
        assert len(emails) == 1
        assert emails[0]["subject"] == "Test Email"
        
        # 5. 获取历史记录
        response = client.get(f'/api/email-history?session_id={session_id}')
        assert response.status_code == 200
        result = response.get_json()
        assert result["success"] is True
        history = result["data"]["history"]
        assert len(history) == 1
        assert history[0]["email_address"] == email_address
        assert history[0]["is_active"] is True
        
        # 6. 删除邮箱
        response = client.delete('/api/delete-email',
                                json={"address": email_address},
                                content_type='application/json')
        assert response.status_code == 200
        result = response.get_json()
        assert result["success"] is True
        
        # 7. 验证邮箱已删除
        response = client.get(f'/api/emails?address={email_address}')
        assert response.status_code == 404

    def test_session_persistence_workflow(self, client, app_instance):
        """测试会话持久性工作流程"""
        session_id = f"persistence_test_{int(time.time())}"
        
        # 生成多个邮箱
        emails = []
        for i in range(3):
            response = client.post('/api/generate-address',
                                  json={"session_id": session_id, "custom_prefix": f"persist{i}"},
                                  content_type='application/json')
            assert response.status_code == 201
            result = response.get_json()
            emails.append(result["data"]["address"])
        
        # 验证历史记录包含所有邮箱
        response = client.get(f'/api/email-history?session_id={session_id}')
        assert response.status_code == 200
        result = response.get_json()
        assert result["success"] is True
        history = result["data"]["history"]
        assert len(history) == 3
        
        # 验证只有最新邮箱是活跃的
        active_count = sum(1 for h in history if h["is_active"])
        assert active_count == 1
        assert history[0]["is_active"] is True  # 最新的应该是活跃的
        
        # 验证所有邮箱都在历史记录中
        history_emails = [h["email_address"] for h in history]
        for email in emails:
            assert email in history_emails


@pytest.mark.integration
class TestErrorHandlingIntegration:
    """错误处理集成测试"""

    def test_database_error_recovery(self, client, app_instance):
        """测试数据库错误恢复"""
        # 模拟数据库路径不存在的情况
        original_db_path = app_instance.config['DATABASE_PATH']
        app_instance.config['DATABASE_PATH'] = "/nonexistent/path/test.db"
        
        try:
            response = client.post('/api/generate-address',
                                 json={},
                                 content_type='application/json')
            # 应该返回服务器错误
            assert response.status_code == 500
        finally:
            # 恢复原配置
            app_instance.config['DATABASE_PATH'] = original_db_path

    def test_invalid_request_handling(self, client):
        """测试无效请求处理"""
        # 测试无效的JSON
        response = client.post('/api/generate-address',
                              data="invalid json",
                              content_type='application/json')
        assert response.status_code == 400
        
        # 测试错误的Content-Type
        response = client.post('/api/generate-address',
                              data='{"test": "data"}',
                              content_type='text/plain')
        assert response.status_code == 400

    def test_rate_limiting_integration(self, client, app_instance, clear_rate_limit):
        """测试速率限制集成"""
        clear_rate_limit()
        
        # 临时禁用测试模式以启用速率限制
        original_testing = app_instance.config['TESTING']
        app_instance.config['TESTING'] = False
        
        try:
            # 快速发送多个请求
            responses = []
            for _ in range(10):
                response = client.post('/api/generate-address',
                                     json={},
                                     content_type='application/json')
                responses.append(response.status_code)
            
            # 应该有一些请求被限制
            success_count = responses.count(201)
            rate_limited_count = responses.count(429)
            
            # 验证速率限制生效
            assert success_count <= 5  # 假设限制为5个请求
            assert rate_limited_count > 0
            
        finally:
            # 恢复测试模式
            app_instance.config['TESTING'] = original_testing


@pytest.mark.integration
class TestConfigurationIntegration:
    """配置集成测试"""

    def test_environment_variable_integration(self, app_instance):
        """测试环境变量集成"""
        # 验证关键配置项存在
        required_configs = [
            'DATABASE_PATH',
            'DOMAIN_NAME',
            'EMAIL_EXPIRATION_HOURS',
            'API_BASE_URL',
            'API_TIMEOUT',
            'API_RETRY_ATTEMPTS',
            'API_RETRY_DELAY'
        ]
        
        for config_key in required_configs:
            assert config_key in app_instance.config, f"Missing config: {config_key}"

    def test_logging_configuration_integration(self, app_instance):
        """测试日志配置集成"""
        # 验证日志记录器配置正确
        assert app_instance.logger is not None
        assert len(app_instance.logger.handlers) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
