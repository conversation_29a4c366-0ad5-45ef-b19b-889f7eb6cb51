"""
单元测试模块
整合所有单元测试，包括基础功能、验证函数、工具函数等
"""
import pytest
import os
import sys
import sqlite3
import tempfile
import logging
from unittest import mock
from datetime import datetime, timezone, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import (
    error_response, get_email_cache_key, validate_email_address,
    validate_custom_prefix, validate_timestamp, sanitize_html_content,
    validate_request_data, generate_email_prefix, app
)


@pytest.mark.unit
class TestUtilityFunctions:
    """测试工具函数"""

    def test_error_response_basic(self):
        """测试基本错误响应"""
        with app.app_context():
            resp, code = error_response("test message", 400)
            assert code == 400
            assert resp.json["error"] == "test message"

    def test_error_response_with_details(self):
        """测试带详细信息的错误响应"""
        with app.app_context():
            resp, code = error_response("test message", 403, details={"key": "value"})
            assert code == 403
            assert resp.json["details"] == {"key": "value"}

    def test_error_response_with_error_code(self):
        """测试带错误代码的错误响应"""
        with app.app_context():
            resp, code = error_response("test message", 400, error_code="TEST_ERROR")
            assert code == 400
            assert resp.json["error_code"] == "TEST_ERROR"

    def test_get_email_cache_key(self):
        """测试邮件缓存键生成"""
        assert get_email_cache_key(1, "<EMAIL>") == "1:<EMAIL>"
        assert get_email_cache_key(123, "<EMAIL>") == "123:<EMAIL>"


@pytest.mark.unit
class TestValidationFunctions:
    """测试验证函数"""

    def test_validate_email_address_valid(self):
        """测试有效邮箱地址验证"""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        for email in valid_emails:
            is_valid, error = validate_email_address(email)
            assert is_valid, f"Email {email} should be valid, but got error: {error}"

    def test_validate_email_address_invalid(self):
        """测试无效邮箱地址验证"""
        invalid_emails = [
            "",  # 空邮箱
            "invalid",  # 无@符号
            "@example.com",  # 缺少用户名
            "user@",  # 缺少域名
            "user@domain",  # 缺少顶级域名
            "a" * 255 + "@example.com",  # 超长邮箱
        ]
        for email in invalid_emails:
            is_valid, error = validate_email_address(email)
            assert not is_valid, f"Email {email} should be invalid"
            assert error is not None

    def test_validate_custom_prefix_valid(self):
        """测试有效自定义前缀验证"""
        valid_prefixes = [
            "test",
            "test123",
            "test-123",
            "a",  # 最短长度
            "a" * 20,  # 最长长度
        ]
        for prefix in valid_prefixes:
            is_valid, error = validate_custom_prefix(prefix)
            assert is_valid, f"Prefix {prefix} should be valid, but got error: {error}"

    def test_validate_custom_prefix_invalid(self):
        """测试无效自定义前缀验证"""
        invalid_prefixes = [
            "",  # 空前缀
            "a" * 21,  # 超长前缀
            "test@invalid",  # 包含@
            "test.invalid",  # 包含.
            "test space",  # 包含空格
            "test_underscore",  # 包含下划线
        ]
        for prefix in invalid_prefixes:
            is_valid, error = validate_custom_prefix(prefix)
            assert not is_valid, f"Prefix {prefix} should be invalid"
            assert error is not None

    def test_validate_timestamp_valid(self):
        """测试有效时间戳验证"""
        valid_timestamps = [
            "2023-01-01T00:00:00Z",
            "2023-12-31T23:59:59+00:00",
            datetime.now(timezone.utc).isoformat(),
        ]
        for timestamp in valid_timestamps:
            is_valid, error, dt = validate_timestamp(timestamp)
            assert is_valid, f"Timestamp {timestamp} should be valid, but got error: {error}"
            assert dt is not None
            assert dt.tzinfo is not None

    def test_validate_timestamp_invalid(self):
        """测试无效时间戳验证"""
        invalid_timestamps = [
            "",  # 空时间戳
            "invalid",  # 无效格式
            "2023-13-01T00:00:00Z",  # 无效月份
            "2023-01-32T00:00:00Z",  # 无效日期
        ]
        for timestamp in invalid_timestamps:
            is_valid, error, dt = validate_timestamp(timestamp)
            assert not is_valid, f"Timestamp {timestamp} should be invalid"
            assert error is not None
            assert dt is None

    def test_sanitize_html_content(self):
        """测试HTML内容清理"""
        test_cases = [
            ("", ""),
            ("plain text", "plain text"),
            ("<script>alert('xss')</script>", "&lt;script&gt;alert(&#x27;xss&#x27;)&lt;/script&gt;"),
            ("<p>Hello</p>", "&lt;p&gt;Hello&lt;/p&gt;"),
            ("Test & Co.", "Test &amp; Co."),
        ]
        for input_content, expected in test_cases:
            result = sanitize_html_content(input_content)
            assert result == expected

    def test_validate_request_data_valid(self):
        """测试有效请求数据验证"""
        # 模拟Flask请求对象
        mock_request = mock.MagicMock()
        mock_request.content_type = 'application/json'
        mock_request.get_json.return_value = {"key": "value"}
        
        data, error = validate_request_data(mock_request)
        assert data == {"key": "value"}
        assert error is None

    def test_validate_request_data_invalid_content_type(self):
        """测试无效内容类型的请求数据验证"""
        mock_request = mock.MagicMock()
        mock_request.content_type = 'text/plain'
        
        data, error = validate_request_data(mock_request)
        assert data is None
        assert "application/json" in error

    def test_validate_request_data_invalid_json(self):
        """测试无效JSON的请求数据验证"""
        mock_request = mock.MagicMock()
        mock_request.content_type = 'application/json'
        mock_request.get_json.return_value = None
        
        data, error = validate_request_data(mock_request)
        assert data is None
        assert "JSON" in error


@pytest.mark.unit
class TestEmailPrefixGeneration:
    """测试邮箱前缀生成"""

    def test_generate_email_prefix_with_custom_prefix_first_attempt(self):
        """测试使用自定义前缀的第一次尝试"""
        result = generate_email_prefix("mytest", 0)
        assert result == "mytest"

    def test_generate_email_prefix_with_custom_prefix_collision(self):
        """测试自定义前缀冲突时的处理"""
        result = generate_email_prefix("mytest", 1)
        assert result.startswith("mytest-")
        assert len(result) > len("mytest-")

    @mock.patch('secrets.token_hex')
    def test_generate_email_prefix_without_custom_prefix(self, mock_token_hex):
        """测试不使用自定义前缀时的生成"""
        mock_token_hex.return_value = "abcdef"
        result = generate_email_prefix(None, 0)
        assert result == "abcdef"
        mock_token_hex.assert_called_once_with(6)

    @mock.patch('secrets.token_hex')
    def test_generate_email_prefix_empty_custom_prefix(self, mock_token_hex):
        """测试空自定义前缀时的生成"""
        mock_token_hex.return_value = "123456"
        result = generate_email_prefix("", 0)
        assert result == "123456"
        mock_token_hex.assert_called_once_with(6)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
