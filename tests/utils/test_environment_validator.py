#!/usr/bin/env python3
"""
测试环境验证工具
用于验证测试环境的配置和依赖是否正确
"""

import os
import sys
import sqlite3
import requests
import importlib
from pathlib import Path
from typing import Dict, List, Tuple, Any


class TestEnvironmentValidator:
    """测试环境验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        
    def validate_python_environment(self) -> bool:
        """验证Python环境"""
        self.info.append(f"Python版本: {sys.version}")
        self.info.append(f"Python路径: {sys.executable}")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            self.errors.append("Python版本过低，需要3.8或更高版本")
            return False
        
        return True
    
    def validate_required_modules(self) -> bool:
        """验证必需的Python模块"""
        required_modules = [
            'pytest',
            'requests',
            'sqlite3',
            'flask',
        ]
        
        optional_modules = [
            'psutil',
            'coverage',
        ]
        
        all_available = True
        
        for module in required_modules:
            try:
                importlib.import_module(module)
                self.info.append(f"✅ 必需模块 {module} 可用")
            except ImportError:
                self.errors.append(f"❌ 缺少必需模块: {module}")
                all_available = False
        
        for module in optional_modules:
            try:
                importlib.import_module(module)
                self.info.append(f"✅ 可选模块 {module} 可用")
            except ImportError:
                self.warnings.append(f"⚠️  可选模块 {module} 不可用")
        
        return all_available
    
    def validate_environment_variables(self) -> bool:
        """验证环境变量配置"""
        required_env_vars = {
            'TEST_BASE_URL': 'http://127.0.0.1:8080',
            'TEST_DATABASE_PATH': 'tests/database/test_tempmail.db',
            'TEST_DOMAIN_NAME': 'test.local',
        }
        
        optional_env_vars = {
            'TEST_EMAIL_EXPIRATION_HOURS': '1',
            'TEST_API_TIMEOUT': '30',
            'TEST_API_RETRY_ATTEMPTS': '3',
            'TEST_API_RETRY_DELAY': '1',
        }
        
        all_valid = True
        
        for var, default in required_env_vars.items():
            value = os.environ.get(var, default)
            if value:
                self.info.append(f"✅ 环境变量 {var}: {value}")
            else:
                self.errors.append(f"❌ 缺少环境变量: {var}")
                all_valid = False
        
        for var, default in optional_env_vars.items():
            value = os.environ.get(var, default)
            self.info.append(f"📝 可选环境变量 {var}: {value}")
        
        return all_valid
    
    def validate_database_setup(self) -> bool:
        """验证数据库设置"""
        db_path = os.environ.get('TEST_DATABASE_PATH', 'tests/database/test_tempmail.db')
        db_file = Path(db_path)
        
        # 检查数据库目录
        if not db_file.parent.exists():
            try:
                db_file.parent.mkdir(parents=True, exist_ok=True)
                self.info.append(f"✅ 创建数据库目录: {db_file.parent}")
            except Exception as e:
                self.errors.append(f"❌ 无法创建数据库目录: {e}")
                return False
        
        # 测试数据库连接
        try:
            with sqlite3.connect(str(db_file)) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                self.info.append(f"✅ 数据库连接正常: {db_path}")
                return True
        except Exception as e:
            self.errors.append(f"❌ 数据库连接失败: {e}")
            return False
    
    def validate_server_connectivity(self) -> bool:
        """验证服务器连接"""
        base_url = os.environ.get('TEST_BASE_URL', 'http://127.0.0.1:5001')
        
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                self.info.append(f"✅ 测试服务器可用: {base_url}")
                return True
            else:
                self.warnings.append(f"⚠️  测试服务器响应异常 ({response.status_code}): {base_url}")
                return False
        except requests.exceptions.ConnectionError:
            self.warnings.append(f"⚠️  无法连接到测试服务器: {base_url}")
            return False
        except requests.exceptions.Timeout:
            self.warnings.append(f"⚠️  测试服务器连接超时: {base_url}")
            return False
        except Exception as e:
            self.warnings.append(f"⚠️  测试服务器连接错误: {e}")
            return False
    
    def validate_stress_test_server(self) -> bool:
        """验证压力测试服务器"""
        stress_url = os.environ.get('STRESS_TEST_BASE_URL', 'http://127.0.0.1:5002')
        
        try:
            response = requests.get(stress_url, timeout=5)
            if response.status_code == 200:
                self.info.append(f"✅ 压力测试服务器可用: {stress_url}")
                return True
            else:
                self.warnings.append(f"⚠️  压力测试服务器响应异常 ({response.status_code}): {stress_url}")
                return False
        except Exception:
            self.warnings.append(f"⚠️  压力测试服务器不可用: {stress_url}")
            return False
    
    def validate_project_structure(self) -> bool:
        """验证项目结构"""
        project_root = Path(__file__).parent.parent.parent
        required_files = [
            'app.py',
            'tests/conftest.py',
            'tests/pytest.ini',
        ]
        
        required_dirs = [
            'tests',
            'tests/utils',
            'tests/stress',
            'tests/frontend',
        ]
        
        all_valid = True
        
        for file_path in required_files:
            full_path = project_root / file_path
            if full_path.exists():
                self.info.append(f"✅ 文件存在: {file_path}")
            else:
                self.errors.append(f"❌ 缺少文件: {file_path}")
                all_valid = False
        
        for dir_path in required_dirs:
            full_path = project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                self.info.append(f"✅ 目录存在: {dir_path}")
            else:
                self.errors.append(f"❌ 缺少目录: {dir_path}")
                all_valid = False
        
        return all_valid
    
    def validate_all(self) -> Tuple[bool, Dict[str, Any]]:
        """执行所有验证"""
        results = {
            'python_environment': self.validate_python_environment(),
            'required_modules': self.validate_required_modules(),
            'environment_variables': self.validate_environment_variables(),
            'database_setup': self.validate_database_setup(),
            'server_connectivity': self.validate_server_connectivity(),
            'stress_test_server': self.validate_stress_test_server(),
            'project_structure': self.validate_project_structure(),
        }
        
        overall_success = all(results.values())
        
        return overall_success, {
            'results': results,
            'errors': self.errors,
            'warnings': self.warnings,
            'info': self.info,
        }
    
    def print_report(self, validation_result: Tuple[bool, Dict[str, Any]]) -> None:
        """打印验证报告"""
        success, data = validation_result
        
        print("🔍 测试环境验证报告")
        print("=" * 60)
        
        # 打印验证结果
        print("\n📊 验证结果:")
        for category, result in data['results'].items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {category.replace('_', ' ').title()}: {status}")
        
        # 打印详细信息
        if data['info']:
            print("\n📝 详细信息:")
            for info in data['info']:
                print(f"  {info}")
        
        # 打印警告
        if data['warnings']:
            print("\n⚠️  警告:")
            for warning in data['warnings']:
                print(f"  {warning}")
        
        # 打印错误
        if data['errors']:
            print("\n❌ 错误:")
            for error in data['errors']:
                print(f"  {error}")
        
        # 总结
        print("\n" + "=" * 60)
        if success:
            print("🎉 环境验证通过！可以运行测试。")
        else:
            print("❌ 环境验证失败！请修复上述错误后重试。")
        
        print(f"总计: {len(data['errors'])} 个错误, {len(data['warnings'])} 个警告")


def main():
    """主函数"""
    validator = TestEnvironmentValidator()
    result = validator.validate_all()
    validator.print_report(result)
    
    success, _ = result
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
