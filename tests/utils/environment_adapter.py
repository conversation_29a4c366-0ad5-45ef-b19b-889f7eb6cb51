#!/usr/bin/env python3
"""
测试环境适配工具模块
提供统一的环境检测、服务器连接检查、依赖验证等功能
"""

import os
import sys
import time
import sqlite3
import requests
import pytest
from pathlib import Path
from typing import Dict, Any, Optional, Tuple


class EnvironmentAdapter:
    """环境适配器类"""
    
    def __init__(self):
        self.environment = self._detect_environment()
        self.config = self._load_environment_config()
    
    def _detect_environment(self) -> str:
        """检测当前运行环境"""
        if os.environ.get('CI'):
            return 'ci'
        elif os.environ.get('PYTEST_CURRENT_TEST'):
            return 'test'
        elif os.environ.get('FLASK_ENV') == 'development':
            return 'development'
        elif os.environ.get('FLASK_ENV') == 'production':
            return 'production'
        else:
            return 'development'  # 默认为开发环境
    
    def _load_environment_config(self) -> Dict[str, Any]:
        """加载环境配置"""
        base_config = {
            'TEST_BASE_URL': os.environ.get('TEST_BASE_URL', 'http://127.0.0.1:5001'),
            'TEST_DATABASE_PATH': os.environ.get('TEST_DATABASE_PATH', 'tests/database/test_tempmail.db'),
            'TEST_DOMAIN_NAME': os.environ.get('TEST_DOMAIN_NAME', 'test.local'),
            'TEST_EMAIL_EXPIRATION_HOURS': int(os.environ.get('TEST_EMAIL_EXPIRATION_HOURS', '1')),
            'TEST_API_TIMEOUT': int(os.environ.get('TEST_API_TIMEOUT', '30')),
            'TEST_API_RETRY_ATTEMPTS': int(os.environ.get('TEST_API_RETRY_ATTEMPTS', '3')),
            'TEST_API_RETRY_DELAY': float(os.environ.get('TEST_API_RETRY_DELAY', '1')),
        }
        
        # 根据环境调整配置
        if self.environment == 'ci':
            base_config.update({
                'TEST_API_TIMEOUT': 60,  # CI环境增加超时时间
                'TEST_SKIP_EXTERNAL_DEPS': True,
                'TEST_SKIP_SLOW_TESTS': True,
            })
        elif self.environment == 'production':
            base_config.update({
                'TEST_SKIP_DESTRUCTIVE_TESTS': True,
                'TEST_SKIP_PERFORMANCE_TESTS': True,
            })
        
        return base_config
    
    def is_server_available(self, url: Optional[str] = None, timeout: int = 5) -> bool:
        """检查服务器是否可用"""
        if url is None:
            url = self.config['TEST_BASE_URL']
        
        try:
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except Exception:
            return False
    
    def is_database_available(self, db_path: Optional[str] = None) -> bool:
        """检查数据库是否可用"""
        if db_path is None:
            db_path = self.config['TEST_DATABASE_PATH']
        
        try:
            if not db_path or not os.path.exists(str(db_path)):
                return False
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return True
        except Exception:
            return False
    
    def check_external_dependency(self, module_name: str) -> bool:
        """检查外部依赖是否可用"""
        if not module_name:
            return True  # 空模块名认为是可用的
        try:
            __import__(module_name)
            return True
        except ImportError:
            return False
    
    def get_skip_condition(self, condition_type: str, **kwargs) -> pytest.MarkDecorator:
        """获取跳过条件装饰器"""
        conditions = {
            'no_server': (
                not self.is_server_available(),
                f"测试服务器不可用: {self.config['TEST_BASE_URL']}"
            ),
            'no_database': (
                not self.is_database_available(),
                f"测试数据库不可用: {self.config['TEST_DATABASE_PATH']}"
            ),
            'external_deps': (
                self.config.get('TEST_SKIP_EXTERNAL_DEPS', False),
                "跳过外部依赖测试"
            ),
            'destructive': (
                self.config.get('TEST_SKIP_DESTRUCTIVE_TESTS', False),
                "生产环境跳过破坏性测试"
            ),
            'slow': (
                self.config.get('TEST_SKIP_SLOW_TESTS', False),
                "跳过慢速测试"
            ),
            'performance': (
                self.config.get('TEST_SKIP_PERFORMANCE_TESTS', False),
                "跳过性能测试"
            ),
            'missing_module': (
                not self.check_external_dependency(kwargs.get('module', '')),
                f"缺少依赖模块: {kwargs.get('module', '')}"
            ),
        }
        
        if condition_type not in conditions:
            raise ValueError(f"未知的跳过条件类型: {condition_type}")
        
        should_skip, reason = conditions[condition_type]
        return pytest.mark.skipif(should_skip, reason=reason)
    
    def wait_for_server(self, url: Optional[str] = None, timeout: int = 30, interval: float = 1.0) -> bool:
        """等待服务器可用"""
        if url is None:
            url = self.config['TEST_BASE_URL']
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_server_available(url):
                return True
            time.sleep(interval)
        return False
    
    def get_test_config(self, key: str, default: Any = None) -> Any:
        """获取测试配置值"""
        return self.config.get(key, default)
    
    def setup_test_environment(self) -> None:
        """设置测试环境"""
        # 设置环境变量
        for key, value in self.config.items():
            if key.startswith('TEST_') and key not in os.environ:
                os.environ[key] = str(value)
        
        # 设置Python路径
        project_root = Path(__file__).parent.parent.parent
        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))
    
    def validate_environment(self) -> Tuple[bool, list]:
        """验证环境配置"""
        errors = []
        
        # 检查必需的配置
        required_configs = ['TEST_BASE_URL', 'TEST_DATABASE_PATH', 'TEST_DOMAIN_NAME']
        for config in required_configs:
            if not self.config.get(config):
                errors.append(f"缺少必需配置: {config}")
        
        # 检查数据库目录
        db_path = Path(self.config['TEST_DATABASE_PATH'])
        if not db_path.parent.exists():
            try:
                db_path.parent.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建数据库目录: {e}")
        
        return len(errors) == 0, errors
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            'environment': self.environment,
            'config': self.config,
            'server_available': self.is_server_available(),
            'database_available': self.is_database_available(),
            'python_version': sys.version,
            'platform': sys.platform,
        }


# 全局环境适配器实例
env_adapter = EnvironmentAdapter()

# 便捷函数
def skip_if_no_server():
    """如果服务器不可用则跳过测试"""
    return env_adapter.get_skip_condition('no_server')

def skip_if_no_database():
    """如果数据库不可用则跳过测试"""
    return env_adapter.get_skip_condition('no_database')

def skip_if_external_deps():
    """如果设置跳过外部依赖则跳过测试"""
    return env_adapter.get_skip_condition('external_deps')

def skip_if_destructive():
    """如果是生产环境则跳过破坏性测试"""
    return env_adapter.get_skip_condition('destructive')

def skip_if_slow():
    """如果设置跳过慢速测试则跳过测试"""
    return env_adapter.get_skip_condition('slow')

def skip_if_missing_module(module_name: str):
    """如果缺少指定模块则跳过测试"""
    return env_adapter.get_skip_condition('missing_module', module=module_name)

def requires_server(func):
    """装饰器：需要服务器连接的测试"""
    return skip_if_no_server()(func)

def requires_database(func):
    """装饰器：需要数据库连接的测试"""
    return skip_if_no_database()(func)

def requires_external_deps(func):
    """装饰器：需要外部依赖的测试"""
    return skip_if_external_deps()(func)


if __name__ == "__main__":
    # 环境检测和验证
    adapter = EnvironmentAdapter()
    
    print("🔍 环境检测结果:")
    print("=" * 50)
    
    info = adapter.get_environment_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    print("\n🔧 环境验证:")
    print("=" * 50)
    
    is_valid, errors = adapter.validate_environment()
    if is_valid:
        print("✅ 环境配置有效")
    else:
        print("❌ 环境配置存在问题:")
        for error in errors:
            print(f"  - {error}")
