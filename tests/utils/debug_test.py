#!/usr/bin/env python3
"""
调试脚本 - 测试邮箱创建功能
"""

import requests
import json
import traceback

# 测试配置
BASE_URL = "http://localhost:5000"

def test_create_email():
    """测试创建邮箱"""
    print("测试创建邮箱...")
    
    url = f"{BASE_URL}/api/generate-address"
    data = {
        "session_id": "test_session_123",
        "custom_prefix": "test123"
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, indent=2)}")
        except:
            print(f"响应文本: {response.text}")
            
        return response_data if response.status_code == 200 else None
        
    except Exception as e:
        print(f"请求失败: {e}")
        traceback.print_exc()
        return None

def check_server():
    """检查服务器状态"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"服务器状态: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("邮箱创建功能调试")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server():
        print("错误: 无法连接到服务器")
        exit(1)
    
    # 测试创建邮箱
    result = test_create_email()
    
    if result:
        print("\n✓ 测试成功")
    else:
        print("\n✗ 测试失败")
