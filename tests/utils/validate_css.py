#!/usr/bin/env python3
"""
CSS文件验证脚本
验证高分辨率适配CSS文件的语法和结构
"""

import os
import re
import sys

def validate_css_file(file_path):
    """验证CSS文件的基本语法"""
    print(f"验证CSS文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False
    
    # 基本语法检查
    issues = []
    
    # 检查括号匹配
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        issues.append(f"括号不匹配: 开括号 {open_braces} 个, 闭括号 {close_braces} 个")
    
    # 检查媒体查询语法
    media_queries = re.findall(r'@media[^{]+{', content)
    for i, mq in enumerate(media_queries):
        if not mq.strip().endswith('{'):
            issues.append(f"媒体查询 {i+1} 语法可能有误: {mq[:50]}...")
    
    # 检查CSS属性语法
    properties = re.findall(r'[a-zA-Z-]+\s*:\s*[^;]+;', content)
    for prop in properties:
        if not re.match(r'^[a-zA-Z-]+\s*:\s*.+;$', prop.strip()):
            issues.append(f"CSS属性语法可能有误: {prop[:30]}...")
    
    # 检查分辨率断点
    breakpoints = {
        '1920px': False,
        '2560px': False, 
        '3840px': False,
        'device-pixel-ratio': False
    }
    
    for bp in breakpoints:
        if bp in content:
            breakpoints[bp] = True
    
    print("📊 分辨率断点检查:")
    for bp, found in breakpoints.items():
        status = "✅" if found else "⚠️"
        print(f"  {status} {bp}: {'已定义' if found else '未找到'}")
    
    if issues:
        print("⚠️ 发现的问题:")
        for issue in issues[:5]:  # 只显示前5个问题
            print(f"  - {issue}")
        if len(issues) > 5:
            print(f"  ... 还有 {len(issues) - 5} 个问题")
        return False
    else:
        print("✅ CSS语法检查通过")
        return True

def check_media_query_coverage():
    """检查媒体查询覆盖范围"""
    print("\n📱 媒体查询覆盖范围分析:")
    
    ranges = [
        ("移动端", "max-width: 768px"),
        ("平板端", "769px - 1024px"), 
        ("桌面端", "1025px - 1919px"),
        ("1080p", "1920px - 2559px"),
        ("2K", "2560px - 3839px"),
        ("4K+", "≥3840px"),
        ("高DPI", "device-pixel-ratio ≥ 2")
    ]
    
    for name, range_desc in ranges:
        print(f"  📐 {name}: {range_desc}")
    
    print("\n✅ 覆盖了从移动端到4K+的完整分辨率范围")

def validate_file_structure():
    """验证文件结构"""
    print("\n📁 文件结构检查:")
    
    files_to_check = [
        "static/css/high-resolution.css",
        "static/css/styles.css", 
        "static/css/style.css",
        "tests/test_high_resolution.html",
        "docs/HIGH_RESOLUTION_ADAPTATION_README.md"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")

def main():
    """主函数"""
    print("🔍 高分辨率适配CSS验证工具")
    print("=" * 50)
    
    # 验证主要CSS文件
    css_files = [
        "static/css/high-resolution.css",
        "static/css/styles.css"
    ]
    
    all_valid = True
    for css_file in css_files:
        if not validate_css_file(css_file):
            all_valid = False
        print()
    
    # 检查媒体查询覆盖
    check_media_query_coverage()
    
    # 验证文件结构
    validate_file_structure()
    
    print("\n" + "=" * 50)
    if all_valid:
        print("🎉 所有检查通过！高分辨率适配已成功实现")
        print("\n📋 测试建议:")
        print("1. 在浏览器中打开 http://127.0.0.1:5000")
        print("2. 使用开发者工具测试不同分辨率")
        print("3. 打开 tests/test_high_resolution.html 查看测试页面")
        print("4. 在真实的高分辨率显示器上测试")
    else:
        print("⚠️ 发现一些问题，请检查上述输出")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
