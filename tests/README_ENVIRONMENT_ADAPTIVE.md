# 环境自适应测试系统

本文档介绍了临时邮箱系统的环境自适应测试功能，该系统能够根据不同的运行环境自动调整测试配置和执行策略。

## 🌟 主要特性

### 1. 自动环境检测
- **开发环境**: 本地开发时的默认环境
- **测试环境**: 专门的测试环境
- **CI环境**: 持续集成环境（通过CI环境变量检测）
- **生产环境**: 生产环境（通过FLASK_ENV=production检测）

### 2. 智能测试跳过
- **服务器连接检查**: 自动检测服务器可用性，不可用时跳过相关测试
- **外部依赖检测**: 检测可选依赖模块，缺失时跳过相关测试
- **环境适配**: 根据环境类型自动跳过不适合的测试

### 3. 配置自适应
- **超时时间**: CI环境自动增加超时时间
- **重试机制**: 根据环境调整API重试参数
- **日志级别**: 不同环境使用不同的日志详细程度

## 🚀 快速开始

### 基本使用

```bash
# 运行所有测试（自动环境适配）
python tests/run_adaptive_tests.py

# 运行快速测试（排除慢速测试）
python tests/run_adaptive_tests.py --type fast

# 运行API测试
python tests/run_adaptive_tests.py --type api

# 显示环境信息
python tests/run_adaptive_tests.py --env-info

# 列出所有测试类型
python tests/run_adaptive_tests.py --list-types
```

### 环境验证

```bash
# 验证测试环境配置
python tests/utils/test_environment_validator.py

# 跳过环境验证直接运行测试
python tests/run_adaptive_tests.py --no-validate
```

## 🔧 环境配置

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `TEST_BASE_URL` | `http://127.0.0.1:8080` | 测试服务器地址 |
| `TEST_DATABASE_PATH` | `tests/database/test_tempmail.db` | 测试数据库路径 |
| `TEST_DOMAIN_NAME` | `test.local` | 测试域名 |
| `TEST_API_TIMEOUT` | `30` | API请求超时时间（秒） |
| `TEST_SKIP_EXTERNAL_DEPS` | `false` | 是否跳过外部依赖测试 |
| `TEST_SKIP_DESTRUCTIVE_TESTS` | `false` | 是否跳过破坏性测试 |
| `TEST_SKIP_SLOW_TESTS` | `false` | 是否跳过慢速测试 |
| `STRESS_TEST_BASE_URL` | `http://127.0.0.1:8081` | 压力测试服务器地址 |

### 环境特定配置

#### 开发环境
```bash
export TEST_BASE_URL=http://localhost:8080
export TEST_SKIP_SLOW_TESTS=false
```

#### CI环境
```bash
export CI=true
export TEST_SKIP_EXTERNAL_DEPS=true
export TEST_SKIP_SLOW_TESTS=true
export TEST_API_TIMEOUT=60
```

#### 生产环境
```bash
export FLASK_ENV=production
export TEST_SKIP_DESTRUCTIVE_TESTS=true
export TEST_SKIP_PERFORMANCE_TESTS=true
```

## 📋 测试类型

| 类型 | 标记 | 说明 |
|------|------|------|
| `all` | - | 运行所有测试 |
| `fast` | `not slow and not stress` | 快速测试 |
| `unit` | `unit` | 单元测试 |
| `integration` | `integration` | 集成测试 |
| `api` | `api` | API测试 |
| `performance` | `performance` | 性能测试 |
| `stress` | `stress` | 压力测试 |
| `frontend` | `frontend` | 前端测试 |
| `requires_server` | `requires_server` | 需要服务器的测试 |
| `no_external_deps` | `not requires_external_deps` | 不需要外部依赖的测试 |

## 🛠️ 高级用法

### 自定义pytest参数

```bash
# 传递额外的pytest参数
python tests/run_adaptive_tests.py --type api -- --maxfail=1 --tb=short

# 运行特定测试文件
python tests/run_adaptive_tests.py -- tests/test_api.py

# 使用覆盖率报告
python tests/run_adaptive_tests.py -- --cov=app --cov-report=html
```

### 环境检测和跳过

```python
# 在测试中使用环境适配装饰器
from tests.utils.environment_adapter import skip_if_no_server, skip_if_slow

@skip_if_no_server()
def test_api_endpoint():
    """需要服务器连接的测试"""
    pass

@skip_if_slow()
def test_performance():
    """慢速测试"""
    pass
```

### 手动环境检测

```python
from tests.utils.environment_adapter import env_adapter

# 检查服务器可用性
if env_adapter.is_server_available():
    # 运行需要服务器的测试
    pass

# 获取环境配置
timeout = env_adapter.get_test_config('TEST_API_TIMEOUT', 30)
```

## 🔍 故障排除

### 常见问题

1. **服务器连接失败**
   ```bash
   # 检查服务器状态
   curl http://127.0.0.1:5001/
   
   # 或使用环境验证工具
   python tests/utils/test_environment_validator.py
   ```

2. **数据库连接问题**
   ```bash
   # 检查数据库目录权限
   ls -la tests/database/
   
   # 手动创建数据库目录
   mkdir -p tests/database/
   ```

3. **依赖模块缺失**
   ```bash
   # 安装可选依赖
   pip install psutil coverage
   ```

### 调试模式

```bash
# 显示详细的环境信息
python tests/run_adaptive_tests.py --env-info

# 验证环境配置
python tests/utils/test_environment_validator.py

# 运行单个测试文件进行调试
python tests/run_adaptive_tests.py -- tests/test_api.py -v -s
```

## 📊 测试报告

### 生成覆盖率报告

```bash
# HTML覆盖率报告
python tests/run_adaptive_tests.py -- --cov=app --cov-report=html

# 终端覆盖率报告
python tests/run_adaptive_tests.py -- --cov=app --cov-report=term-missing
```

### 性能测试报告

```bash
# 运行性能测试并生成报告
python tests/run_adaptive_tests.py --type performance

# 运行压力测试
python tests/run_adaptive_tests.py --type stress
```

## 🔄 持续集成

### GitHub Actions示例

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest coverage
    
    - name: Run adaptive tests
      env:
        CI: true
        TEST_SKIP_EXTERNAL_DEPS: true
        TEST_SKIP_SLOW_TESTS: true
      run: python tests/run_adaptive_tests.py --type fast
```

## 📝 最佳实践

1. **环境隔离**: 为不同环境使用不同的配置文件
2. **依赖检测**: 始终检查外部依赖的可用性
3. **优雅降级**: 在依赖不可用时提供替代方案
4. **清晰错误**: 提供明确的错误信息和解决建议
5. **文档更新**: 保持环境配置文档的及时更新

## 🤝 贡献指南

1. 添加新的环境检测逻辑时，请更新 `environment_adapter.py`
2. 新增测试标记时，请同时更新 `pytest.ini` 和本文档
3. 修改环境配置时，请确保向后兼容性
4. 提交前请运行完整的环境验证测试

---

**最后更新**: 2024年12月
**维护者**: AI Assistant
**状态**: ✅ 活跃维护
