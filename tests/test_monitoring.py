#!/usr/bin/env python3
"""
监控系统测试脚本

用于测试轻量级监控系统的功能
"""

import os
import sys
import time
import requests
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from monitoring.config import MonitoringConfig
from monitoring.lightweight_monitor import LightweightMonitor


def test_monitoring_config():
    """测试监控配置"""
    print("🔧 测试监控配置...")
    
    # 测试默认配置
    config = MonitoringConfig()
    print(f"  默认配置级别: {config.level}")
    print(f"  指标采集间隔: {config.metrics_interval}秒")
    print(f"  内存限制: {config.max_memory_mb}MB")
    
    # 测试配置验证
    errors = config.validate()
    if errors:
        print(f"  配置错误: {errors}")
    else:
        print("  ✅ 配置验证通过")
    
    # 测试环境变量配置
    os.environ['MONITORING_LEVEL'] = 'detailed'
    os.environ['MONITORING_METRICS_INTERVAL'] = '30'
    
    env_config = MonitoringConfig.from_env()
    print(f"  环境变量配置级别: {env_config.level}")
    print(f"  环境变量指标间隔: {env_config.metrics_interval}秒")
    
    print("✅ 配置测试完成\n")


def test_lightweight_monitor():
    """测试轻量级监控器"""
    print("📊 测试轻量级监控器...")
    
    # 创建测试配置
    config = MonitoringConfig(
        enabled=True,
        level='basic',
        metrics_interval=5,  # 5秒间隔用于测试
        health_check_interval=10,
        max_memory_mb=30
    )
    
    # 初始化监控器
    monitor = LightweightMonitor(config)
    
    try:
        # 启动监控
        print("  启动监控器...")
        monitor.start()
        
        # 等待一些指标收集
        print("  等待指标收集...")
        time.sleep(12)  # 等待至少一次指标收集和健康检查
        
        # 获取状态
        status = monitor.get_status()
        print(f"  监控状态: {'运行中' if status['running'] else '已停止'}")
        print(f"  运行时间: {status['uptime_seconds']:.1f}秒")
        print(f"  线程数: {status['threads_count']}")
        
        # 获取当前指标
        metrics = monitor.get_current_metrics()
        if 'error' not in metrics:
            print("  ✅ 指标收集正常")
            if 'system' in metrics:
                cpu = metrics['system'].get('cpu_percent', 0)
                memory = metrics['system'].get('memory', {}).get('percent', 0)
                print(f"    CPU使用率: {cpu:.1f}%")
                print(f"    内存使用率: {memory:.1f}%")
        else:
            print(f"  ❌ 指标收集失败: {metrics['error']}")
        
        # 获取指标摘要
        summary = monitor.metrics_collector.get_metrics_summary(1)
        if 'error' not in summary:
            print(f"  指标摘要: {summary['data_points']} 个数据点")
        
        # 执行健康检查
        health = monitor.force_health_check()
        print(f"  健康检查: {'✅ 通过' if health['healthy'] else '❌ 失败'}")
        if not health['healthy']:
            print(f"    失败原因: {health['message']}")
        
        # 测试告警
        print("  测试告警功能...")
        monitor.alert_manager.send_alert(
            'test_alert',
            '这是一个测试告警',
            'warning',
            {'test': True}
        )
        
        active_alerts = monitor.alert_manager.get_active_alerts()
        print(f"  活跃告警数: {len(active_alerts)}")
        
    finally:
        # 停止监控
        print("  停止监控器...")
        monitor.stop()
    
    print("✅ 监控器测试完成\n")


def test_flask_integration():
    """测试Flask集成"""
    print("🌐 测试Flask集成...")
    
    try:
        from app import app
        
        # 检查监控是否已集成
        if hasattr(app, 'monitoring') and app.monitoring:
            print("  ✅ Flask监控集成已启用")
            
            # 测试监控API端点
            with app.test_client() as client:
                # 测试监控状态端点
                response = client.get('/api/monitoring/status')
                if response.status_code == 200:
                    print("  ✅ 监控状态API正常")
                    data = response.get_json()
                    print(f"    监控级别: {data.get('level', 'unknown')}")
                else:
                    print(f"  ❌ 监控状态API失败: {response.status_code}")
                
                # 测试指标API端点
                response = client.get('/api/monitoring/metrics')
                if response.status_code == 200:
                    print("  ✅ 指标API正常")
                else:
                    print(f"  ❌ 指标API失败: {response.status_code}")
                
                # 测试健康检查API端点
                response = client.get('/api/monitoring/health')
                if response.status_code == 200:
                    print("  ✅ 健康检查API正常")
                    data = response.get_json()
                    print(f"    健康状态: {'正常' if data.get('healthy') else '异常'}")
                else:
                    print(f"  ❌ 健康检查API失败: {response.status_code}")
        else:
            print("  ❌ Flask监控集成未启用")
    
    except ImportError as e:
        print(f"  ❌ 无法导入Flask应用: {e}")
    except Exception as e:
        print(f"  ❌ Flask集成测试失败: {e}")
    
    print("✅ Flask集成测试完成\n")


def test_resource_usage():
    """测试资源使用情况"""
    print("💾 测试资源使用情况...")
    
    import psutil
    
    # 获取当前进程
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"  初始内存使用: {initial_memory:.1f}MB")
    
    # 创建监控器
    config = MonitoringConfig(
        enabled=True,
        level='standard',
        metrics_interval=2,
        health_check_interval=5
    )
    
    monitor = LightweightMonitor(config)
    
    try:
        # 启动监控
        monitor.start()
        
        # 运行一段时间
        print("  运行监控30秒...")
        for i in range(6):
            time.sleep(5)
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = current_memory - initial_memory
            print(f"    {(i+1)*5}秒: 内存使用 {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
        
        # 最终内存使用
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"  最终内存使用: {final_memory:.1f}MB")
        print(f"  总内存增加: {total_increase:.1f}MB")
        
        if total_increase < 30:  # 小于30MB增加
            print("  ✅ 内存使用在可接受范围内")
        else:
            print("  ⚠️ 内存使用较高")
        
        # 获取监控器自身的内存使用估算
        monitor_memory = monitor.metrics_collector.get_memory_usage()
        print(f"  监控器估算内存使用: {monitor_memory:.1f}MB")
        
    finally:
        monitor.stop()
    
    print("✅ 资源使用测试完成\n")


def main():
    """主测试函数"""
    print("🚀 开始监控系统测试\n")
    
    # 设置测试环境变量
    os.environ['MONITORING_ENABLED'] = 'true'
    os.environ['MONITORING_EMAIL_ENABLED'] = 'false'  # 禁用邮件通知避免测试时发送邮件
    
    try:
        # 运行各项测试
        test_monitoring_config()
        test_lightweight_monitor()
        test_flask_integration()
        test_resource_usage()
        
        print("🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
