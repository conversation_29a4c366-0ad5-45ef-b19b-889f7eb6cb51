#!/usr/bin/env python3
"""
验证压力测试配置脚本
检查速率限制是否已正确禁用

作者：Augment Agent
日期：2025-01-27
"""

import os
import sys
from pathlib import Path

# 确保在项目根目录
project_root = Path(__file__).resolve().parent
os.chdir(project_root)
sys.path.insert(0, str(project_root))

def test_rate_limit_bypass():
    """测试速率限制绕过功能"""
    print("🧪 测试速率限制绕过功能...")

    try:
        from app import app, rate_limit

        # 确保Flask应用配置正确
        app.config['TESTING'] = True
        app.config['STRESS_TESTING'] = True
        app.config['DISABLE_RATE_LIMIT'] = True

        with app.app_context():
            with app.test_request_context():
                # 验证配置是否生效
                print(f"   Flask配置 - TESTING: {app.config.get('TESTING')}")
                print(f"   Flask配置 - STRESS_TESTING: {app.config.get('STRESS_TESTING')}")
                print(f"   Flask配置 - DISABLE_RATE_LIMIT: {app.config.get('DISABLE_RATE_LIMIT')}")

                # 创建一个严格的速率限制装饰器（1次/60秒）
                @rate_limit(max_requests=1, window_seconds=60)
                def test_function():
                    return "success"

                # 测试多次调用
                print("📊 测试结果:")
                success_count = 0

                for i in range(5):
                    try:
                        result = test_function()
                        # 检查结果是否为字符串 "success" 或者是否被速率限制
                        if result == "success":
                            success_count += 1
                            print(f"   ✅ 调用 {i+1}: 成功")
                        elif isinstance(result, tuple) and len(result) == 2:
                            # 这是一个错误响应元组 (response, status_code)
                            print(f"   ❌ 调用 {i+1}: 被速率限制 (状态码: {result[1]})")
                        else:
                            print(f"   ❌ 调用 {i+1}: 未知结果 - {result}")
                    except Exception as e:
                        print(f"   ❌ 调用 {i+1}: 异常 - {e}")

                print(f"\n📈 成功率: {success_count}/5 ({success_count/5*100:.0f}%)")

                if success_count == 5:
                    print("✅ 速率限制已成功禁用！")
                    return True
                else:
                    print("❌ 速率限制仍然生效")
                    return False

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_database_optimization():
    """测试数据库优化配置"""
    print("\n🗄️  测试数据库优化配置...")
    
    try:
        from app import app, get_db_connection
        
        with app.app_context():
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查数据库配置
            configs = [
                ('journal_mode', ['WAL', 'wal']),
                ('synchronous', ['NORMAL', '1', 1]),  # SQLite返回1表示NORMAL
                ('temp_store', ['MEMORY', '2', 2])    # SQLite返回2表示MEMORY
            ]

            print("📊 数据库配置:")
            all_correct = True

            for config_name, expected_values in configs:
                cursor.execute(f"PRAGMA {config_name}")
                actual_value = cursor.fetchone()[0]

                if str(actual_value) in [str(v) for v in expected_values]:
                    print(f"   ✅ {config_name}: {actual_value}")
                else:
                    print(f"   ❌ {config_name}: {actual_value} (期望: {expected_values[0]})")
                    all_correct = False
            
            # 检查缓存大小
            cursor.execute("PRAGMA cache_size")
            cache_size = cursor.fetchone()[0]
            print(f"   📦 cache_size: {cache_size}")
            
            conn.close()
            
            if all_correct:
                print("✅ 数据库优化配置正确！")
                return True
            else:
                print("⚠️  部分数据库配置可能需要调整")
                return False
                
    except Exception as e:
        print(f"❌ 测试数据库配置时发生错误: {e}")
        return False

def test_environment_variables():
    """测试环境变量配置"""
    print("\n🌍 测试环境变量配置...")
    
    required_vars = {
        'STRESS_TESTING': 'true',
        'DISABLE_RATE_LIMIT': 'true',
        'TESTING': 'true'
    }
    
    print("📊 环境变量:")
    all_correct = True
    
    for var_name, expected_value in required_vars.items():
        actual_value = os.environ.get(var_name, '未设置')
        
        if actual_value.lower() == expected_value.lower():
            print(f"   ✅ {var_name}: {actual_value}")
        else:
            print(f"   ❌ {var_name}: {actual_value} (期望: {expected_value})")
            all_correct = False
    
    if all_correct:
        print("✅ 环境变量配置正确！")
        return True
    else:
        print("❌ 环境变量配置不正确")
        return False

def main():
    """主函数"""
    print("🔍 压力测试配置验证")
    print("=" * 50)

    # 首先设置压力测试环境变量
    print("🔧 设置压力测试环境变量...")
    os.environ['STRESS_TESTING'] = 'true'
    os.environ['DISABLE_RATE_LIMIT'] = 'true'
    os.environ['TESTING'] = 'true'
    print("✅ 环境变量已设置")

    # 运行所有测试
    tests = [
        ("环境变量配置", test_environment_variables),
        ("速率限制绕过", test_rate_limit_bypass),
        ("数据库优化", test_database_optimization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过 ({passed/total*100:.0f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！压力测试环境配置正确。")
        print("💡 现在可以运行压力测试:")
        print("   python run_stress_test.py --quick-test")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
