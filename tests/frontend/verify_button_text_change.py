#!/usr/bin/env python3
"""
验证"历史记录"按钮文本修改为"切换邮箱"的脚本
"""

import os
import re

def verify_html_changes():
    """验证HTML文件中的按钮文本修改"""
    print("🔍 验证 templates/index.html 中的按钮文本修改...")
    
    html_file = "templates/index.html"
    if not os.path.exists(html_file):
        print(f"❌ 文件不存在: {html_file}")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查按钮ID和data-i18n属性保持不变
    history_btn_pattern = r'<button id="history-btn"[^>]*>'
    history_btn_match = re.search(history_btn_pattern, content)
    
    if not history_btn_match:
        print("❌ 未找到 history-btn 按钮")
        return False
    
    print("✅ 找到 history-btn 按钮")
    
    # 检查按钮内容
    btn_content_pattern = r'<button id="history-btn"[^>]*>.*?</button>'
    btn_content_match = re.search(btn_content_pattern, content, re.DOTALL)
    
    if btn_content_match:
        btn_content = btn_content_match.group(0)
        print(f"📝 按钮内容: {btn_content[:100]}...")
        
        # 检查是否包含正确的文本
        if '切换邮箱' in btn_content:
            print("✅ 按钮显示文本已更新为'切换邮箱'")
        else:
            print("❌ 按钮显示文本未正确更新")
            return False
            
        # 检查data-i18n属性
        if 'data-i18n="button.history"' in btn_content:
            print("✅ data-i18n属性保持不变")
        else:
            print("❌ data-i18n属性被意外修改")
            return False
            
        # 检查图标
        if 'fas fa-history' in btn_content:
            print("✅ 按钮图标保持不变")
        else:
            print("❌ 按钮图标被意外修改")
            return False
            
        # 检查CSS类名
        if 'bg-blue-500 hover:bg-blue-600' in btn_content:
            print("✅ 按钮CSS类名保持不变")
        else:
            print("❌ 按钮CSS类名被意外修改")
            return False
    
    return True

def verify_i18n_changes():
    """验证国际化文件中的翻译修改"""
    print("\n🌐 验证 static/js/i18n.js 中的翻译修改...")
    
    i18n_file = "static/js/i18n.js"
    if not os.path.exists(i18n_file):
        print(f"❌ 文件不存在: {i18n_file}")
        return False
    
    with open(i18n_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查中文翻译
    zh_pattern = r"'button\.history':\s*'([^']+)'"
    zh_matches = re.findall(zh_pattern, content)
    
    if len(zh_matches) >= 1:
        zh_text = zh_matches[0]
        if zh_text == '切换邮箱':
            print("✅ 中文翻译已更新为'切换邮箱'")
        else:
            print(f"❌ 中文翻译错误: '{zh_text}', 应该是'切换邮箱'")
            return False
    else:
        print("❌ 未找到中文翻译")
        return False
    
    # 检查英文翻译
    if len(zh_matches) >= 2:
        en_text = zh_matches[1]
        if en_text == 'Switch Email':
            print("✅ 英文翻译已更新为'Switch Email'")
        else:
            print(f"❌ 英文翻译错误: '{en_text}', 应该是'Switch Email'")
            return False
    else:
        print("❌ 未找到英文翻译")
        return False
    
    return True

def verify_functionality_preserved():
    """验证功能逻辑是否保持不变"""
    print("\n⚙️ 验证功能逻辑是否保持不变...")
    
    # 检查HTML中的按钮ID
    html_file = "templates/index.html"
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    if 'id="history-btn"' in html_content:
        print("✅ 按钮ID保持不变")
    else:
        print("❌ 按钮ID被意外修改")
        return False
    
    # 检查JavaScript文件中是否仍然引用正确的ID
    js_files = ['static/js/main.js', 'static/js/mobile.js']
    
    for js_file in js_files:
        if os.path.exists(js_file):
            with open(js_file, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            if 'history-btn' in js_content:
                print(f"✅ {js_file} 中的按钮引用保持不变")
            else:
                print(f"ℹ️ {js_file} 中未找到按钮引用（可能正常）")
    
    return True

def main():
    """主函数"""
    print("🔍 验证'历史记录'按钮文本修改为'切换邮箱'")
    print("=" * 60)
    
    success = True
    
    # 验证HTML修改
    if not verify_html_changes():
        success = False
    
    # 验证国际化修改
    if not verify_i18n_changes():
        success = False
    
    # 验证功能保持不变
    if not verify_functionality_preserved():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有验证通过！按钮文本修改成功")
        print("\n📋 修改总结:")
        print("✅ 按钮显示文本: '历史记录' → '切换邮箱'")
        print("✅ 中文翻译: '历史记录' → '切换邮箱'")
        print("✅ 英文翻译: 'History' → 'Switch Email'")
        print("✅ 按钮ID、CSS类名、图标保持不变")
        print("✅ 功能逻辑保持不变")
        print("\n🌐 测试建议:")
        print("1. 在浏览器中访问 http://127.0.0.1:5000")
        print("2. 检查按钮显示为'切换邮箱'")
        print("3. 切换语言测试英文显示为'Switch Email'")
        print("4. 点击按钮确认功能正常")
    else:
        print("❌ 验证失败，请检查上述错误")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
