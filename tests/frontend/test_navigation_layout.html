<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏响应式布局测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="static/css/styles.css">
    <style>
        /* 测试辅助样式 */
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .breakpoint-indicator {
            background: #f0f0f0;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .test-section {
            margin: 20px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        /* 显示当前断点 */
        .current-breakpoint::before {
            content: "当前断点: ";
            font-weight: bold;
        }
        
        .desktop::before { content: "当前断点: 桌面端 (>768px)"; }
        .tablet::before { content: "当前断点: 平板端 (429px-768px)"; }
        .mobile::before { content: "当前断点: 移动端 (≤428px)"; }
        .small-mobile::before { content: "当前断点: 小屏手机 (≤375px)"; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 测试信息显示 -->
    <div class="test-info">
        <div>屏幕宽度: <span id="screen-width"></span>px</div>
        <div class="current-breakpoint" id="breakpoint-info"></div>
    </div>

    <!-- 导航栏测试 -->
    <header class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-4">
            <div class="header-content flex justify-between items-center">
                <div class="header-brand flex items-center">
                    <i class="fas fa-envelope text-2xl mr-2"></i>
                    <h1 class="text-xl font-bold">临时邮箱</h1>
                </div>
                <nav class="header-nav">
                    <ul class="nav-list flex space-x-6 items-center">
                        <li class="nav-item"><a href="#" class="nav-link hover:underline">首页</a></li>
                        <li class="nav-item"><a href="#" class="nav-link hover:underline">常见问题</a></li>
                        <li class="nav-item"><a href="#" class="nav-link hover:underline">联系我们</a></li>
                        <!-- 语言切换器 -->
                        <li class="nav-item language-selector-item relative">
                            <select class="language-selector bg-blue-500 text-white border border-blue-400 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300">
                                <option value="zh-CN">中文</option>
                                <option value="en-US">English</option>
                            </select>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- 测试内容 -->
    <main class="container mx-auto px-4 py-8">
        <div class="breakpoint-indicator">
            <h2 class="text-2xl font-bold mb-4">导航栏响应式布局测试</h2>
            <p>请调整浏览器窗口大小来测试不同断点下的导航栏布局</p>
        </div>

        <div class="test-section">
            <h3 class="text-xl font-bold mb-4">测试要点</h3>
            <ul class="list-disc list-inside space-y-2">
                <li><strong>桌面端 (>768px)</strong>: 导航项水平排列，语言选择器右对齐</li>
                <li><strong>平板端 (429px-768px)</strong>: 保持水平排列但调整间距</li>
                <li><strong>移动端 (≤428px)</strong>: 标题居中，导航项水平排列但可换行</li>
                <li><strong>小屏手机 (≤375px)</strong>: 进一步优化间距和字体大小</li>
            </ul>
        </div>

        <div class="test-section">
            <h3 class="text-xl font-bold mb-4">预期行为</h3>
            <ul class="list-disc list-inside space-y-2">
                <li>导航项不应重叠或溢出容器</li>
                <li>语言选择器在所有设备上都应易于点击</li>
                <li>在移动端，标题应居中显示</li>
                <li>导航项间距应根据屏幕大小适当调整</li>
                <li>字体大小应在小屏幕上适当缩小</li>
            </ul>
        </div>
    </main>

    <script>
        // 实时显示屏幕宽度和断点信息
        function updateScreenInfo() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width;
            
            const breakpointInfo = document.getElementById('breakpoint-info');
            breakpointInfo.className = 'current-breakpoint';
            
            if (width > 768) {
                breakpointInfo.classList.add('desktop');
            } else if (width >= 429) {
                breakpointInfo.classList.add('tablet');
            } else if (width > 375) {
                breakpointInfo.classList.add('mobile');
            } else {
                breakpointInfo.classList.add('small-mobile');
            }
        }
        
        // 初始化和监听窗口大小变化
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
