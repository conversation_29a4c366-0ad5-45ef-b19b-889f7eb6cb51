<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高分辨率屏幕适配测试 - 临时邮箱</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="../static/css/high-resolution.css">
    <style>
        /* 测试页面特定样式 */
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .resolution-indicator {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
            text-align: center;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h4 {
            color: #3b82f6;
            margin-bottom: 10px;
        }
        
        /* 模拟邮件数据 */
        .demo-email {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 8px 0;
            border-left: 3px solid #3b82f6;
        }
        
        .demo-email .sender {
            font-weight: bold;
            color: #1f2937;
        }
        
        .demo-email .subject {
            color: #4b5563;
            margin: 4px 0;
        }
        
        .demo-email .time {
            color: #6b7280;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 分辨率信息显示 -->
    <div class="test-info">
        <div><strong>屏幕分辨率:</strong> <span id="screen-resolution"></span></div>
        <div><strong>窗口大小:</strong> <span id="window-size"></span></div>
        <div><strong>设备像素比:</strong> <span id="device-pixel-ratio"></span></div>
        <div><strong>当前断点:</strong> <span id="current-breakpoint"></span></div>
    </div>

    <header class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-4">
            <div class="header-content flex justify-between items-center">
                <div class="header-brand flex items-center">
                    <i class="fas fa-envelope text-2xl mr-2"></i>
                    <h1 class="text-xl font-bold">临时邮箱 - 高分辨率测试</h1>
                </div>
                <nav class="header-nav">
                    <ul class="nav-list flex space-x-6 items-center">
                        <li class="nav-item"><a href="#" class="nav-link hover:underline">首页</a></li>
                        <li class="nav-item"><a href="#" class="nav-link hover:underline">常见问题</a></li>
                        <li class="nav-item"><a href="#" class="nav-link hover:underline">联系我们</a></li>
                        <li class="nav-item language-selector-item relative">
                            <select class="language-selector bg-blue-500 text-white border border-blue-400 rounded px-2 py-1 text-sm">
                                <option value="zh-CN">中文</option>
                                <option value="en-US">English</option>
                            </select>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">高分辨率屏幕适配测试</h2>
            
            <div class="resolution-indicator" id="resolution-status">
                正在检测屏幕分辨率...
            </div>

            <div class="test-section">
                <h4>邮箱地址显示测试</h4>
                <div class="email-box bg-gray-100 p-4 rounded-lg mb-4">
                    <span class="text-blue-600 font-bold break-all"><EMAIL></span>
                </div>
            </div>

            <div class="test-section">
                <h4>按钮组响应式测试</h4>
                <div class="button-group">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-copy mr-2"></i><span>复制</span>
                    </button>
                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-sync-alt mr-2"></i><span>刷新</span>
                    </button>
                    <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i><span>新邮箱</span>
                    </button>
                    <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-edit mr-2"></i><span>自定义</span>
                    </button>
                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-history mr-2"></i><span>历史记录</span>
                    </button>
                </div>
            </div>

            <div class="test-section">
                <h4>收件箱布局测试</h4>
                <div class="border-t pt-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold text-gray-800">收件箱</h3>
                        <button class="close-btn text-gray-500 hover:text-gray-700 px-3 py-1 rounded transition-colors">
                            <i class="fas fa-times mr-1"></i><span>关闭</span>
                        </button>
                    </div>

                    <div class="inbox-section two-column-mode">
                        <!-- 邮件列表 -->
                        <div class="message-list">
                            <div class="demo-email">
                                <div class="sender">测试发件人 1</div>
                                <div class="subject">这是一封测试邮件的主题</div>
                                <div class="time">2分钟前</div>
                            </div>
                            <div class="demo-email">
                                <div class="sender">测试发件人 2</div>
                                <div class="subject">高分辨率显示器适配测试邮件</div>
                                <div class="time">5分钟前</div>
                            </div>
                            <div class="demo-email">
                                <div class="sender">测试发件人 3</div>
                                <div class="subject">响应式设计验证邮件内容</div>
                                <div class="time">10分钟前</div>
                            </div>
                        </div>

                        <!-- 邮件内容区域 -->
                        <div class="message-content-area">
                            <div class="bg-white p-6 rounded-lg border">
                                <h4 class="text-lg font-semibold mb-4">邮件内容预览</h4>
                                <div class="space-y-4">
                                    <div><strong>发件人:</strong> <EMAIL></div>
                                    <div><strong>主题:</strong> 高分辨率显示器适配测试</div>
                                    <div><strong>时间:</strong> 2025-01-27 10:30:00</div>
                                    <div class="border-t pt-4">
                                        <p>这是一封用于测试高分辨率屏幕适配效果的邮件内容。</p>
                                        <p>在不同分辨率的显示器上，字体大小、间距和布局应该都能保持良好的可读性和美观性。</p>
                                        <p>特别是在1080p、2K、4K以及Mac M1等高DPI显示器上的显示效果。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h4>字体和间距测试</h4>
                <div class="space-y-4">
                    <h1>H1 标题测试 - 主标题</h1>
                    <h2>H2 标题测试 - 副标题</h2>
                    <h3>H3 标题测试 - 小标题</h3>
                    <p>这是正文段落测试。在高分辨率屏幕上，文字应该清晰易读，行高适中，不会过于拥挤或稀疏。</p>
                    <p class="text-sm">这是小号文字测试。即使在高分辨率屏幕上，小号文字也应该保持良好的可读性。</p>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h3 class="text-lg font-semibold mb-2">高分辨率适配测试页面</h3>
                <p class="text-gray-400">测试不同分辨率下的显示效果</p>
            </div>
        </div>
    </footer>

    <script>
        // 实时显示屏幕信息
        function updateScreenInfo() {
            document.getElementById('screen-resolution').textContent = 
                `${screen.width} × ${screen.height}`;
            document.getElementById('window-size').textContent = 
                `${window.innerWidth} × ${window.innerHeight}`;
            document.getElementById('device-pixel-ratio').textContent = 
                window.devicePixelRatio || 1;
            
            // 判断当前断点
            const width = window.innerWidth;
            let breakpoint = '';
            let status = '';
            
            if (width >= 3840) {
                breakpoint = '4K+ (≥3840px)';
                status = '🖥️ 4K超高分辨率显示器适配';
            } else if (width >= 2560) {
                breakpoint = '2K (2560-3839px)';
                status = '🖥️ 2K高分辨率显示器适配';
            } else if (width >= 1920) {
                breakpoint = '1080p (1920-2559px)';
                status = '🖥️ 1080p标准桌面显示器适配';
            } else if (width >= 1025) {
                breakpoint = '桌面端 (1025-1919px)';
                status = '💻 标准桌面端显示';
            } else if (width >= 769) {
                breakpoint = '平板端 (769-1024px)';
                status = '📱 平板端显示';
            } else {
                breakpoint = '移动端 (≤768px)';
                status = '📱 移动端显示';
            }
            
            document.getElementById('current-breakpoint').textContent = breakpoint;
            document.getElementById('resolution-status').textContent = status;
        }
        
        // 初始化和监听窗口大小变化
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // 高DPI检测
        if (window.devicePixelRatio > 1) {
            document.querySelector('.test-info').innerHTML += 
                '<div style="color: #10b981; margin-top: 8px;"><strong>✓ 高DPI显示器</strong></div>';
        }
    </script>
</body>
</html>
