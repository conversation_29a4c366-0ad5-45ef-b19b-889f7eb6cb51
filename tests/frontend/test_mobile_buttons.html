<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端按钮适配测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="static/css/styles.css">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }

        .device-info {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }

        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }

        @media (max-width: 640px) {
            .before-after {
                grid-template-columns: 1fr;
            }
        }

        .demo-section {
            padding: 1rem;
            border: 1px dashed #d1d5db;
            border-radius: 6px;
        }

        .demo-section h4 {
            margin: 0 0 1rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
        }

        /* 原始按钮样式（用于对比） */
        .original-buttons button {
            background: #3b82f6 !important;
            color: white !important;
            border: none !important;
        }

        .original-buttons button:hover {
            background: #2563eb !important;
        }

        .original-buttons button.bg-green-600 {
            background: #059669 !important;
        }

        .original-buttons button.bg-green-600:hover {
            background: #047857 !important;
        }

        .original-buttons button.bg-purple-600 {
            background: #7c3aed !important;
        }

        .original-buttons button.bg-purple-600:hover {
            background: #6d28d9 !important;
        }

        .original-buttons button.bg-red-500 {
            background: #ef4444 !important;
        }

        .original-buttons button.bg-red-500:hover {
            background: #dc2626 !important;
        }

        .original-buttons button.bg-gray-200 {
            background: #e5e7eb !important;
            color: #374151 !important;
        }

        .original-buttons button.bg-gray-200:hover {
            background: #d1d5db !important;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-4">
    <div class="max-w-4xl mx-auto">
        <header class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">移动端按钮适配测试</h1>
            <p class="text-gray-600">测试无背景图标按钮风格和横向布局</p>
        </header>

        <!-- 设备信息 -->
        <div class="device-info">
            <div><strong>屏幕宽度:</strong> <span id="screen-width"></span>px</div>
            <div><strong>设备像素比:</strong> <span id="device-ratio"></span></div>
            <div><strong>触摸支持:</strong> <span id="touch-support"></span></div>
            <div><strong>用户代理:</strong> <span id="user-agent"></span></div>
        </div>

        <!-- 对比测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">样式对比测试</h2>

            <div class="before-after">
                <!-- 原始样式 -->
                <div class="demo-section original-buttons">
                    <h4>原始样式（有背景色）</h4>
                    <div class="button-group">
                        <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-sync-alt mr-2"></i>刷新
                        </button>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-plus mr-2"></i>新邮箱
                        </button>
                        <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-edit mr-2"></i>自定义
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-history mr-2"></i>历史
                        </button>
                        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-trash mr-2"></i>删除
                        </button>
                    </div>
                </div>

                <!-- 新样式 -->
                <div class="demo-section">
                    <h4>新样式（垂直布局：图标在上，文字在下）</h4>
                    <div class="button-group">
                        <button id="copy-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-copy"></i><span>复制</span>
                        </button>
                        <button id="refresh-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-sync-alt"></i><span>刷新</span>
                        </button>
                        <button id="new-email-btn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-plus"></i><span>新邮箱</span>
                        </button>
                        <button id="custom-email-btn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-edit"></i><span>自定义</span>
                        </button>
                        <button id="history-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-history"></i><span>历史</span>
                        </button>
                        <button id="delete-email-btn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-trash"></i><span>删除</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新布局演示 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">新布局演示</h2>
            <p class="text-sm text-gray-600 mb-4">复制按钮已移入按钮组，邮箱地址独立显示</p>

            <div class="demo-section">
                <h4>完整布局演示</h4>
                <!-- 邮箱地址显示区域 -->
                <div class="email-box bg-gray-100 p-4 rounded-lg mb-4 long-press-hint haptic-feedback">
                    <span class="text-blue-600 font-bold break-all"><EMAIL></span>
                </div>

                <!-- 操作按钮组 -->
                <div class="button-group">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-copy"></i><span>复制</span>
                    </button>
                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-sync-alt"></i><span>刷新</span>
                    </button>
                    <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-plus"></i><span>新邮箱</span>
                    </button>
                    <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-edit"></i><span>自定义</span>
                    </button>
                    <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-history"></i><span>历史</span>
                    </button>
                    <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-trash"></i><span>删除</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 响应式测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">响应式布局测试</h2>
            <p class="text-sm text-gray-600 mb-4">调整浏览器窗口大小或使用开发者工具测试不同屏幕尺寸</p>

            <div class="space-y-4">
                <div class="p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">≤375px (小型手机)</h4>
                    <p class="text-sm text-blue-600">垂直布局：图标在上，文字在下，横向滚动，最小宽度70px，高度60px</p>
                </div>

                <div class="p-4 bg-green-50 rounded-lg">
                    <h4 class="font-semibold text-green-800 mb-2">376px-428px (标准手机)</h4>
                    <p class="text-sm text-green-600">垂直布局：图标在上，文字在下，横向排列换行，最小宽度80px，高度65px</p>
                </div>

                <div class="p-4 bg-purple-50 rounded-lg">
                    <h4 class="font-semibold text-purple-800 mb-2">429px-768px (小型平板)</h4>
                    <p class="text-sm text-purple-600">垂直布局：图标在上，文字在下，横向排列，最小宽度90px，高度70px</p>
                </div>

                <div class="p-4 bg-gray-50 rounded-lg">
                    <h4 class="font-semibold text-gray-800 mb-2">≥769px (桌面端)</h4>
                    <p class="text-sm text-gray-600">水平布局：图标在左，文字在右，保持原有背景色样式</p>
                </div>
            </div>
        </div>

        <!-- 布局对比演示 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">布局对比演示</h2>
            <p class="text-sm text-gray-600 mb-4">展示移动端垂直布局与桌面端水平布局的区别</p>

            <div class="before-after">
                <!-- 桌面端样式（水平布局） -->
                <div class="demo-section">
                    <h4>桌面端样式（水平布局）</h4>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; border: none; display: flex; align-items: center; min-height: 44px;">
                            <i class="fas fa-copy" style="margin-right: 0.5rem;"></i><span>复制</span>
                        </button>
                        <button style="background: #e5e7eb; color: #374151; padding: 0.5rem 1rem; border-radius: 0.5rem; border: none; display: flex; align-items: center; min-height: 44px;">
                            <i class="fas fa-sync-alt" style="margin-right: 0.5rem;"></i><span>刷新</span>
                        </button>
                        <button style="background: #059669; color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; border: none; display: flex; align-items: center; min-height: 44px;">
                            <i class="fas fa-plus" style="margin-right: 0.5rem;"></i><span>新邮箱</span>
                        </button>
                    </div>
                </div>

                <!-- 移动端样式（垂直布局） -->
                <div class="demo-section">
                    <h4>移动端样式（垂直布局）</h4>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button style="background: transparent; color: #6b7280; padding: 0.75rem 0.5rem; border-radius: 0.5rem; border: none; display: flex; flex-direction: column; align-items: center; justify-content: center; min-width: 80px; min-height: 65px;">
                            <i class="fas fa-copy" style="font-size: 1.1em; margin-bottom: 0.25rem; opacity: 0.8;"></i><span style="font-size: 0.75rem; text-align: center;">复制</span>
                        </button>
                        <button style="background: transparent; color: #6b7280; padding: 0.75rem 0.5rem; border-radius: 0.5rem; border: none; display: flex; flex-direction: column; align-items: center; justify-content: center; min-width: 80px; min-height: 65px;">
                            <i class="fas fa-sync-alt" style="font-size: 1.1em; margin-bottom: 0.25rem; opacity: 0.8;"></i><span style="font-size: 0.75rem; text-align: center;">刷新</span>
                        </button>
                        <button style="background: transparent; color: #6b7280; padding: 0.75rem 0.5rem; border-radius: 0.5rem; border: none; display: flex; flex-direction: column; align-items: center; justify-content: center; min-width: 80px; min-height: 65px;">
                            <i class="fas fa-plus" style="font-size: 1.1em; margin-bottom: 0.25rem; opacity: 0.8;"></i><span style="font-size: 0.75rem; text-align: center;">新邮箱</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交互测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">交互功能测试</h2>

            <div class="space-y-4">
                <div class="p-4 bg-yellow-50 rounded-lg">
                    <h4 class="font-semibold text-yellow-800 mb-2">触摸反馈</h4>
                    <p class="text-sm text-yellow-600">点击按钮时应有轻微震动反馈（支持的设备）</p>
                </div>

                <div class="p-4 bg-indigo-50 rounded-lg">
                    <h4 class="font-semibold text-indigo-800 mb-2">视觉反馈</h4>
                    <p class="text-sm text-indigo-600">按钮按下时有缩放效果和颜色变化</p>
                </div>

                <div class="p-4 bg-pink-50 rounded-lg">
                    <h4 class="font-semibold text-pink-800 mb-2">滚动优化</h4>
                    <p class="text-sm text-pink-600">小屏幕下支持平滑的横向滚动</p>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">功能测试</h2>
            <div class="space-y-2">
                <button onclick="testHapticFeedback()" class="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                    测试触觉反馈
                </button>
                <button onclick="testScrollIndicator()" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                    测试滚动指示器
                </button>
                <button onclick="simulateResize()" class="w-full bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600">
                    模拟窗口大小变化
                </button>
            </div>
        </div>
    </div>

    <script src="static/js/mobile.js"></script>
    <script>
        // 更新设备信息
        function updateDeviceInfo() {
            document.getElementById('screen-width').textContent = window.innerWidth;
            document.getElementById('device-ratio').textContent = window.devicePixelRatio || 1;
            document.getElementById('touch-support').textContent = 'ontouchstart' in window ? '是' : '否';
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 80) + '...';
        }

        // 测试触觉反馈
        function testHapticFeedback() {
            if ('vibrate' in navigator) {
                navigator.vibrate([50, 100, 50]);
                alert('触觉反馈已触发');
            } else {
                alert('此设备不支持触觉反馈');
            }
        }

        // 测试滚动指示器
        function testScrollIndicator() {
            const buttonGroup = document.querySelector('.button-group');
            if (buttonGroup && window.mobileEnhancement) {
                window.mobileEnhancement.setupScrollIndicator(buttonGroup);
                alert('滚动指示器已更新');
            }
        }

        // 模拟窗口大小变化
        function simulateResize() {
            window.dispatchEvent(new Event('resize'));
            updateDeviceInfo();
            alert('已触发窗口大小变化事件');
        }

        // 初始化
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);

        console.log('移动端按钮测试页面已加载');
    </script>
</body>
</html>
