#!/usr/bin/env python3
"""
测试用户邮箱隔离功能
验证不同用户可以创建相同前缀的邮箱地址

环境变量配置:
- TEST_BASE_URL: 指定测试服务器地址 (默认: http://localhost:5000)

使用示例:
- 开发环境: python3 test_user_isolation.py
- 生产环境: TEST_BASE_URL=https://your-domain.com python3 test_user_isolation.py
"""

import requests
import json
import time
import sys
import os

# 测试配置
BASE_URL = 'https://kuroneko.lol'
TEST_PREFIX = "test123"

def test_user_isolation():
    """测试用户邮箱隔离功能"""
    print("开始测试用户邮箱隔离功能...")

    # 模拟两个不同的用户会话
    session_a = f"test_session_a_{int(time.time())}"
    session_b = f"test_session_b_{int(time.time())}"

    print(f"用户A会话ID: {session_a}")
    print(f"用户B会话ID: {session_b}")

    # 测试1: 用户A创建邮箱
    print("\n=== 测试1: 用户A创建邮箱 ===")
    response_a = create_email(session_a, TEST_PREFIX)
    if response_a and response_a.get('success'):
        email_a = response_a['data']['address']
        print(f"✓ 用户A成功创建邮箱: {email_a}")
    else:
        print(f"✗ 用户A创建邮箱失败: {response_a}")
        return False

    # 测试2: 用户B创建相同前缀的邮箱
    print("\n=== 测试2: 用户B创建相同前缀的邮箱 ===")
    response_b = create_email(session_b, TEST_PREFIX)
    if response_b and response_b.get('success'):
        email_b = response_b['data']['address']
        print(f"✓ 用户B成功创建邮箱: {email_b}")

        # 验证两个邮箱地址相同
        if email_a == email_b:
            print(f"✓ 验证通过: 两个用户创建了相同的邮箱地址 {email_a}")
        else:
            print(f"✗ 验证失败: 邮箱地址不同 - A: {email_a}, B: {email_b}")
            return False
    else:
        print(f"✗ 用户B创建邮箱失败: {response_b}")
        return False

    # 测试3: 用户A尝试再次创建相同前缀的邮箱（应该失败并返回建议）
    print("\n=== 测试3: 用户A尝试再次创建相同前缀的邮箱 ===")
    response_a2 = create_email(session_a, TEST_PREFIX)
    if response_a2 and not response_a2.get('success') and response_a2.get('error_code') == 'EMAIL_ALREADY_EXISTS':
        suggestions = response_a2.get('data', {}).get('suggestions', [])
        print(f"✓ 用户A重复创建失败，返回建议: {suggestions}")
    else:
        print(f"✗ 用户A重复创建测试失败: {response_a2}")
        return False

    # 测试4: 用户B尝试再次创建相同前缀的邮箱（应该失败并返回建议）
    print("\n=== 测试4: 用户B尝试再次创建相同前缀的邮箱 ===")
    response_b2 = create_email(session_b, TEST_PREFIX)
    if response_b2 and not response_b2.get('success') and response_b2.get('error_code') == 'EMAIL_ALREADY_EXISTS':
        suggestions = response_b2.get('data', {}).get('suggestions', [])
        print(f"✓ 用户B重复创建失败，返回建议: {suggestions}")
    else:
        print(f"✗ 用户B重复创建测试失败: {response_b2}")
        return False

    # 测试5: 验证历史记录隔离
    print("\n=== 测试5: 验证历史记录隔离 ===")
    history_a = get_email_history(session_a)
    history_b = get_email_history(session_b)

    if history_a and history_b:
        emails_a = [item['email_address'] for item in history_a.get('data', {}).get('history', [])]
        emails_b = [item['email_address'] for item in history_b.get('data', {}).get('history', [])]

        print(f"用户A的历史记录: {emails_a}")
        print(f"用户B的历史记录: {emails_b}")

        if email_a in emails_a and email_a not in emails_b:
            print("✗ 历史记录隔离失败: 用户B能看到用户A的邮箱")
            return False
        elif email_b in emails_b and email_b not in emails_a:
            print("✗ 历史记录隔离失败: 用户A能看到用户B的邮箱")
            return False
        else:
            print("✓ 历史记录隔离正常")

    print("\n=== 所有测试通过! ===")
    return True

def create_email(session_id, custom_prefix=None):
    """创建邮箱"""
    url = f"{BASE_URL}/api/generate-address"
    data = {"session_id": session_id}
    if custom_prefix:
        data["custom_prefix"] = custom_prefix

    try:
        response = requests.post(url, json=data, timeout=10)
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def get_email_history(session_id):
    """获取邮箱历史记录"""
    url = f"{BASE_URL}/api/email-history"
    data = {"session_id": session_id}

    try:
        response = requests.post(url, json=data, timeout=10)
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return None

def check_server():
    """检查服务器是否运行"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("用户邮箱隔离功能测试")
    print("=" * 50)
    print(f"测试目标服务器: {BASE_URL}")
    print("提示: 可通过环境变量 TEST_BASE_URL 指定测试服务器地址")
    print("=" * 50)

    # 检查服务器状态
    if not check_server():
        print(f"错误: 无法连接到服务器 {BASE_URL}")
        print("请确保应用正在运行，或设置正确的 TEST_BASE_URL 环境变量")
        print("示例: TEST_BASE_URL=https://your-domain.com python3 test_user_isolation.py")
        sys.exit(1)

    # 运行测试
    success = test_user_isolation()

    if success:
        print("\n🎉 所有测试通过！用户邮箱隔离功能正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！请检查实现。")
        sys.exit(1)
