# 环境自适应测试系统 - 快速开始指南

## 🚀 立即开始

### 1. 验证环境配置
```bash
# 检查测试环境是否正确配置
python tests/utils/test_environment_validator.py
```

### 2. 运行快速测试
```bash
# 运行所有快速测试（排除慢速和压力测试）
python tests/run_adaptive_tests.py --type fast

# 或者使用传统方式
pytest -m "not slow and not stress"
```

### 3. 查看可用选项
```bash
# 显示所有测试类型
python tests/run_adaptive_tests.py --list-types

# 显示环境信息
python tests/run_adaptive_tests.py --env-info
```

## 📋 常用命令

### 基础测试运行
```bash
# 运行所有测试
python tests/run_adaptive_tests.py

# 运行单元测试
python tests/run_adaptive_tests.py --type unit

# 运行API测试
python tests/run_adaptive_tests.py --type api

# 运行集成测试
python tests/run_adaptive_tests.py --type integration
```

### 环境特定测试
```bash
# 只运行不需要外部依赖的测试
python tests/run_adaptive_tests.py --type no_external_deps

# 只运行需要服务器的测试
python tests/run_adaptive_tests.py --type requires_server

# 跳过环境验证直接运行
python tests/run_adaptive_tests.py --no-validate
```

### 高级用法
```bash
# 传递额外的pytest参数
python tests/run_adaptive_tests.py --type api -- --maxfail=1 --tb=short

# 生成覆盖率报告
python tests/run_adaptive_tests.py -- --cov=app --cov-report=html

# 运行特定测试文件
python tests/run_adaptive_tests.py -- tests/test_api.py -v
```

## 🔧 环境配置

### 开发环境
```bash
export TEST_BASE_URL=http://localhost:5000
export TEST_SKIP_SLOW_TESTS=false
```

### CI/CD环境
```bash
export CI=true
export TEST_SKIP_EXTERNAL_DEPS=true
export TEST_SKIP_SLOW_TESTS=true
export TEST_API_TIMEOUT=60
```

### 生产环境测试
```bash
export FLASK_ENV=production
export TEST_SKIP_DESTRUCTIVE_TESTS=true
export TEST_SKIP_PERFORMANCE_TESTS=true
```

## 🎯 测试类型说明

| 类型 | 描述 | 适用场景 |
|------|------|----------|
| `all` | 运行所有测试 | 完整验证 |
| `fast` | 快速测试 | 日常开发 |
| `unit` | 单元测试 | 代码逻辑验证 |
| `integration` | 集成测试 | 组件交互验证 |
| `api` | API测试 | 接口功能验证 |
| `performance` | 性能测试 | 性能基准测试 |
| `stress` | 压力测试 | 负载能力测试 |
| `frontend` | 前端测试 | 用户界面验证 |

## ⚠️ 常见问题

### 1. 服务器连接失败
```bash
# 问题：测试服务器不可用
# 解决：检查服务器状态或跳过相关测试
python tests/run_adaptive_tests.py --type no_external_deps
```

### 2. 数据库连接问题
```bash
# 问题：数据库文件不存在
# 解决：检查数据库目录权限
mkdir -p tests/database/
chmod 755 tests/database/
```

### 3. 依赖模块缺失
```bash
# 问题：缺少可选依赖
# 解决：安装缺失的模块
pip install psutil coverage
```

### 4. 环境变量未设置
```bash
# 问题：环境变量配置不正确
# 解决：使用环境验证工具检查
python tests/utils/test_environment_validator.py
```

## 🔍 调试技巧

### 1. 详细输出
```bash
# 显示详细的测试输出
python tests/run_adaptive_tests.py --type unit -- -v -s
```

### 2. 失败时停止
```bash
# 遇到第一个失败就停止
python tests/run_adaptive_tests.py -- -x
```

### 3. 只运行失败的测试
```bash
# 重新运行上次失败的测试
python tests/run_adaptive_tests.py -- --lf
```

### 4. 调试特定测试
```bash
# 运行单个测试方法
python tests/run_adaptive_tests.py -- tests/test_api.py::TestEmailGeneration::test_generate_address_success -v
```

## 📊 测试报告

### 生成HTML覆盖率报告
```bash
python tests/run_adaptive_tests.py -- --cov=app --cov-report=html
# 报告位置：htmlcov/index.html
```

### 生成JUnit XML报告
```bash
python tests/run_adaptive_tests.py -- --junit-xml=test-results.xml
```

### 生成测试时间报告
```bash
python tests/run_adaptive_tests.py -- --durations=10
```

## 🔄 持续集成

### GitHub Actions 示例
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Run tests
      env:
        CI: true
      run: python tests/run_adaptive_tests.py --type fast
```

### GitLab CI 示例
```yaml
test:
  stage: test
  script:
    - pip install -r requirements.txt
    - python tests/run_adaptive_tests.py --type fast
  variables:
    CI: "true"
    TEST_SKIP_SLOW_TESTS: "true"
```

## 📝 最佳实践

1. **日常开发**: 使用 `--type fast` 进行快速验证
2. **提交前**: 运行 `--type all` 进行完整测试
3. **CI/CD**: 使用环境变量控制测试范围
4. **生产部署**: 运行 `--type integration` 验证核心功能
5. **性能监控**: 定期运行 `--type performance` 检查性能

## 🆘 获取帮助

```bash
# 查看帮助信息
python tests/run_adaptive_tests.py --help

# 查看pytest帮助
python tests/run_adaptive_tests.py -- --help

# 验证环境配置
python tests/utils/test_environment_validator.py
```

---

**快速开始完成！** 🎉

现在你已经掌握了环境自适应测试系统的基本使用方法。根据你的具体需求选择合适的测试类型和配置选项。
