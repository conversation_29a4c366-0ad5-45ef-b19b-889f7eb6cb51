{"devDependencies": {"@babel/preset-env": "^7.27.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^3.4.0"}, "name": "temp-email-mvp", "version": "1.0.0", "description": "这是一个基于 Python Flask (后端), Vanilla JavaScript (前端), SQLite (数据库) 和 Postfix (邮件接收) 的轻量级临时邮箱服务MVP实现。", "main": "index.js", "directories": {"doc": "docs", "test": "tests"}, "dependencies": {"abab": "^2.0.6", "acorn": "^8.14.1", "acorn-globals": "^7.0.1", "acorn-walk": "^8.3.4", "agent-base": "^6.0.2", "ansi-escapes": "^4.3.2", "ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "anymatch": "^3.1.3", "argparse": "^1.0.10", "asynckit": "^0.4.0", "babel-jest": "^29.7.0", "babel-plugin-istanbul": "^6.1.1", "babel-plugin-jest-hoist": "^29.6.3", "babel-plugin-polyfill-corejs2": "^0.4.13", "babel-plugin-polyfill-corejs3": "^0.11.1", "babel-plugin-polyfill-regenerator": "^0.6.4", "babel-preset-current-node-syntax": "^1.1.0", "babel-preset-jest": "^29.6.3", "balanced-match": "^1.0.2", "brace-expansion": "^1.1.11", "braces": "^3.0.3", "browserslist": "^4.24.5", "bser": "^2.1.1", "buffer-from": "^1.1.2", "call-bind-apply-helpers": "^1.0.2", "callsites": "^3.1.0", "camelcase": "^5.3.1", "caniuse-lite": "^1.0.30001718", "chalk": "^4.1.2", "char-regex": "^1.0.2", "ci-info": "^3.9.0", "cjs-module-lexer": "^1.4.3", "cliui": "^8.0.1", "co": "^4.6.0", "collect-v8-coverage": "^1.0.2", "color-convert": "^2.0.1", "color-name": "^1.1.4", "combined-stream": "^1.0.8", "concat-map": "^0.0.1", "convert-source-map": "^2.0.0", "core-js-compat": "^3.42.0", "create-jest": "^29.7.0", "cross-spawn": "^7.0.6", "cssom": "^0.5.0", "cssstyle": "^2.3.0", "data-urls": "^3.0.2", "debug": "^4.4.1", "decimal.js": "^10.5.0", "dedent": "^1.6.0", "deepmerge": "^4.3.1", "delayed-stream": "^1.0.0", "detect-newline": "^3.1.0", "diff-sequences": "^29.6.3", "domexception": "^4.0.0", "dunder-proto": "^1.0.1", "electron-to-chromium": "^1.5.155", "emittery": "^0.13.1", "emoji-regex": "^8.0.0", "entities": "^6.0.0", "error-ex": "^1.3.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "escalade": "^3.2.0", "escape-string-regexp": "^2.0.0", "escodegen": "^2.1.0", "esprima": "^4.0.1", "estraverse": "^5.3.0", "esutils": "^2.0.3", "execa": "^5.1.1", "exit": "^0.1.2", "expect": "^29.7.0", "fast-json-stable-stringify": "^2.1.0", "fb-watchman": "^2.0.2", "fill-range": "^7.1.1", "find-up": "^4.1.0", "form-data": "^4.0.2", "fs.realpath": "^1.0.0", "function-bind": "^1.1.2", "gensync": "^1.0.0-beta.2", "get-caller-file": "^2.0.5", "get-intrinsic": "^1.3.0", "get-package-type": "^0.1.0", "get-proto": "^1.0.1", "get-stream": "^6.0.1", "glob": "^7.2.3", "globals": "^11.12.0", "gopd": "^1.2.0", "graceful-fs": "^4.2.11", "has-flag": "^4.0.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "html-encoding-sniffer": "^3.0.0", "html-escaper": "^2.0.2", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "human-signals": "^2.1.0", "iconv-lite": "^0.6.3", "import-local": "^3.2.0", "imurmurhash": "^0.1.4", "inflight": "^1.0.6", "inherits": "^2.0.4", "is-arrayish": "^0.2.1", "is-core-module": "^2.16.1", "is-fullwidth-code-point": "^3.0.0", "is-generator-fn": "^2.1.0", "is-number": "^7.0.0", "is-potential-custom-element-name": "^1.0.1", "is-stream": "^2.0.1", "isexe": "^2.0.0", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-instrument": "^6.0.3", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.7", "jest-changed-files": "^29.7.0", "jest-circus": "^29.7.0", "jest-cli": "^29.7.0", "jest-config": "^29.7.0", "jest-diff": "^29.7.0", "jest-docblock": "^29.7.0", "jest-each": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-pnp-resolver": "^1.2.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "js-tokens": "^4.0.0", "js-yaml": "^3.14.1", "jsdom": "^20.0.3", "jsesc": "^3.1.0", "json-parse-even-better-errors": "^2.3.1", "json5": "^2.2.3", "kleur": "^3.0.3", "leven": "^3.1.0", "lines-and-columns": "^1.2.4", "locate-path": "^5.0.0", "lodash.debounce": "^4.0.8", "lru-cache": "^5.1.1", "make-dir": "^4.0.0", "makeerror": "^1.0.12", "math-intrinsics": "^1.1.0", "merge-stream": "^2.0.0", "micromatch": "^4.0.8", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mimic-fn": "^2.1.0", "minimatch": "^3.1.2", "ms": "^2.1.3", "natural-compare": "^1.4.0", "node-int64": "^0.4.0", "node-releases": "^2.0.19", "normalize-path": "^3.0.0", "npm-run-path": "^4.0.1", "nwsapi": "^2.2.20", "once": "^1.4.0", "onetime": "^5.1.2", "p-limit": "^3.1.0", "p-locate": "^4.1.0", "p-try": "^2.2.0", "parse-json": "^5.2.0", "parse5": "^7.3.0", "path-exists": "^4.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "path-parse": "^1.0.7", "picocolors": "^1.1.1", "picomatch": "^2.3.1", "pirates": "^4.0.7", "pkg-dir": "^4.2.0", "pretty-format": "^29.7.0", "prompts": "^2.4.2", "psl": "^1.15.0", "punycode": "^2.3.1", "pure-rand": "^6.1.0", "querystringify": "^2.2.0", "react-is": "^18.3.1", "regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regexpu-core": "^6.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "require-directory": "^2.1.1", "requires-port": "^1.0.0", "resolve": "^1.22.10", "resolve-cwd": "^3.0.0", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.3", "safer-buffer": "^2.1.2", "saxes": "^6.0.0", "semver": "^6.3.1", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "signal-exit": "^3.0.7", "sisteransi": "^1.0.5", "slash": "^3.0.0", "source-map": "^0.6.1", "source-map-support": "^0.5.13", "sprintf-js": "^1.0.3", "stack-utils": "^2.0.6", "string-length": "^4.0.2", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "strip-bom": "^4.0.0", "strip-final-newline": "^2.0.0", "strip-json-comments": "^3.1.1", "supports-color": "^7.2.0", "supports-preserve-symlinks-flag": "^1.0.0", "symbol-tree": "^3.2.4", "test-exclude": "^6.0.0", "tmpl": "^1.0.5", "to-regex-range": "^5.0.1", "tough-cookie": "^4.1.4", "tr46": "^3.0.0", "type-detect": "^4.0.8", "type-fest": "^0.21.3", "undici-types": "^6.21.0", "unicode-canonical-property-names-ecmascript": "^2.0.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.2.0", "unicode-property-aliases-ecmascript": "^2.1.0", "universalify": "^0.2.0", "update-browserslist-db": "^1.1.3", "url-parse": "^1.5.10", "v8-to-istanbul": "^9.3.0", "w3c-xmlserializer": "^4.0.0", "walker": "^1.0.8", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^2.0.0", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^11.0.0", "which": "^2.0.2", "wrap-ansi": "^7.0.0", "wrappy": "^1.0.2", "write-file-atomic": "^4.0.2", "ws": "^8.18.2", "xml-name-validator": "^4.0.0", "xmlchars": "^2.2.0", "y18n": "^5.0.8", "yallist": "^3.1.1", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yocto-queue": "^0.1.0"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "build-css": "./build-css.sh", "verify-css": "./verify-build.sh", "dev": "npm run build-css && npm run verify-css"}, "jest": {"testEnvironment": "jsdom", "setupFiles": ["./tests/setup.js"]}, "repository": {"type": "git", "url": "git+https://github.com/skywalk128/tempmail.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/skywalk128/tempmail/issues"}, "homepage": "https://github.com/skywalk128/tempmail#readme"}