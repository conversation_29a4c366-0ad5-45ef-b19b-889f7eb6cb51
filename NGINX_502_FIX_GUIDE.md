# Nginx 502错误修复指南

## 🔍 问题诊断

**问题原因**: Nginx配置指向Unix socket (`/run/tempmail/tempemail.sock`)，但Gunicorn运行在TCP端口8080上。

**当前状态**:
- ✅ Gunicorn正在8080端口运行
- ❌ Nginx配置指向不存在的Unix socket
- ❌ 导致502 Bad Gateway错误

## 🔧 修复步骤

### 步骤1: 备份当前配置
```bash
sudo cp /etc/nginx/sites-available/temp_email_app /etc/nginx/sites-available/temp_email_app.backup.$(date +%Y%m%d_%H%M%S)
```

### 步骤2: 替换Nginx配置
```bash
sudo cp /var/www/tempmail/temp_nginx_config.conf /etc/nginx/sites-available/temp_email_app
```

### 步骤3: 测试配置
```bash
sudo nginx -t
```

### 步骤4: 重新加载Nginx
```bash
sudo systemctl reload nginx
```

### 步骤5: 验证修复
```bash
# 测试本地连接
curl -I http://localhost

# 测试域名连接
curl -I https://kuroneko.lol
```

## 📋 关键修改内容

**原配置** (导致502错误):
```nginx
proxy_pass http://unix:/run/tempmail/tempemail.sock;
```

**新配置** (修复502错误):
```nginx
proxy_pass http://127.0.0.1:8080;
```

## 🧪 验证命令

```bash
# 1. 检查Gunicorn状态
netstat -tlnp | grep :8080

# 2. 检查Nginx状态
sudo systemctl status nginx

# 3. 测试HTTP连接
curl -I http://localhost

# 4. 测试HTTPS连接
curl -I https://kuroneko.lol

# 5. 测试API功能
curl -X POST https://kuroneko.lol/api/generate-address \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "test"}'
```

## 🚨 故障排除

如果修复后仍有问题：

### 检查Nginx错误日志
```bash
sudo tail -f /var/log/nginx/temp_email_error.log
```

### 检查Gunicorn状态
```bash
ps aux | grep gunicorn
```

### 重启服务
```bash
# 重启Gunicorn
cd /var/www/tempmail
./scripts/start_tempmail_app.sh production

# 重启Nginx
sudo systemctl restart nginx
```

### 检查防火墙
```bash
sudo ufw status
```

## 🎯 预期结果

修复成功后：
- ✅ https://kuroneko.lol 正常访问
- ✅ http://localhost 正常访问  
- ✅ API功能正常工作
- ✅ 不再出现502错误

## 📝 配置说明

新配置的主要改进：
1. **TCP连接**: 使用 `http://127.0.0.1:8080` 替代Unix socket
2. **超时设置**: 添加连接、发送、读取超时配置
3. **保留功能**: 保持SSL、限流等原有功能
4. **缓存优化**: 为静态文件添加缓存控制
