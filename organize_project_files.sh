#!/bin/bash

# 临时邮箱项目文件整理脚本
# 整理项目根目录中的文件，创建合理的目录结构

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

PROJECT_ROOT="/var/www/tempmail"

echo -e "${BLUE}📁 临时邮箱项目文件整理${NC}"
echo "=================================================="

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 分析当前文件状态
analyze_files() {
    echo ""
    echo -e "${BLUE}📋 分析当前文件状态${NC}"
    
    cd "$PROJECT_ROOT"
    
    # 分类文件
    echo ""
    echo "🔍 诊断和修复脚本:"
    ls -la *.sh 2>/dev/null | grep -E "(diagnose|fix|check)" | awk '{print "  " $9}' || echo "  无"
    
    echo ""
    echo "⚙️ 服务配置文件:"
    ls -la *.service 2>/dev/null | awk '{print "  " $9}' || echo "  无"
    
    echo ""
    echo "📄 文档文件 (根目录):"
    ls -la *.md 2>/dev/null | awk '{print "  " $9}' || echo "  无"
    
    echo ""
    echo "🔧 管理脚本:"
    ls -la *.sh 2>/dev/null | grep -E "(manage|start|build)" | awk '{print "  " $9}' || echo "  无"
    
    echo ""
    echo "🗂️ 配置文件:"
    ls -la *.conf *.json *.py 2>/dev/null | grep -E "(config|conf|optimize)" | awk '{print "  " $9}' || echo "  无"
    
    echo ""
    echo "🗄️ 备份文件:"
    find . -maxdepth 1 -name "*.backup*" -o -name "*_backup*" | sed 's/^/  /' || echo "  无"
    
    echo ""
    echo "🧪 测试和临时文件:"
    ls -la *.json *.py 2>/dev/null | grep -E "(demo|test|temp|verify)" | awk '{print "  " $9}' || echo "  无"
}

# 创建目录结构
create_directories() {
    echo ""
    echo -e "${BLUE}📁 创建目录结构${NC}"
    
    # 创建主要目录
    directories=(
        "scripts"
        "scripts/maintenance"
        "scripts/deployment"
        "scripts/diagnostics"
        "config"
        "config/systemd"
        "config/templates"
        "backup"
        "backup/database"
        "backup/configs"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        else
            log_info "目录已存在: $dir"
        fi
    done
}

# 整理脚本文件
organize_scripts() {
    echo ""
    echo -e "${BLUE}🔧 整理脚本文件${NC}"
    
    # 诊断脚本
    if [ -f "diagnose_permissions.sh" ]; then
        mv "diagnose_permissions.sh" "scripts/diagnostics/"
        log_info "移动: diagnose_permissions.sh → scripts/diagnostics/"
    fi
    
    if [ -f "diagnose_service_failure.sh" ]; then
        mv "diagnose_service_failure.sh" "scripts/diagnostics/"
        log_info "移动: diagnose_service_failure.sh → scripts/diagnostics/"
    fi
    
    if [ -f "check_permissions.sh" ]; then
        mv "check_permissions.sh" "scripts/diagnostics/"
        log_info "移动: check_permissions.sh → scripts/diagnostics/"
    fi
    
    # 修复脚本
    if [ -f "fix_permissions.sh" ]; then
        mv "fix_permissions.sh" "scripts/maintenance/"
        log_info "移动: fix_permissions.sh → scripts/maintenance/"
    fi
    
    if [ -f "fix_monitoring_permissions.sh" ]; then
        mv "fix_monitoring_permissions.sh" "scripts/maintenance/"
        log_info "移动: fix_monitoring_permissions.sh → scripts/maintenance/"
    fi
    
    if [ -f "fix_service_issues.sh" ]; then
        mv "fix_service_issues.sh" "scripts/maintenance/"
        log_info "移动: fix_service_issues.sh → scripts/maintenance/"
    fi
    
    # 快速修复脚本（保留在根目录，但创建软链接）
    if [ -f "quick_fix_permissions.sh" ]; then
        mv "quick_fix_permissions.sh" "scripts/maintenance/"
        ln -sf "scripts/maintenance/quick_fix_permissions.sh" "quick_fix_permissions.sh"
        log_info "移动并创建软链接: quick_fix_permissions.sh"
    fi
    
    if [ -f "quick_fix_service.sh" ]; then
        mv "quick_fix_service.sh" "scripts/maintenance/"
        ln -sf "scripts/maintenance/quick_fix_service.sh" "quick_fix_service.sh"
        log_info "移动并创建软链接: quick_fix_service.sh"
    fi
    
    # 部署脚本
    if [ -f "start_production.sh" ]; then
        mv "start_production.sh" "scripts/deployment/"
        ln -sf "scripts/deployment/start_production.sh" "start_production.sh"
        log_info "移动并创建软链接: start_production.sh"
    fi
    
    # 验证脚本
    if [ -f "final_verification.sh" ]; then
        mv "final_verification.sh" "scripts/diagnostics/"
        log_info "移动: final_verification.sh → scripts/diagnostics/"
    fi
    
    # 管理脚本（保留在根目录）
    if [ -f "manage_tempmail_service.sh" ]; then
        log_info "保留在根目录: manage_tempmail_service.sh"
    fi
}

# 整理配置文件
organize_configs() {
    echo ""
    echo -e "${BLUE}⚙️ 整理配置文件${NC}"
    
    # 服务配置文件
    service_files=(
        "tempmail-optimized.service"
        "tempmail-optimized-simple.service"
        "tempmail-minimal.service"
    )
    
    # 保留最终版本，移动其他版本
    if [ -f "tempmail-minimal.service" ]; then
        cp "tempmail-minimal.service" "config/systemd/tempmail-optimized.service"
        log_info "保留最终版本: tempmail-minimal.service → config/systemd/tempmail-optimized.service"
        
        # 移动其他版本到备份
        for file in "${service_files[@]}"; do
            if [ -f "$file" ] && [ "$file" != "tempmail-minimal.service" ]; then
                mv "$file" "backup/configs/"
                log_info "备份: $file → backup/configs/"
            fi
        done
        
        # 删除原始文件，创建软链接
        rm -f "tempmail-minimal.service"
        ln -sf "config/systemd/tempmail-optimized.service" "tempmail-optimized.service"
        log_info "创建软链接: tempmail-optimized.service"
    fi
    
    # 日志轮转配置
    if [ -f "tempmail-logrotate.conf" ]; then
        mv "tempmail-logrotate.conf" "config/"
        log_info "移动: tempmail-logrotate.conf → config/"
    fi
    
    # 配置模板
    if [ -f "monitoring_config.env.example" ]; then
        mv "monitoring_config.env.example" "config/templates/"
        log_info "移动: monitoring_config.env.example → config/templates/"
    fi
    
    # Gunicorn配置（保留在根目录）
    if [ -f "gunicorn.conf.py" ]; then
        log_info "保留在根目录: gunicorn.conf.py"
    fi
}

# 整理文档文件
organize_docs() {
    echo ""
    echo -e "${BLUE}📄 整理文档文件${NC}"
    
    # 移动根目录的文档到docs目录
    doc_files=(
        "MONITORING_PERMISSIONS_FIXED.md"
        "MONITORING_PERMISSIONS_SOLUTION.md"
        "OPTIMIZATION_IMPLEMENTATION_SUMMARY.md"
        "PRODUCTION_SERVICE_DEPLOYMENT_GUIDE.md"
        "SERVICE_DEPLOYMENT_SUCCESS_REPORT.md"
    )
    
    for file in "${doc_files[@]}"; do
        if [ -f "$file" ]; then
            mv "$file" "docs/"
            log_info "移动: $file → docs/"
        fi
    done
    
    # 保留README.md在根目录
    if [ -f "README.md" ]; then
        log_info "保留在根目录: README.md"
    fi
}

# 整理备份文件
organize_backups() {
    echo ""
    echo -e "${BLUE}🗄️ 整理备份文件${NC}"
    
    # 移动数据库备份
    if [ -f "database/tempmail.db.backup_20250611_150526" ]; then
        mv "database/tempmail.db.backup_20250611_150526" "backup/database/"
        log_info "移动: 数据库备份 → backup/database/"
    fi
    
    # 移动ACL备份
    if [ -f "git_permissions_backup.acl" ]; then
        mv "git_permissions_backup.acl" "backup/configs/"
        log_info "移动: git_permissions_backup.acl → backup/configs/"
    fi
}

# 清理临时和测试文件
cleanup_temp_files() {
    echo ""
    echo -e "${BLUE}🧹 清理临时和测试文件${NC}"
    
    # 移动验证和优化脚本
    temp_files=(
        "verify_optimizations.py"
        "verify_monitoring_fix.py"
        "optimize_sqlite.py"
    )
    
    for file in "${temp_files[@]}"; do
        if [ -f "$file" ]; then
            mv "$file" "scripts/maintenance/"
            log_info "移动: $file → scripts/maintenance/"
        fi
    done
    
    # 移动演示文件
    demo_files=(
        "demo_internal_mail_monitoring.py"
        "demo_monitoring.py"
    )
    
    for file in "${demo_files[@]}"; do
        if [ -f "$file" ]; then
            mv "$file" "scripts/diagnostics/"
            log_info "移动: $file → scripts/diagnostics/"
        fi
    done
    
    # 移动结果文件
    if [ -f "optimization_verification_results.json" ]; then
        mv "optimization_verification_results.json" "backup/"
        log_info "移动: optimization_verification_results.json → backup/"
    fi
    
    if [ -f "internal_mail_demo_results.json" ]; then
        mv "internal_mail_demo_results.json" "backup/"
        log_info "移动: internal_mail_demo_results.json → backup/"
    fi
}

# 更新脚本引用
update_script_references() {
    echo ""
    echo -e "${BLUE}🔗 更新脚本引用${NC}"
    
    # 更新manage_tempmail_service.sh中的路径引用
    if [ -f "manage_tempmail_service.sh" ]; then
        # 备份原文件
        cp "manage_tempmail_service.sh" "backup/configs/manage_tempmail_service.sh.backup"
        
        # 更新路径引用（如果有的话）
        log_info "检查并更新 manage_tempmail_service.sh 中的路径引用"
    fi
}

# 创建目录说明文件
create_readme_files() {
    echo ""
    echo -e "${BLUE}📝 创建目录说明文件${NC}"
    
    # scripts目录说明
    cat > scripts/README.md << 'EOF'
# Scripts Directory

This directory contains various maintenance and utility scripts for the temporary email system.

## Directory Structure

- `diagnostics/` - Diagnostic and verification scripts
- `maintenance/` - Maintenance and repair scripts  
- `deployment/` - Deployment and production scripts

## Usage

Most scripts should be run from the project root directory to ensure correct paths.
EOF
    
    # config目录说明
    cat > config/README.md << 'EOF'
# Configuration Directory

This directory contains configuration files and templates for the temporary email system.

## Directory Structure

- `systemd/` - systemd service configuration files
- `templates/` - Configuration file templates

## Usage

Configuration files in this directory are used by the deployment scripts and services.
EOF
    
    log_info "创建目录说明文件"
}

# 显示整理结果
show_results() {
    echo ""
    echo -e "${BLUE}📊 整理结果总结${NC}"
    echo ""
    
    echo "📁 新的目录结构:"
    tree -L 2 -I 'node_modules|venv|__pycache__|*.pyc' . 2>/dev/null || find . -maxdepth 2 -type d | grep -v -E "(node_modules|venv|__pycache__)" | sort
    
    echo ""
    echo "🔗 创建的软链接:"
    find . -maxdepth 1 -type l -exec ls -la {} \;
    
    echo ""
    echo "📄 根目录剩余文件:"
    ls -la *.sh *.py *.md *.service 2>/dev/null | awk '{print "  " $9}' || echo "  无相关文件"
}

# 主函数
main() {
    log_info "开始整理项目文件..."
    log_info "项目路径: $PROJECT_ROOT"
    
    # 检查是否在正确的目录
    if [ ! -f "$PROJECT_ROOT/app.py" ]; then
        log_error "未找到app.py文件，请确认项目路径正确"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # 执行整理步骤
    analyze_files
    create_directories
    organize_scripts
    organize_configs
    organize_docs
    organize_backups
    cleanup_temp_files
    update_script_references
    create_readme_files
    show_results
    
    echo ""
    echo "=================================================="
    echo -e "${GREEN}🎉 项目文件整理完成！${NC}"
    echo ""
    echo "📋 整理摘要:"
    echo "  ✅ 创建了合理的目录结构"
    echo "  ✅ 整理了脚本文件到相应目录"
    echo "  ✅ 整理了配置文件"
    echo "  ✅ 移动了文档文件"
    echo "  ✅ 清理了临时文件"
    echo "  ✅ 创建了必要的软链接"
    echo ""
    echo "🔗 重要软链接:"
    echo "  • tempmail-optimized.service → config/systemd/tempmail-optimized.service"
    echo "  • start_production.sh → scripts/deployment/start_production.sh"
    echo "  • quick_fix_*.sh → scripts/maintenance/"
    echo ""
    echo "📁 主要目录:"
    echo "  • scripts/ - 维护和管理脚本"
    echo "  • config/ - 配置文件和模板"
    echo "  • backup/ - 备份文件"
    echo "  • docs/ - 文档文件"
}

# 运行主函数
main "$@"
